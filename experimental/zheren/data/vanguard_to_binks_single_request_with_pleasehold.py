#!/usr/bin/env python3
"""Single-request Ray-based Vanguard to Binks conversion pipeline with PleaseHold filtering.

This script uses single-request processing to avoid workspace grouping issues.
Each request is processed individually, ensuring all blobs belong to the same workspace.
"""

import argparse
import logging
from datetime import datetime
from pathlib import Path
from typing import Optional, Any
from base.datasets.tenants import DATASET_TENANTS

# Import the single-request converter components
from experimental.zheren.data.vanguard_to_binks_single_request_converter import (
    VanguardSingleRequest,
    FilteredVanguardSingleRequest,
    SingleRequestToRepositoryActor,
)

# Import PleaseHold classifier
from experimental.zheren.data.pleasehold_classifier_actor import (
    PleaseHoldClassifierActor,
    ClassificationRequest,
)

# Import Ray utilities
from research.data.ray.ray_utils import AbstractRayActor, RayRunner

# Import dataset infrastructure
from base.datasets.gcp_creds import get_gcp_creds

# Import Vanguard utilities
from experimental.zheren.data.vanguard_data_utils import (
    get_chat_request_from_gcs,
    query_chat_requests,
)

# Import official Binks schemas
from experimental.tongfei.data.binks_schemas import Repository

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class SingleRequestClassificationActor(
    AbstractRayActor[VanguardSingleRequest, FilteredVanguardSingleRequest]
):
    """Ray actor that classifies single Vanguard requests and filters for codebase-related ones.

    This actor:
    1. Takes a single Vanguard request
    2. Fetches chat request details from GCS
    3. Classifies the request using PleaseHold
    4. Returns filtered request with classification result
    """

    def __init__(
        self,
        pleasehold_checkpoint: str = "/mnt/efs/augment/checkpoints/pleasehold/pleasehold_v2_qwen14b_bsz128_ff_fp8",
        pleasehold_sha256: Optional[
            str
        ] = "301cb2137636abaee6a0dfb08c7e50d95aca9677cb33a7ccc72858c02938cad6",
        docsets: Optional[list[str]] = None,
        service_account_json: Optional[str] = None,
    ):
        super().__init__(
            input_cls=VanguardSingleRequest, output_cls=FilteredVanguardSingleRequest
        )

        # Initialize PleaseHold classifier
        self.classifier = PleaseHoldClassifierActor(
            checkpoint_path=pleasehold_checkpoint,
            checkpoint_sha256=pleasehold_sha256,
            docsets=docsets or [],
            num_gpus=1,  # Use single GPU per actor
        )

        # Store credentials
        if service_account_json:
            import google.auth
            from google.oauth2 import service_account

            self.credentials = service_account.Credentials.from_service_account_file(
                service_account_json
            )
        else:
            import google.auth

            self.credentials = google.auth.default()[0]

        logger.info("Initialized SingleRequestClassificationActor with PleaseHold")

    def process(
        self, row: VanguardSingleRequest
    ) -> list[FilteredVanguardSingleRequest]:
        """Process a single request through classification and filtering."""
        from base.datasets.tenants import get_tenant

        tenant = get_tenant(row.tenant_name)

        # Fetch chat request from GCS
        request = get_chat_request_from_gcs(
            tenant,
            row.request_row["tenant_id"],
            row.request_row["request_id"],
            self.credentials,
        )

        if not request or not request.message:
            logger.debug(f"No valid request found for {row.request_row['request_id']}")
            return []

        # Create classification request
        classification_req = ClassificationRequest(
            request_id=row.request_row["request_id"],
            message=request.message,
            tenant_name=row.tenant_name,
            blob_names=request.blob_names if request else None,
            file_paths=None,  # Will be populated later if needed
        )

        # Classify the request
        results = self.classifier.process(classification_req)

        if results and results[0].is_codebase_related:
            logger.debug(
                f"Request {row.request_row['request_id']} classified as category {results[0].category} - keeping"
            )

            return [
                FilteredVanguardSingleRequest(
                    tenant_name=row.tenant_name,
                    tenant_id=row.tenant_id,
                    request_row=row.request_row,
                    classification_category=str(results[0].category),
                    is_codebase_related=True,
                )
            ]
        else:
            logger.debug(
                f"Request {row.request_row['request_id']} not codebase-related - filtering out"
            )
            return []


class FilteredSingleRequestToRepositoryActor(
    AbstractRayActor[FilteredVanguardSingleRequest, Repository]
):
    """Ray actor that processes filtered single requests into Repository objects.

    This is a thin wrapper around SingleRequestToRepositoryActor that handles the
    filtered request format.
    """

    def __init__(
        self,
        blob_cache_gb: float = 1.0,
        checkpoint_cache_gb: float = 0.5,
        service_account_json: Optional[str] = None,
    ):
        super().__init__(input_cls=FilteredVanguardSingleRequest, output_cls=Repository)

        # Create the underlying repository actor
        self.repo_actor = SingleRequestToRepositoryActor(
            blob_cache_gb=blob_cache_gb,
            checkpoint_cache_gb=checkpoint_cache_gb,
            service_account_json=service_account_json,
        )

        logger.info("Initialized FilteredSingleRequestToRepositoryActor")

    def process(self, row: FilteredVanguardSingleRequest) -> list[Repository]:
        """Process filtered single request into repository."""
        # Convert to VanguardSingleRequest format
        single_request = VanguardSingleRequest(
            tenant_name=row.tenant_name,
            tenant_id=row.tenant_id,
            request_row=row.request_row,
        )

        # Process using the single-request actor
        repositories = self.repo_actor.process(single_request)

        # Log classification info
        if repositories:
            logger.info(
                f"Processed request {row.request_row['request_id']} "
                f"(category: {row.classification_category}) -> {len(repositories)} repositories"
            )

        return repositories


def create_single_requests(
    tenant_names: list[str],
    date_from: str,
    date_to: str,
    limit: Optional[int],
    credentials: Any,
) -> list[VanguardSingleRequest]:
    """Create single request objects from BigQuery data."""
    from base.datasets.tenants import get_tenant

    all_requests = []

    for tenant_name in tenant_names:
        tenant = get_tenant(tenant_name)

        # Query for request IDs
        request_rows = query_chat_requests(
            tenant, date_from, date_to, limit, credentials
        )

        if not request_rows:
            logger.info(f"No requests found for tenant {tenant_name}")
            continue

        logger.info(f"Found {len(request_rows)} requests for tenant {tenant_name}")

        # Create single request objects
        for row in request_rows:
            # Serialize the row to ensure it's JSON-compatible
            serializable_row = {}
            for key, value in row.items():
                if isinstance(value, datetime):
                    serializable_row[key] = value.isoformat()
                else:
                    serializable_row[key] = value

            single_request = VanguardSingleRequest(
                tenant_name=tenant_name,
                tenant_id=tenant.tenant_id,
                request_row=serializable_row,
            )
            all_requests.append(single_request)

    return all_requests


def main():
    """Main function to orchestrate the single-request Ray-based conversion with PleaseHold filtering."""
    parser = argparse.ArgumentParser(
        description="Single-request Ray-based Vanguard to Binks dataset conversion with PleaseHold filtering"
    )

    # Data selection arguments
    parser.add_argument(
        "--date-from",
        type=str,
        required=True,
        help="Start date for data collection (YYYY-MM-DD)",
    )
    parser.add_argument(
        "--date-to",
        type=str,
        required=True,
        help="End date for data collection (YYYY-MM-DD)",
    )
    parser.add_argument(
        "--output-path", type=str, required=True, help="Output path for the JSONL file"
    )
    parser.add_argument(
        "--limit",
        type=int,
        default=None,
        help="Limit number of requests per tenant (for testing)",
    )
    parser.add_argument(
        "--tenants",
        type=str,
        default=None,
        help="Comma-separated list of specific tenants to process",
    )

    # Processing configuration
    parser.add_argument(
        "--blob-cache-gb",
        type=float,
        default=1.0,
        help="Blob cache size in GB (default: 1.0)",
    )
    parser.add_argument(
        "--checkpoint-cache-gb",
        type=float,
        default=0.5,
        help="Checkpoint cache size in GB (default: 0.5)",
    )
    parser.add_argument(
        "--service-account-file",
        type=str,
        default=None,
        help="Path to service account JSON file for GCP authentication",
    )

    # PleaseHold configuration
    parser.add_argument(
        "--pleasehold-checkpoint",
        type=str,
        default="/mnt/efs/augment/checkpoints/pleasehold/pleasehold_v2_qwen14b_bsz128_ff_fp8",
        help="Path to PleaseHold model checkpoint",
    )
    parser.add_argument(
        "--pleasehold-sha256",
        type=str,
        default="301cb2137636abaee6a0dfb08c7e50d95aca9677cb33a7ccc72858c02938cad6",
        help="SHA256 hash of PleaseHold checkpoint",
    )
    parser.add_argument(
        "--docsets",
        type=str,
        default=None,
        help="Comma-separated list of docsets for PleaseHold",
    )

    # Ray configuration
    parser.add_argument(
        "--mode",
        type=str,
        default="local",
        choices=["local", "ray"],
        help="Execution mode: local (for testing) or ray (distributed)",
    )
    parser.add_argument(
        "--num-workers",
        type=int,
        default=1,
        help="Number of Ray workers (default: 1)",
    )
    parser.add_argument(
        "--num-cpu-per-worker",
        type=int,
        default=8,
        help="Number of CPUs per worker (default: 8)",
    )
    parser.add_argument(
        "--num-gpu-per-worker",
        type=int,
        default=0,
        help="Number of GPUs per worker (default: 0, set to 1 for GPU mode)",
    )
    parser.add_argument(
        "--two-stage",
        action="store_true",
        help="Use two-stage pipeline (classification then processing) instead of single stage",
    )

    args = parser.parse_args()

    # Validate dates
    try:
        datetime.strptime(args.date_from, "%Y-%m-%d")
        datetime.strptime(args.date_to, "%Y-%m-%d")
    except ValueError:
        logger.error("Invalid date format. Use YYYY-MM-DD")
        return 1

    # Set up credentials
    credentials, _ = get_gcp_creds(args.service_account_file)

    # Get tenants to process
    if args.tenants:
        tenant_names = [t.strip() for t in args.tenants.split(",")]
        # Validate tenant names against single source of truth
        invalid_tenants = [name for name in tenant_names if name not in DATASET_TENANTS]
        if invalid_tenants:
            logger.error(f"Invalid tenant names: {invalid_tenants}")
            logger.error(f"Available tenants: {sorted(DATASET_TENANTS.keys())}")
            return 1
    else:
        # Use all available tenants for training
        tenant_names = list(DATASET_TENANTS.keys())

    # Parse docsets
    docsets = None
    if args.docsets:
        docsets = [d.strip() for d in args.docsets.split(",")]

    logger.info(
        f"Processing {len(tenant_names)} tenants in {args.mode} mode with PleaseHold filtering"
    )

    # Create single request objects
    logger.info("Creating single request objects")
    single_requests = create_single_requests(
        tenant_names,
        args.date_from,
        args.date_to,
        args.limit,
        credentials,
    )
    logger.info(f"Created {len(single_requests)} single request objects for processing")

    if not single_requests:
        logger.error("No requests created - no data to process")
        return 1

    # Create temporary JSONL file with single requests
    import tempfile

    with tempfile.NamedTemporaryFile(mode="w", suffix=".jsonl", delete=False) as f:
        temp_input_path = f.name
        for request in single_requests:
            f.write(request.to_json() + "\n")

    try:
        if args.two_stage:
            # Two-stage pipeline: classify first, then process
            logger.info("Using two-stage pipeline: classification -> processing")

            # Stage 1: Classification and filtering
            # Create a temporary directory for filtered output
            import shutil

            temp_filtered_dir = tempfile.mkdtemp()

            with RayRunner(
                actor_cls=SingleRequestClassificationActor,
                actor_args={
                    "pleasehold_checkpoint": args.pleasehold_checkpoint,
                    "pleasehold_sha256": args.pleasehold_sha256,
                    "docsets": docsets,
                    "service_account_json": args.service_account_file,
                },
                num_workers=args.num_workers,
                num_cpu_per_worker=args.num_cpu_per_worker,
                num_gpu_per_worker=args.num_gpu_per_worker,
                local=args.mode == "local",
            ) as runner:
                runner.process_jsonl(temp_input_path, temp_filtered_dir)

            # Get the filtered file path
            temp_filtered_path = Path(temp_filtered_dir) / Path(temp_input_path).name

            # Stage 2: Process filtered requests
            # Create output directory if needed
            output_dir = Path(args.output_path).parent
            if output_dir and not output_dir.exists():
                output_dir.mkdir(parents=True, exist_ok=True)

            with RayRunner(
                actor_cls=FilteredSingleRequestToRepositoryActor,
                actor_args={
                    "blob_cache_gb": args.blob_cache_gb,
                    "checkpoint_cache_gb": args.checkpoint_cache_gb,
                    "service_account_json": args.service_account_file,
                },
                num_workers=args.num_workers,
                num_cpu_per_worker=args.num_cpu_per_worker,
                num_gpu_per_worker=0,  # No GPU needed for repository creation
                local=args.mode == "local",
            ) as runner:
                # For the second stage, we need to pass the actual file, not directory
                runner.process_jsonl(str(temp_filtered_path), args.output_path)

            # Clean up intermediate directory
            shutil.rmtree(temp_filtered_dir, ignore_errors=True)

        else:
            # Single-stage pipeline: combined actor
            logger.info(
                "Using single-stage pipeline with combined classification and processing"
            )
            logger.warning(
                "Single-stage pipeline not yet implemented for single-request processing - use --two-stage"
            )
            return 1

        logger.info(f"Conversion complete! Output written to {args.output_path}")

        # Count results
        if args.mode == "local":
            output_file = Path(args.output_path) / Path(temp_input_path).name
            if output_file.exists():
                with open(output_file, "r") as f:
                    repo_count = sum(1 for _ in f)
                logger.info(
                    f"Generated {repo_count} repositories from codebase-related queries"
                )

        return 0

    finally:
        # Clean up temporary file
        Path(temp_input_path).unlink(missing_ok=True)


if __name__ == "__main__":
    exit(main())
