#!/usr/bin/env python3
"""Single-request Vanguard to Binks conversion actor.

This module provides a simplified actor that processes Vanguard requests individually
instead of in batches. This eliminates the need for workspace grouping since each
request is guaranteed to be from a single workspace.
"""

import base64
import logging
import uuid
from collections import defaultdict
from dataclasses import dataclass
from pathlib import Path
from typing import Optional

import google.auth
from dataclasses_json import DataClassJsonMixin
from google.cloud import storage
from google.oauth2 import service_account

# Import Ray utilities
from research.data.ray.ray_utils import AbstractRayActor

# Import dataset infrastructure
from base.datasets.tenants import get_tenant
from base.datasets.gcs_blob_cache import GCSBlobCache, GCSCheckpointCache

# Import Vanguard utilities
from experimental.zheren.data.vanguard_data_utils import (
    get_chat_request_from_gcs,
    VanguardChatRequest,
)

# Import official Binks schemas
from experimental.tongfei.data.binks_schemas import (
    Repository,
    File,
    DocumentWithQuestionsV2,
)

# Import language detection utilities
from base.languages import guess_language

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


@dataclass
class VanguardSingleRequest(DataClassJsonMixin):
    """Input data structure for single request processing."""

    tenant_name: str
    tenant_id: str
    request_row: dict  # Single BigQuery row dict


@dataclass
class FilteredVanguardSingleRequest(DataClassJsonMixin):
    """Single Vanguard request that has been filtered by PleaseHold."""

    tenant_name: str
    tenant_id: str
    request_row: dict  # Single request row
    classification_category: str  # Classification result category
    is_codebase_related: bool  # Whether the request is codebase-related


class SingleRequestToRepositoryActor(
    AbstractRayActor[VanguardSingleRequest, Repository]
):
    """Ray actor that processes single Vanguard requests into Repository objects.

    This actor is designed to process one request at a time, eliminating the need
    for workspace grouping. Each request's blobs are guaranteed to be from the
    same workspace.
    """

    def __init__(
        self,
        blob_cache_gb: float = 1.0,
        checkpoint_cache_gb: float = 0.5,
        service_account_json: Optional[str] = None,
    ):
        super().__init__(input_cls=VanguardSingleRequest, output_cls=Repository)

        # Initialize credentials
        if service_account_json:
            self.credentials = service_account.Credentials.from_service_account_file(
                service_account_json
            )
        else:
            self.credentials = google.auth.default()[0]

        # Store cache configuration
        self.blob_cache_size_bytes = int(blob_cache_gb * 2**30)
        self.checkpoint_cache_size_bytes = int(checkpoint_cache_gb * 2**30)

        # Cache tenant objects and their caches
        self._tenant_cache = {}
        self._blob_cache = {}
        self._checkpoint_cache = {}

        logger.info(
            f"Initialized SingleRequestToRepositoryActor with {blob_cache_gb}GB blob cache"
        )

    def _get_or_create_caches(self, tenant_name: str):
        """Get or create caches for a tenant."""
        if tenant_name not in self._tenant_cache:
            tenant = get_tenant(tenant_name)
            self._tenant_cache[tenant_name] = tenant

            # Initialize storage client
            storage_client = storage.Client(
                project=tenant.project_id, credentials=self.credentials
            )

            # Create blob cache
            blob_bucket = storage_client.bucket(tenant.blob_bucket_name)
            self._blob_cache[tenant_name] = GCSBlobCache(
                blob_bucket,
                tenant.blob_bucket_prefix,
                self.blob_cache_size_bytes,
                num_threads=32,
            )

            # Create checkpoint cache
            checkpoint_bucket = storage_client.bucket(tenant.checkpoint_bucket_name)
            self._checkpoint_cache[tenant_name] = GCSCheckpointCache(
                checkpoint_bucket,
                tenant.checkpoint_bucket_prefix,
                self.checkpoint_cache_size_bytes,
                num_threads=16,
            )

        return (
            self._tenant_cache[tenant_name],
            self._blob_cache[tenant_name],
            self._checkpoint_cache[tenant_name],
        )

    def process(self, row: VanguardSingleRequest) -> list[Repository]:
        """Process a single Vanguard request into a Repository object."""
        tenant, blob_cache, _ = self._get_or_create_caches(row.tenant_name)

        # Fetch chat request from GCS
        chat_request = get_chat_request_from_gcs(
            tenant,
            row.request_row["tenant_id"],
            row.request_row["request_id"],
            self.credentials,
        )

        if not chat_request or not chat_request.blob_names:
            logger.warning(
                f"No valid chat request or blobs found for request {row.request_row['request_id']}"
            )
            return []

        # Fetch blob contents
        blob_contents = self._fetch_blob_contents(chat_request, blob_cache)

        if not blob_contents:
            logger.warning(
                f"No blob contents found for request {row.request_row['request_id']}"
            )
            return []

        # Create repository from single request
        repository = self._create_repository_from_request(
            chat_request, blob_contents, tenant.name
        )

        if repository:
            logger.info(
                f"Created repository with {len(repository.file_list)} files "
                f"for request {row.request_row['request_id']}"
            )
            return [repository]
        else:
            logger.warning(
                f"Failed to create repository for request {row.request_row['request_id']}"
            )
            return []

    def _transform_blob_id(self, blob_id: str) -> str:
        """Transform blob ID to hex format for GCS lookup.

        Blob IDs can be in two formats:
        1. Already hex-encoded (64 chars, 0-9a-f)
        2. Base64-encoded (needs to be decoded to hex)
        """
        # Check if it's already a hex string (64 characters, all hex digits)
        if len(blob_id) == 64 and all(c in "0123456789abcdef" for c in blob_id.lower()):
            return blob_id

        # Otherwise, try to decode from base64
        try:
            blob_bytes = base64.b64decode(blob_id)
            return blob_bytes.hex()
        except Exception as e:
            logger.warning(f"Failed to transform blob ID {blob_id}: {e}")
            return blob_id

    def _fetch_blob_contents(
        self, chat_request: VanguardChatRequest, blob_cache: GCSBlobCache
    ) -> dict[str, dict]:
        """Fetch blob contents for a single request."""
        blob_contents = {}

        # Transform blob IDs to hex format
        hex_blob_ids = []
        original_to_hex = {}
        for blob_id in chat_request.blob_names:
            hex_id = self._transform_blob_id(blob_id)
            hex_blob_ids.append(hex_id)
            original_to_hex[blob_id] = hex_id

        logger.debug(f"Request has {len(chat_request.blob_names)} blob names")

        # Fetch blobs
        blob_results = blob_cache.get(hex_blob_ids)

        successful_blobs = 0
        failed_blobs = 0

        for hex_id, result in zip(hex_blob_ids, blob_results):
            if result:
                successful_blobs += 1
                # Find original blob ID
                original_id = None
                for orig, hx in original_to_hex.items():
                    if hx == hex_id:
                        original_id = orig
                        break

                if original_id:
                    blob_contents[original_id] = {
                        "blob_id": hex_id,
                        "path": str(result.path),
                        "content": result.content,
                        "size": len(result.content.encode("utf-8")),
                    }
            else:
                failed_blobs += 1

        logger.info(
            f"Blob fetch results: {successful_blobs} successful, "
            f"{failed_blobs} failed out of {len(hex_blob_ids)} total"
        )

        return blob_contents

    def _create_repository_from_request(
        self,
        chat_request: VanguardChatRequest,
        blob_contents: dict[str, dict],
        tenant_name: str,
    ) -> Optional[Repository]:
        """Create a Repository object from a single request."""

        # Extract workspace identifier from paths
        workspace_key = self._extract_workspace_key(
            [blob_data["path"] for blob_data in blob_contents.values()]
        )

        # Generate repository metadata
        repo_name = (
            f"vanguard_{tenant_name}_{workspace_key}_{chat_request.request_id[:8]}"
        )
        repo_uuid = str(uuid.uuid4())

        # Create File objects
        file_list = []
        lang_counts = defaultdict(int)
        lang_sizes = defaultdict(int)
        total_size = 0

        for blob_id, blob_data in blob_contents.items():
            file_obj = self._create_file_object(blob_data, repo_name)
            file_list.append(file_obj)

            lang = file_obj.langpart
            lang_counts[lang] += 1
            lang_sizes[lang] += file_obj.size
            total_size += file_obj.size

        if not file_list:
            return None

        # Create DocumentWithQuestionsV2 object
        paths = [blob_data["path"] for blob_data in blob_contents.values()]
        document = DocumentWithQuestionsV2(
            question=chat_request.message,
            paths=paths,
            answer="",  # Empty answer for retrieval training
        )

        # Find max language stats
        max_file_lang_name, max_file_count = max(
            lang_counts.items(), key=lambda x: x[1], default=("unknown", 0)
        )
        max_size_lang_name, max_size_total = max(
            lang_sizes.items(), key=lambda x: x[1], default=("unknown", 0)
        )

        return Repository(
            max_stars_repo_name=repo_name,
            max_file_lang={
                "file_count": max_file_count,
                "langpart": max_file_lang_name,
            },
            max_size_lang={
                "total_size": max_size_total,
                "langpart": max_size_lang_name,
            },
            total_size=total_size,
            file_list=file_list,
            documents_with_questions=[document],
            repo_uuid=repo_uuid,
        )

    def _extract_workspace_key(self, paths: list[str]) -> str:
        """Extract a workspace identifier from file paths.

        Since we're processing single requests, all paths belong to the same workspace.
        This method extracts a meaningful identifier from the paths.
        """
        if not paths:
            return "unknown"

        # Clean and normalize paths
        normalized_paths = []
        for path in paths:
            if path and path.strip():
                clean_path = path.replace("\\", "/").strip("/")
                if clean_path:
                    normalized_paths.append(clean_path)

        if not normalized_paths:
            return "unknown"

        # For single file, extract project name from path
        if len(normalized_paths) == 1:
            path_obj = Path(normalized_paths[0])
            parts = path_obj.parts

            # Look for meaningful project indicators
            for i, part in enumerate(parts):
                if part in ["src", "lib", "app", "packages", "apps", "services"]:
                    if i > 0:
                        return self._sanitize_workspace_key(parts[i - 1])
                elif any(
                    indicator in part
                    for indicator in ["-app", "-service", "-lib", "-api"]
                ):
                    return self._sanitize_workspace_key(part)

            # Use parent directory if meaningful
            if len(parts) > 1:
                for part in reversed(parts[:-1]):
                    if part not in ["home", "usr", "var", "tmp", "Users"]:
                        return self._sanitize_workspace_key(part)

        # Multiple files - find common prefix
        all_parts = [Path(p).parts for p in normalized_paths]
        min_depth = min(len(parts) for parts in all_parts)

        common_parts = []
        for i in range(min_depth):
            first_part = all_parts[0][i]
            if all(parts[i] == first_part for parts in all_parts):
                common_parts.append(first_part)
            else:
                break

        # Find the most meaningful common part
        if common_parts:
            # Skip generic prefixes
            while common_parts and common_parts[0] in [
                "home",
                "usr",
                "var",
                "tmp",
                "Users",
            ]:
                common_parts.pop(0)

            if common_parts:
                # Return the deepest meaningful directory
                for part in reversed(common_parts):
                    if part not in ["lib", "local", "src", "bin"]:
                        return self._sanitize_workspace_key(part)

        # Fallback to language-based identifier
        detected_langs = set()
        for path in normalized_paths:
            lang = guess_language(path)
            if lang:
                detected_langs.add(lang)

        if len(detected_langs) == 1:
            return f"{list(detected_langs)[0].lower().replace(' ', '_')}_project"

        return "mixed_project"

    def _sanitize_workspace_key(self, key: str) -> str:
        """Sanitize workspace key for use in repository name."""
        # Replace special characters with underscores
        sanitized = key.replace("/", "_").replace("\\", "_").replace(" ", "_")
        sanitized = sanitized.replace("-", "_").replace(".", "_")
        # Remove consecutive underscores
        while "__" in sanitized:
            sanitized = sanitized.replace("__", "_")
        # Strip leading/trailing underscores
        return sanitized.strip("_")

    def _create_file_object(self, blob_data: dict, repo_name: str) -> File:
        """Create a File object from blob data."""
        path = blob_data["path"]
        content = blob_data["content"]

        # Extract file extension
        ext = Path(path).suffix.lstrip(".")
        if not ext:
            ext = "txt"

        # Detect language
        lang = guess_language(path)
        if not lang:
            lang = "unknown"

        # Calculate metrics
        lines = content.splitlines()
        num_lines = len(lines)

        if num_lines > 0:
            line_lengths = [len(line) for line in lines]
            avg_line_length = sum(line_lengths) / num_lines
            max_line_length = max(line_lengths)
        else:
            avg_line_length = 0.0
            max_line_length = 0

        # Calculate alphanumeric fraction
        if content:
            alphanum_chars = sum(1 for c in content if c.isalnum())
            alphanum_fraction = alphanum_chars / len(content)
        else:
            alphanum_fraction = 0.0

        # Create File object with all required fields
        return File(
            hexsha=blob_data["blob_id"],
            size=blob_data["size"],
            ext=ext,
            max_stars_repo_path=path,
            max_stars_repo_name=repo_name,
            max_stars_repo_licenses=[],
            max_stars_count=None,
            max_stars_repo_stars_event_min_datetime=None,
            max_stars_repo_stars_event_max_datetime=None,
            max_issues_repo_path=path,
            max_issues_repo_name=repo_name,
            max_issues_repo_licenses=[],
            max_issues_count=None,
            max_issues_repo_issues_event_min_datetime=None,
            max_issues_repo_issues_event_max_datetime=None,
            max_forks_repo_path=path,
            max_forks_repo_name=repo_name,
            max_forks_repo_licenses=[],
            max_forks_count=None,
            max_forks_repo_forks_event_min_datetime=None,
            max_forks_repo_forks_event_max_datetime=None,
            content=content,
            avg_line_length=avg_line_length,
            max_line_length=max_line_length,
            alphanum_fraction=alphanum_fraction,
            langpart=lang,
        )
