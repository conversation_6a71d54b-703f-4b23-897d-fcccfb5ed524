#!/usr/bin/env python3
"""
Beachhead Failure Log Analyzer

A command line tool to query and analyze beachhead failure logs from Google Cloud Logging.
Provides insights into failure patterns, pod counts, timing, and cluster information.
"""

import argparse
import json
import sys
import subprocess
from collections import Counter
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Any


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Analyze beachhead failure logs from Google Cloud Logging",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Analyze failures from the last 24 hours (default)
  %(prog)s

  # Analyze failures from the last 6 hours
  %(prog)s --hours 6

  # Analyze failures from a specific time range
  %(prog)s --start "2024-01-15T10:00:00Z" --end "2024-01-15T18:00:00Z"

  # Include detailed log entries
  %(prog)s --verbose

  # Export to CSV format
  %(prog)s --format csv
        """,
    )

    # Time range options
    time_group = parser.add_mutually_exclusive_group()
    time_group.add_argument(
        "--hours",
        type=int,
        default=24,
        help="Number of hours to look back from now (default: 24)",
    )
    time_group.add_argument(
        "--start",
        type=str,
        help="Start time in ISO format (e.g., 2024-01-15T10:00:00Z)",
    )

    parser.add_argument(
        "--end", type=str, help="End time in ISO format (only used with --start)"
    )

    # Query options
    parser.add_argument(
        "--limit",
        type=int,
        default=1000,
        help="Maximum number of log entries to retrieve (default: 1000)",
    )

    # Output options
    parser.add_argument(
        "--verbose",
        "-v",
        action="store_true",
        help="Include detailed log entries in output",
    )

    parser.add_argument(
        "--format",
        choices=["summary", "json", "csv"],
        default="summary",
        help="Output format (default: summary)",
    )

    return parser.parse_args()


def get_time_range(args) -> tuple[datetime, datetime]:
    """Get the time range for the query based on arguments."""
    if args.start:
        start_time = datetime.fromisoformat(args.start.replace("Z", "+00:00"))
        if args.end:
            end_time = datetime.fromisoformat(args.end.replace("Z", "+00:00"))
        else:
            end_time = datetime.now(timezone.utc)
    else:
        end_time = datetime.now(timezone.utc)
        start_time = end_time - timedelta(hours=args.hours)

    return start_time, end_time


def get_projects_to_query() -> List[str]:
    """Get the list of GCP projects to query."""
    # Default to production agent cluster only
    return ["agent-sandbox-prod"]


def build_log_filter(start_time: datetime, end_time: datetime) -> str:
    """Build the Google Cloud Logging filter for beachhead failures."""
    start_str = start_time.strftime("%Y-%m-%dT%H:%M:%SZ")
    end_str = end_time.strftime("%Y-%m-%dT%H:%M:%SZ")

    return f"""
        jsonPayload._augment_event="beachhead_failure"
        AND timestamp >= "{start_str}"
        AND timestamp <= "{end_str}"
    """.strip()


def query_beachhead_failures(
    project: str, log_filter: str, limit: int
) -> List[Dict[str, Any]]:
    """Query beachhead failure logs from a specific project using gcloud command."""
    try:
        # Build gcloud command
        cmd = [
            "gcloud",
            "logging",
            "read",
            log_filter,
            f"--project={project}",
            f"--limit={limit}",
            "--format=json",
            "--freshness=1d",  # Only look at recent logs for performance
        ]

        # Execute gcloud command
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=60,  # 60 second timeout
        )

        if result.returncode != 0:
            print(f"Error querying project {project}: {result.stderr}", file=sys.stderr)
            return []

        # Parse JSON output
        if not result.stdout.strip():
            return []

        log_entries = json.loads(result.stdout)

        # Convert to our format
        results = []
        for entry in log_entries:
            log_data = {
                "timestamp": entry.get("timestamp", ""),
                "project": project,
                "severity": entry.get("severity", ""),
                "resource": entry.get("resource", {}),
                "labels": entry.get("labels", {}),
                "payload": entry.get("jsonPayload", {}),
            }
            results.append(log_data)

        return results

    except subprocess.TimeoutExpired:
        print(f"Timeout querying project {project}", file=sys.stderr)
        return []
    except json.JSONDecodeError as e:
        print(
            f"Error parsing JSON response from project {project}: {e}", file=sys.stderr
        )
        return []
    except Exception as e:
        print(f"Error querying project {project}: {e}", file=sys.stderr)
        return []


def extract_pod_info(log_entry: Dict[str, Any]) -> Dict[str, str]:
    """Extract pod and cluster information from a log entry."""
    info = {
        "pod_name": "unknown",
        "namespace": "unknown",
        "cluster": "unknown",
        "location": "unknown",
        "staging": "unknown",
    }

    # Extract from resource labels (gcloud JSON format)
    resource = log_entry.get("resource", {})
    if isinstance(resource, dict):
        labels = resource.get("labels", {})
        if isinstance(labels, dict):
            info["pod_name"] = labels.get("pod_name", "unknown")
            info["namespace"] = labels.get("namespace_name", "unknown")
            info["cluster"] = labels.get("cluster_name", "unknown")
            info["location"] = labels.get("location", "unknown")

    # Also check top-level labels
    top_labels = log_entry.get("labels", {})
    if isinstance(top_labels, dict):
        if info["pod_name"] == "unknown":
            info["pod_name"] = top_labels.get("pod_name", "unknown")
        if info["namespace"] == "unknown":
            info["namespace"] = top_labels.get("namespace_name", "unknown")
        if info["cluster"] == "unknown":
            info["cluster"] = top_labels.get("cluster_name", "unknown")
        if info["location"] == "unknown":
            info["location"] = top_labels.get("location", "unknown")

    # Determine if staging based on various indicators
    pod_name = info["pod_name"]
    namespace = info["namespace"]
    cluster = info["cluster"]

    staging_indicators = ["staging", "dev", "test"]
    if any(indicator in pod_name.lower() for indicator in staging_indicators):
        info["staging"] = "staging"
    elif any(indicator in namespace.lower() for indicator in staging_indicators):
        info["staging"] = "staging"
    elif any(indicator in cluster.lower() for indicator in staging_indicators):
        info["staging"] = "staging"
    else:
        info["staging"] = "production"

    return info


def analyze_failures(log_entries: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Analyze the beachhead failure log entries."""
    if not log_entries:
        return {
            "total_failures": 0,
            "pod_counts": {},
            "cluster_counts": {},
            "namespace_counts": {},
            "staging_counts": {},
            "hourly_distribution": {},
            "failure_details": [],
        }

    pod_counts = Counter()
    cluster_counts = Counter()
    namespace_counts = Counter()
    staging_counts = Counter()
    hourly_distribution = Counter()
    failure_details = []

    for entry in log_entries:
        pod_info = extract_pod_info(entry)
        timestamp = datetime.fromisoformat(entry["timestamp"].replace("Z", "+00:00"))

        # Count by various dimensions
        pod_with_namespace = f"{pod_info['pod_name']} ({pod_info['namespace']})"
        pod_counts[pod_with_namespace] += 1
        cluster_counts[pod_info["cluster"]] += 1
        namespace_counts[pod_info["namespace"]] += 1
        staging_counts[pod_info["staging"]] += 1

        # Hourly distribution
        hour_key = timestamp.strftime("%Y-%m-%d %H:00")
        hourly_distribution[hour_key] += 1

        # Extract failure details
        payload = entry.get("payload", {})
        restart_count = "unknown"
        return_code = "unknown"
        message = "unknown"

        if isinstance(payload, dict):
            restart_count = payload.get("restart_count", "unknown")
            return_code = payload.get("rc", "unknown")
            message = payload.get("MESSAGE", "unknown")

        failure_details.append(
            {
                "timestamp": entry["timestamp"],
                "pod_name": pod_info["pod_name"],
                "namespace": pod_info["namespace"],
                "cluster": pod_info["cluster"],
                "location": pod_info["location"],
                "staging": pod_info["staging"],
                "restart_count": restart_count,
                "return_code": return_code,
                "message": message,
                "project": entry["project"],
            }
        )

    return {
        "total_failures": len(log_entries),
        "pod_counts": dict(pod_counts.most_common()),
        "cluster_counts": dict(cluster_counts.most_common()),
        "namespace_counts": dict(namespace_counts.most_common()),
        "staging_counts": dict(staging_counts.most_common()),
        "hourly_distribution": dict(sorted(hourly_distribution.items())),
        "failure_details": failure_details,
    }


def format_summary_output(
    analysis: Dict[str, Any], start_time: datetime, end_time: datetime
):
    """Format the analysis results as a human-readable summary."""
    print("Beachhead Failure Analysis")
    print(
        f"Time Range: {start_time.strftime('%Y-%m-%d %H:%M:%S UTC')} to {end_time.strftime('%Y-%m-%d %H:%M:%S UTC')}"
    )
    print(f"Total Failures: {analysis['total_failures']}")
    print()

    if analysis["total_failures"] == 0:
        print("No beachhead failures found in the specified time range.")
        return

    # Pod counts
    print("Failures by Pod:")
    for pod, count in analysis["pod_counts"].items():
        print(f"  {pod}: {count}")
    print()

    # Cluster counts
    print("Failures by Cluster:")
    for cluster, count in analysis["cluster_counts"].items():
        print(f"  {cluster}: {count}")
    print()

    # Namespace counts
    print("Failures by Namespace:")
    for namespace, count in analysis["namespace_counts"].items():
        print(f"  {namespace}: {count}")
    print()


def format_json_output(analysis: Dict[str, Any]):
    """Format the analysis results as JSON."""
    print(json.dumps(analysis, indent=2, default=str))


def format_csv_output(analysis: Dict[str, Any]):
    """Format the failure details as CSV."""
    if not analysis["failure_details"]:
        print(
            "timestamp,pod_name,namespace,cluster,location,staging,restart_count,return_code,message,project"
        )
        return

    # CSV header
    print(
        "timestamp,pod_name,namespace,cluster,location,staging,restart_count,return_code,message,project"
    )

    # CSV rows
    for detail in analysis["failure_details"]:
        # Escape commas and quotes in message
        message = str(detail["message"]).replace('"', '""')
        if "," in message:
            message = f'"{message}"'

        print(
            f"{detail['timestamp']},{detail['pod_name']},{detail['namespace']},"
            f"{detail['cluster']},{detail['location']},{detail['staging']},"
            f"{detail['restart_count']},{detail['return_code']},{message},{detail['project']}"
        )


def main():
    """Main function."""
    args = parse_args()

    # Get time range
    start_time, end_time = get_time_range(args)

    # Get projects to query
    projects = get_projects_to_query()

    # Build log filter
    log_filter = build_log_filter(start_time, end_time)

    if args.verbose:
        print(f"Querying projects: {', '.join(projects)}", file=sys.stderr)
        print(f"Time range: {start_time} to {end_time}", file=sys.stderr)
        print(f"Log filter: {log_filter}", file=sys.stderr)
        print(file=sys.stderr)

    # Query all projects
    all_entries = []
    for project in projects:
        if args.verbose:
            print(f"Querying project: {project}...", file=sys.stderr)

        entries = query_beachhead_failures(project, log_filter, args.limit)
        all_entries.extend(entries)

        if args.verbose:
            print(f"Found {len(entries)} entries in {project}", file=sys.stderr)

    # Analyze the results
    analysis = analyze_failures(all_entries)

    # Output results
    if args.format == "json":
        format_json_output(analysis)
    elif args.format == "csv":
        format_csv_output(analysis)
    else:
        format_summary_output(analysis, start_time, end_time)

        if args.verbose and analysis["failure_details"]:
            print("\nDetailed Failure Entries:")
            for detail in analysis["failure_details"][:20]:  # Show first 20
                print(
                    f"  {detail['timestamp']} | {detail['pod_name']} | {detail['cluster']} | "
                    f"{detail['staging']} | RC:{detail['return_code']} | {detail['message']}"
                )
            if len(analysis["failure_details"]) > 20:
                print(f"  ... and {len(analysis['failure_details']) - 20} more entries")


if __name__ == "__main__":
    main()
