<script lang="ts">
  import type { ChangedFile } from "$vscode/src/remote-agent-manager/types";
  import { getContext } from "svelte";
  import { RemoteAgentDiffOpsModel } from "../models/ra-diff-ops-model";
  import { RemoteAgentDiffModel } from "../models/remote-agent-diff-model";
  import AgentDetailFileExplorer from "./AgentDetailFileExplorer.svelte";
  import RemoteAgentDiffView from "./RemoteAgentDiffView.svelte";
  import { getInitialFocusedPathContext, subscribeToFocusedPath } from "./context";

  export let changedFiles: ChangedFile[] = [];
  export let pendingFiles: string[] = [];
  export let appliedFiles: string[] = [];
  export let agentLabel: string | undefined = undefined;
  export let latestUserPrompt: string | undefined = undefined;
  export let isAgentFromDifferentRepo: boolean = false;

  const diffOperationsModel = getContext<RemoteAgentDiffOpsModel>(RemoteAgentDiffOpsModel.key);
  const diffModel = getContext<RemoteAgentDiffModel>(RemoteAgentDiffModel.key);

  let diffView: "changedFiles" | "summary" = "summary";

  const onApplyChanges = async (path: string, originalCode: string, newCode: string) => {
    await diffOperationsModel.applyChanges(path, originalCode, newCode);
  };

  const onOpenFile = (path: string) => {
    return diffOperationsModel.openFile(path);
  };

  const focusedPath = getInitialFocusedPathContext(null);
  subscribeToFocusedPath(focusedPath);
</script>

<div class="diff-page">
  <div class="file-explorer-main">
    {#if changedFiles}
      <div class="file-explorer-contents">
        {#key $diffModel.opts}
          {#if diffView === "changedFiles"}
            <AgentDetailFileExplorer
              {changedFiles}
              {onApplyChanges}
              {onOpenFile}
              {pendingFiles}
              {appliedFiles}
            />
          {:else}
            <RemoteAgentDiffView
              {changedFiles}
              {onApplyChanges}
              {onOpenFile}
              {agentLabel}
              {latestUserPrompt}
              onRenderBackup={() => {
                diffView = "changedFiles";
              }}
              preloadedExplanation={$diffModel?.opts?.preloadedExplanation}
              {isAgentFromDifferentRepo}
            />
          {/if}
        {/key}
      </div>
    {/if}
  </div>
</div>

<style>
  .diff-page {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .file-explorer-contents {
    flex: 1;
    overflow: auto;
    min-height: 0;
  }

  .file-explorer-main {
    padding: 0 var(--ds-spacing-2);
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;
  }
</style>
