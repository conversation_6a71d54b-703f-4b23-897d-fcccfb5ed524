/**
 * @file task-tools.test.ts
 * Tests for the task management tools.
 */

import { Exchange } from "../../../chat/chat-types";
import { TaskState, TaskUpdatedBy } from "../../../agent/task/task-types";
import {
  createTaskTestEnvironment,
  createMockTask,
  createMockTaskManifest,
} from "../../../agent/task/__tests__/task-test-kit";
import {
  ViewTaskListTool,
  UpdateTasksTool,
  AddTasksTool,
  ReorganizeTaskListTool,
} from "../task-tools";

// Mock crypto.randomUUID to generate unique UUIDs for testing
let uuidCounter = 0;
const mockRandomUUID = jest.fn(() => `test-uuid-${++uuidCounter}`);
Object.defineProperty(global, 'crypto', {
  value: {
    randomUUID: mockRandomUUID,
  },
  writable: true,
});

describe("Task Tools", () => {
  let testEnv: ReturnType<typeof createTaskTestEnvironment>;
  let mockChatHistory: Exchange[];
  let mockAbortSignal: AbortSignal;

  beforeEach(() => {
    // Reset UUID counter for predictable test UUIDs
    uuidCounter = 0;
    mockRandomUUID.mockClear();

    testEnv = createTaskTestEnvironment();
    mockChatHistory = [];
    mockAbortSignal = new AbortController().signal;
  });

  afterEach(() => {
    testEnv.cleanup();
  });

  describe("ViewTaskListTool", () => {
    it("should return error when no root task is set", async () => {
      const tool = new ViewTaskListTool(testEnv.manager);
      const result = await tool.call({}, mockChatHistory, mockAbortSignal);

      expect(result.isError).toBe(true);
      expect(result.text).toContain("No root task found");
    });

    it("should return task list markdown when root task exists", async () => {
      await testEnv.manager.initialize();
      
      // Create a root task
      const rootTaskId = await testEnv.manager.createTask("Root Task", "Root description");
      testEnv.manager.setCurrentRootTaskUuid(rootTaskId);

      const tool = new ViewTaskListTool(testEnv.manager);
      const result = await tool.call({}, mockChatHistory, mockAbortSignal);

      expect(result.isError).toBe(false);
      expect(result.text).toContain("# Current Task List");
      expect(result.text).toContain("Root Task");
      expect(result.text).toContain("Root description");
    });
  });

  describe("UpdateTasksTool", () => {
    let rootTaskId: string;

    beforeEach(async () => {
      await testEnv.manager.initialize();
      rootTaskId = await testEnv.manager.createTask("Root Task", "Root description");
      testEnv.manager.setCurrentRootTaskUuid(rootTaskId);
    });

    it("should return error when task_id is missing", async () => {
      const tool = new UpdateTasksTool(testEnv.manager);
      const result = await tool.call({}, mockChatHistory, mockAbortSignal);

      expect(result.isError).toBe(true);
      expect(result.text).toContain("Either task_id or tasks array is required");
    });

    it("should return error when task does not exist", async () => {
      const tool = new UpdateTasksTool(testEnv.manager);
      const result = await tool.call(
        { task_id: "non-existent-id" },
        mockChatHistory,
        mockAbortSignal
      );

      expect(result.isError).toBe(true);
      expect(result.text).toContain("not found");
    });

    it("should update task state successfully", async () => {
      const tool = new UpdateTasksTool(testEnv.manager);
      const result = await tool.call(
        { task_id: rootTaskId, state: "IN_PROGRESS" },
        mockChatHistory,
        mockAbortSignal
      );

      expect(result.isError).toBe(false);
      expect(result.text).toContain("updated successfully");
      expect(result.text).toContain("state: IN_PROGRESS");

      // Verify the task was actually updated
      const updatedTask = await testEnv.manager.getTask(rootTaskId);
      expect(updatedTask?.state).toBe(TaskState.IN_PROGRESS);
    });

    it("should update task name and description", async () => {
      const tool = new UpdateTasksTool(testEnv.manager);
      const result = await tool.call(
        {
          task_id: rootTaskId,
          name: "Updated Name",
          description: "Updated Description",
        },
        mockChatHistory,
        mockAbortSignal
      );

      expect(result.isError).toBe(false);
      expect(result.text).toContain("name: Updated Name");
      expect(result.text).toContain("description: Updated Description");

      // Verify the task was actually updated
      const updatedTask = await testEnv.manager.getTask(rootTaskId);
      expect(updatedTask?.name).toBe("Updated Name");
      expect(updatedTask?.description).toBe("Updated Description");
    });

    it("should return error for invalid state", async () => {
      const tool = new UpdateTasksTool(testEnv.manager);
      const result = await tool.call(
        { task_id: rootTaskId, state: "INVALID_STATE" },
        mockChatHistory,
        mockAbortSignal
      );

      expect(result.isError).toBe(true);
      expect(result.text).toContain("Invalid state");
    });

    it("should return error when no updates provided", async () => {
      const tool = new UpdateTasksTool(testEnv.manager);
      const result = await tool.call(
        { task_id: rootTaskId },
        mockChatHistory,
        mockAbortSignal
      );

      expect(result.isError).toBe(true);
      expect(result.text).toContain("At least one property");
    });

    it("should update multiple tasks in batch", async () => {
      // Create additional tasks for batch testing
      const task1Id = await testEnv.manager.createTask("Task 1", "First task", rootTaskId);
      const task2Id = await testEnv.manager.createTask("Task 2", "Second task", rootTaskId);

      const tool = new UpdateTasksTool(testEnv.manager);
      const result = await tool.call(
        {
          tasks: [
            { task_id: task1Id, state: "IN_PROGRESS" },
            { task_id: task2Id, state: "COMPLETE", name: "Updated Task 2" }
          ]
        },
        mockChatHistory,
        mockAbortSignal
      );

      expect(result.isError).toBe(false);
      expect(result.text).toContain("Batch update completed");
      expect(result.text).toContain("2 tasks updated successfully");

      // Verify the tasks were actually updated
      const updatedTask1 = await testEnv.manager.getTask(task1Id);
      const updatedTask2 = await testEnv.manager.getTask(task2Id);
      expect(updatedTask1?.state).toBe(TaskState.IN_PROGRESS);
      expect(updatedTask2?.state).toBe(TaskState.COMPLETE);
      expect(updatedTask2?.name).toBe("Updated Task 2");
    });

    it("should handle batch update with some failures", async () => {
      const validTaskId = await testEnv.manager.createTask("Valid Task", "Valid task", rootTaskId);

      const tool = new UpdateTasksTool(testEnv.manager);
      const result = await tool.call(
        {
          tasks: [
            { task_id: validTaskId, state: "IN_PROGRESS" },
            { task_id: "non-existent-id", state: "COMPLETE" },
            { task_id: validTaskId, state: "INVALID_STATE" }
          ]
        },
        mockChatHistory,
        mockAbortSignal
      );

      expect(result.isError).toBe(false);
      expect(result.text).toContain("1 tasks updated successfully");
      expect(result.text).toContain("2 tasks failed");
      expect(result.text).toContain("✅ Successfully updated");
      expect(result.text).toContain("❌ Failed to update");
    });

    it("should return error when neither task_id nor tasks provided", async () => {
      const tool = new UpdateTasksTool(testEnv.manager);
      const result = await tool.call(
        { state: "IN_PROGRESS" },
        mockChatHistory,
        mockAbortSignal
      );

      expect(result.isError).toBe(true);
      expect(result.text).toContain("Either task_id or tasks array is required");
    });
  });

  describe("AddTasksTool", () => {
    let rootTaskId: string;

    beforeEach(async () => {
      await testEnv.manager.initialize();
      rootTaskId = await testEnv.manager.createTask("Root Task", "Root description");
      testEnv.manager.setCurrentRootTaskUuid(rootTaskId);
    });

    it("should return error when name is missing", async () => {
      const tool = new AddTasksTool(testEnv.manager);
      const result = await tool.call(
        { description: "Test description" },
        mockChatHistory,
        mockAbortSignal
      );

      expect(result.isError).toBe(true);
      expect(result.text).toContain("Either single task properties (name, description) or tasks array is required");
    });

    it("should return error when description is missing", async () => {
      const tool = new AddTasksTool(testEnv.manager);
      const result = await tool.call(
        { name: "Test name" },
        mockChatHistory,
        mockAbortSignal
      );

      expect(result.isError).toBe(true);
      expect(result.text).toContain("Both name and description are required");
    });

    it("should create a new root-level task", async () => {
      const tool = new AddTasksTool(testEnv.manager);
      const result = await tool.call(
        {
          name: "New Task",
          description: "New task description",
        },
        mockChatHistory,
        mockAbortSignal
      );

      expect(result.isError).toBe(false);
      expect(result.text).toContain("Task created successfully");
      expect(result.text).toContain("New Task");
    });

    it("should create a subtask when parent_task_id is provided", async () => {
      const tool = new AddTasksTool(testEnv.manager);

      const result = await tool.call(
        {
          name: "Subtask",
          description: "Subtask description",
          parent_task_id: rootTaskId,
        },
        mockChatHistory,
        mockAbortSignal
      );

      expect(result.isError).toBe(false);
      expect(result.text).toContain("Task created successfully");

      // Verify the subtask was added to the parent
      const parentTask = await testEnv.manager.getTask(rootTaskId);
      expect(parentTask?.subTasks).toHaveLength(1);
    });

    it("should return error when parent task does not exist", async () => {
      const tool = new AddTasksTool(testEnv.manager);
      const result = await tool.call(
        {
          name: "Subtask",
          description: "Subtask description",
          parent_task_id: "non-existent-id",
        },
        mockChatHistory,
        mockAbortSignal
      );

      expect(result.isError).toBe(true);
      expect(result.text).toContain("Parent task");
      expect(result.text).toContain("not found");
    });

    it("should create task with specified state", async () => {
      const tool = new AddTasksTool(testEnv.manager);
      const result = await tool.call(
        {
          name: "In Progress Task",
          description: "Task description",
          state: "IN_PROGRESS",
        },
        mockChatHistory,
        mockAbortSignal
      );

      expect(result.isError).toBe(false);

      // Extract task ID from response and verify state
      const taskIdMatch = result.text.match(/UUID: (test-uuid-\d+)/);
      expect(taskIdMatch).toBeTruthy();
      
      if (taskIdMatch) {
        const newTaskId = taskIdMatch[1];
        const newTask = await testEnv.manager.getTask(newTaskId);
        expect(newTask?.state).toBe(TaskState.IN_PROGRESS);
      }
    });
  });



  describe("ReorganizeTaskListTool", () => {
    let rootTaskId: string;

    beforeEach(async () => {
      await testEnv.manager.initialize();
      rootTaskId = await testEnv.manager.createTask("Root Task", "Root description");
      testEnv.manager.setCurrentRootTaskUuid(rootTaskId);
    });

    it("should return error when markdown is missing", async () => {
      const tool = new ReorganizeTaskListTool(testEnv.manager);
      const result = await tool.call({}, mockChatHistory, mockAbortSignal);

      expect(result.isError).toBe(true);
      expect(result.text).toContain("No markdown provided");
    });

    it("should return error when no root task is set", async () => {
      testEnv.manager.setCurrentRootTaskUuid("");

      const tool = new ReorganizeTaskListTool(testEnv.manager);
      const result = await tool.call(
        { markdown: "[ ] UUID:test NAME:Test DESCRIPTION:Test" },
        mockChatHistory,
        mockAbortSignal
      );

      expect(result.isError).toBe(true);
      expect(result.text).toContain("No root task found");
    });

    it("should return error for invalid markdown format", async () => {
      const tool = new ReorganizeTaskListTool(testEnv.manager);
      const result = await tool.call(
        { markdown: "Invalid markdown format" },
        mockChatHistory,
        mockAbortSignal
      );

      expect(result.isError).toBe(true);
      expect(result.text).toContain("Failed to parse markdown");
    });

    it("should update task list successfully with valid markdown", async () => {
      const validMarkdown = `[/] UUID:${rootTaskId} NAME:Updated Root DESCRIPTION:Updated root description
-[ ] UUID:NEW_UUID NAME:New Subtask DESCRIPTION:New subtask description`;

      const tool = new ReorganizeTaskListTool(testEnv.manager);
      const result = await tool.call(
        { markdown: validMarkdown },
        mockChatHistory,
        mockAbortSignal
      );

      expect(result.isError).toBe(false);
      expect(result.text).toContain("Task list updated successfully");
      expect(result.text).toContain("Created: 1");
      expect(result.text).toContain("Updated: 1");

      // Verify the root task was updated
      const updatedRootTask = await testEnv.manager.getTask(rootTaskId);
      expect(updatedRootTask?.name).toBe("Updated Root");
      expect(updatedRootTask?.state).toBe(TaskState.IN_PROGRESS);
      expect(updatedRootTask?.subTasks).toHaveLength(1);
    });

    it("should return error for multiple root tasks", async () => {
      const invalidMarkdown = `[/] UUID:${rootTaskId} NAME:Root Task 1 DESCRIPTION:First root task
[ ] UUID:NEW_UUID NAME:Root Task 2 DESCRIPTION:Second root task
-[ ] UUID:NEW_UUID NAME:Subtask DESCRIPTION:A subtask`;

      const tool = new ReorganizeTaskListTool(testEnv.manager);
      const result = await tool.call(
        { markdown: invalidMarkdown },
        mockChatHistory,
        mockAbortSignal
      );

      expect(result.isError).toBe(true);
      expect(result.text).toContain("Multiple root tasks found");
      expect(result.text).toContain("There can only be one root task per conversation");
    });

    it("should return error for no root tasks", async () => {
      const invalidMarkdown = `-[ ] UUID:NEW_UUID NAME:Subtask 1 DESCRIPTION:First subtask
-[ ] UUID:NEW_UUID NAME:Subtask 2 DESCRIPTION:Second subtask`;

      const tool = new ReorganizeTaskListTool(testEnv.manager);
      const result = await tool.call(
        { markdown: invalidMarkdown },
        mockChatHistory,
        mockAbortSignal
      );

      expect(result.isError).toBe(true);
      expect(result.text).toContain("No root task found");
    });
  });
});
