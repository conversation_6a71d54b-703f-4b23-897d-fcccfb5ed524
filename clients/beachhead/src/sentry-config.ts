import * as Sentry from "@sentry/node";
import type { Express } from "express";

import { FeatureFlags } from "./feature-flags";

let sentryInitialized = false;

export function initializeSentry(featureFlags?: FeatureFlags, agentId?: string, modelId?: string) {
    // Check if Sen<PERSON> is explicitly disabled via feature flag
    if (featureFlags?.beachheadEnableSentry === false) {
        return;
    }

    // If no feature flag is provided, use the enableSentry parameter
    if (!featureFlags?.beachheadEnableSentry) {
        return;
    }

    // Get the sampling rate from feature flags or default to 0%
    const samplingRate =
        typeof featureFlags?.beachheadErrorSamplingRate === "number"
            ? featureFlags.beachheadErrorSamplingRate
            : 0.0;

    const traceSamplingRate =
        typeof featureFlags?.beachheadTraceSamplingRate === "number"
            ? featureFlags.beachheadTraceSamplingRate
            : 0.0;

    Sentry.init({
        dsn: "https://<EMAIL>/4509431524032512",
        sendDefaultPii: false,
        sampleRate: samplingRate,
        tracesSampleRate: traceSamplingRate,
    });

    // Set the agent_id as a tag if provided
    if (agentId) {
        Sentry.setTag("agent_id", agentId);
    }

    // Set the model_id as a tag if provided
    if (modelId) {
        Sentry.setTag("model_id", modelId);
    }

    sentryInitialized = true;
}

export function initializeExpressErrorHandler(app: Express) {
    if (!sentryInitialized) {
        return;
    }

    Sentry.setupExpressErrorHandler(app);
}

export function captureSentryException(
    error: Error,
    level: "warning" | "error" = "error",
    sessionId?: string,
    context?: Record<string, any>
) {
    if (!sentryInitialized) {
        return;
    }

    Sentry.withScope((scope) => {
        scope.setLevel(level);

        if (context) {
            scope.setContext("error_context", context);
        }
        if (sessionId) {
            Sentry.setTag("session_id", sessionId);
        }
        Sentry.captureException(error);
    });
}
