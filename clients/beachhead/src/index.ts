import { SerializedStore } from "@augment-internal/sidecar-libs/src/agent/agent-edit-types";
import { AggregateCheckpointManager } from "@augment-internal/sidecar-libs/src/agent/checkpoint/aggregate-checkpoint-manager";
import { ShardedStorage } from "@augment-internal/sidecar-libs/src/agent/sharding/storage";
import { ChatMode, ChatRequestNode } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { setLibraryAPIClient } from "@augment-internal/sidecar-libs/src/client-interfaces/api-client";
import { setLibraryClientWorkspaces } from "@augment-internal/sidecar-libs/src/client-interfaces/client-workspaces";
import {
    IClientFeatureFlags,
    setLibraryClientFeatureFlags,
} from "@augment-internal/sidecar-libs/src/client-interfaces/feature-flags";
import { ToolUseRequestEventReporter } from "@augment-internal/sidecar-libs/src/metrics/tool-use-request-event-reporter";
import { SidecarToolType } from "@augment-internal/sidecar-libs/src/tools/sidecar-tools/sidecar-tool-types";
import {
    McpServerConfig,
    ToolStartupError,
} from "@augment-internal/sidecar-libs/src/tools/tool-types";
import { ToolsModel } from "@augment-internal/sidecar-libs/src/tools/tools-model";
import * as os from "os";
import * as path from "path";
import * as process from "process";
import { v4 as uuidv4 } from "uuid";
import yargs from "yargs";
import { hideBin } from "yargs/helpers";

import { AgentLoop, makeMessageNodes } from "./agent_loop/agent_loop";
import { AgentState } from "./agent_loop/state";
import { StatePersistence } from "./agent_loop/state-persistence";
import {
    APIServerImplWithErrorReporting as APIServerImpl,
    AuthSessionStore,
    ModelConfig,
} from "./augment-api";
import { AugmentConfigListener } from "./augment-config-listener";
import { BeachheadRemoteInfo } from "./beachhead-remote-info";
import { beachheadToolHostFactory } from "./beachhead-tool-host";
import { BeachheadClientWorkspaces } from "./client-interfaces/client-workspaces";
import { SidecarAPIClient } from "./client-interfaces/sidecar-api-client";
import { EventTimer } from "./event-timer";
import { defaultFeatureFlags, FeatureFlags } from "./feature-flags";
import { getLogger } from "./logging";
import { captureSentryException, initializeSentry } from "./sentry-config";
import { startServer } from "./server";
import { SSHManager } from "./ssh";
import { SSHConnectionFinder } from "./ssh-connections";
import { fileExists, readFileUtf8 } from "./utils/fs-utils";
import { WorkspaceAgentConfig } from "./workspace-agent-config";
import { CommandStatus, InitCommands } from "./workspace-init";
import { BeachheadWorkspaceManagerImpl } from "./workspace-manager";
import { WorkspaceSetupStatusTracker } from "./workspace-setup-status-tracker";

/**
 * Converts ModelConfig to both FeatureFlags and IClientFeatureFlags objects
 * Uses live feature flags from the API where available, with sensible defaults for others.
 * Follows the same pattern as VSCode's ClientFeatureFlags class.
 */
function convertToFeatureFlags(modelConfig: ModelConfig): {
    featureFlags: FeatureFlags;
    clientFeatureFlags: IClientFeatureFlags;
} {
    const apiFeatureFlags = modelConfig.featureFlags;

    // Create the full FeatureFlags object by merging API flags with defaults
    const featureFlags: FeatureFlags = {
        ...defaultFeatureFlags,
        vscodeAgentEditTool:
            apiFeatureFlags.vscodeAgentEditTool ?? defaultFeatureFlags.vscodeAgentEditTool,
        memoriesParams: apiFeatureFlags.memoriesParams ?? defaultFeatureFlags.memoriesParams,
        agentEditToolMinViewSize:
            apiFeatureFlags.agentEditToolMinViewSize ??
            defaultFeatureFlags.agentEditToolMinViewSize,
        agentEditToolSchemaType:
            apiFeatureFlags.agentEditToolSchemaType ?? defaultFeatureFlags.agentEditToolSchemaType,
        agentEditToolEnableFuzzyMatching:
            apiFeatureFlags.agentEditToolEnableFuzzyMatching ??
            defaultFeatureFlags.agentEditToolEnableFuzzyMatching,
        agentEditToolFuzzyMatchSuccessMessage:
            apiFeatureFlags.agentEditToolFuzzyMatchSuccessMessage ??
            defaultFeatureFlags.agentEditToolFuzzyMatchSuccessMessage,
        agentEditToolFuzzyMatchMaxDiff:
            apiFeatureFlags.agentEditToolFuzzyMatchMaxDiff ??
            defaultFeatureFlags.agentEditToolFuzzyMatchMaxDiff,
        agentEditToolFuzzyMatchMaxDiffRatio:
            apiFeatureFlags.agentEditToolFuzzyMatchMaxDiffRatio ??
            defaultFeatureFlags.agentEditToolFuzzyMatchMaxDiffRatio,
        agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs:
            apiFeatureFlags.agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs ??
            defaultFeatureFlags.agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs,
        agentEditToolInstructionsReminder:
            apiFeatureFlags.agentEditToolInstructionsReminder ??
            defaultFeatureFlags.agentEditToolInstructionsReminder,
        agentEditToolShowResultSnippet:
            apiFeatureFlags.agentEditToolShowResultSnippet ??
            defaultFeatureFlags.agentEditToolShowResultSnippet,
        agentEditToolMaxLines:
            apiFeatureFlags.agentEditToolMaxLines ?? defaultFeatureFlags.agentEditToolMaxLines,
        agentSaveFileToolInstructionsReminder:
            apiFeatureFlags.agentSaveFileToolInstructionsReminder ??
            defaultFeatureFlags.agentSaveFileToolInstructionsReminder,
        vscodeTaskListMinVersion:
            apiFeatureFlags.vscodeTaskListMinVersion ??
            defaultFeatureFlags.vscodeTaskListMinVersion,
        grepSearchToolEnable:
            apiFeatureFlags.grepSearchToolEnable ?? defaultFeatureFlags.grepSearchToolEnable,
        grepSearchToolTimelimitSec:
            apiFeatureFlags.grepSearchToolTimelimitSec ??
            defaultFeatureFlags.grepSearchToolTimelimitSec,
        grepSearchToolOutputCharsLimit:
            apiFeatureFlags.grepSearchToolOutputCharsLimit ??
            defaultFeatureFlags.grepSearchToolOutputCharsLimit,
        grepSearchToolNumContextLines:
            apiFeatureFlags.grepSearchToolNumContextLines ??
            defaultFeatureFlags.grepSearchToolNumContextLines,
        agentReportStreamedChatEveryChunk:
            apiFeatureFlags.agentReportStreamedChatEveryChunk ??
            defaultFeatureFlags.agentReportStreamedChatEveryChunk,
        agentMaxTotalChangedFilesSizeBytes:
            apiFeatureFlags.agentMaxTotalChangedFilesSizeBytes ??
            defaultFeatureFlags.agentMaxTotalChangedFilesSizeBytes,
        agentMaxChangedFilesSkippedPaths:
            apiFeatureFlags.agentMaxChangedFilesSkippedPaths ??
            defaultFeatureFlags.agentMaxChangedFilesSkippedPaths,
        agentIdleStatusUpdateIntervalMs:
            apiFeatureFlags.agentIdleStatusUpdateIntervalMs ??
            defaultFeatureFlags.agentIdleStatusUpdateIntervalMs,
        agentMaxIterations:
            apiFeatureFlags.agentMaxIterations ?? defaultFeatureFlags.agentMaxIterations,
        agentSshConnectionCheckIntervalMs:
            apiFeatureFlags.agentSshConnectionCheckIntervalMs ??
            defaultFeatureFlags.agentSshConnectionCheckIntervalMs,
        agentSshConnectionCheckLogIntervalMs:
            apiFeatureFlags.agentSshConnectionCheckLogIntervalMs ??
            defaultFeatureFlags.agentSshConnectionCheckLogIntervalMs,
        beachheadEnableSentry:
            apiFeatureFlags.beachheadEnableSentry ?? defaultFeatureFlags.beachheadEnableSentry,
    };

    // Create the IClientFeatureFlags object from the FeatureFlags object
    const clientFeatureFlags = createClientFeatureFlags(featureFlags);

    return { featureFlags, clientFeatureFlags };
}

/**
 * Creates an IClientFeatureFlags object from a FeatureFlags object
 */
function createClientFeatureFlags(featureFlags: FeatureFlags): IClientFeatureFlags {
    return {
        flags: {
            agentEditTool: featureFlags.vscodeAgentEditTool,
            memoriesParams: featureFlags.memoriesParams,
            agentEditToolMinViewSize: featureFlags.agentEditToolMinViewSize,
            agentEditToolSchemaType: featureFlags.agentEditToolSchemaType,
            agentEditToolEnableFuzzyMatching: featureFlags.agentEditToolEnableFuzzyMatching,
            agentEditToolFuzzyMatchSuccessMessage:
                featureFlags.agentEditToolFuzzyMatchSuccessMessage,
            agentEditToolFuzzyMatchMaxDiff: featureFlags.agentEditToolFuzzyMatchMaxDiff,
            agentEditToolFuzzyMatchMaxDiffRatio: featureFlags.agentEditToolFuzzyMatchMaxDiffRatio,
            agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs:
                featureFlags.agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs,
            agentEditToolInstructionsReminder: featureFlags.agentEditToolInstructionsReminder,
            agentEditToolShowResultSnippet: featureFlags.agentEditToolShowResultSnippet,
            agentEditToolMaxLines: featureFlags.agentEditToolMaxLines,
            agentSaveFileToolInstructionsReminder:
                featureFlags.agentSaveFileToolInstructionsReminder,
            enableTaskList: featureFlags.vscodeTaskListMinVersion ? true : false,
            grepSearchToolEnable: featureFlags.grepSearchToolEnable,
            grepSearchToolTimelimitSec: featureFlags.grepSearchToolTimelimitSec,
            grepSearchToolOutputCharsLimit: featureFlags.grepSearchToolOutputCharsLimit,
            grepSearchToolNumContextLines: featureFlags.grepSearchToolNumContextLines,
            // Always enabled for beachhead
            enableChatWithTools: true,
            enableAgentMode: true,
        },
    };
}

async function readApiToken(): Promise<string> {
    const logger = getLogger("Beachhead");
    const apiToken = process.env.AUGMENT_API_TOKEN;
    delete process.env.AUGMENT_API_TOKEN;
    if (apiToken) {
        return apiToken;
    }
    try {
        const homedir = os.homedir();
        const tokenPath = path.join(homedir, ".augment", "token");
        if (fileExists(tokenPath)) {
            return (await readFileUtf8(tokenPath)).trim();
        }
    } catch (error) {
        logger.error("Failed to read token file: %s", error);
    }
    return "";
}

async function parseArgs() {
    return await yargs(hideBin(process.argv))
        .option("port", {
            type: "number",
            default: 0,
            description: "Port to listen on",
            demandOption: false,
        })
        .option("ssh-port", {
            type: "number",
            default: 0,
            description: "SSH port to check for connections on",
            demandOption: false,
        })
        .option("workspace-root", {
            type: "string",
            description: "Root folder of the workspace to index",
            demandOption: false,
        })
        .option("api-url", {
            type: "string",
            description: "API URL to use",
            demandOption: false,
        })
        .option("instruction", {
            type: "string",
            description:
                "Initial instruction to run. Mutually exclusive with --workspace-agent-config.",
            demandOption: false,
        })
        .option("workspace-agent-config", {
            type: "string",
            description:
                "Path to a file containing a WorkspaceAgentConfig message with remote_agent_id and config",
            demandOption: false,
        })
        .check((argv) => {
            // Ensure exactly one of instruction or workspace-agent-config is provided
            const optionsCount = [argv.instruction, argv["workspace-agent-config"]].filter(
                Boolean
            ).length;
            if (optionsCount !== 1) {
                throw new Error(
                    "Exactly one of --instruction or --workspace-agent-config must be provided"
                );
            }
            return true;
        })
        .option("streaming-chat-history", {
            type: "boolean",
            description: "whether the current open exchange should be added to chat history",
            demandOption: false,
            default: true,
        })
        .option("persistent-root", {
            type: "string",
            description: "Root directory for persistent state storage",
            demandOption: false,
        })
        .option("setup-mode", {
            type: "boolean",
            description: "Generate a setup script for the current project",
            demandOption: false,
            default: false,
        })
        .option("image-hash", {
            type: "string",
            description: "Git hash of the image for user-agent string",
            demandOption: false,
            default: "0.0.0",
        })
        .help()
        .parse();
}

// Main async function to allow awaiting
async function main() {
    const logger = getLogger("Beachhead");
    const startupTimer = new EventTimer();
    startupTimer.start();

    // Parse command line arguments
    const args = await startupTimer.asyncEvent("parseArgs", parseArgs());

    // load config
    const workspaceAgentConfigPath = args["workspace-agent-config"];
    let wsCfg: WorkspaceAgentConfig | undefined;
    if (workspaceAgentConfigPath) {
        try {
            wsCfg = await startupTimer.asyncEvent(
                "workspaceAgentConfig.fromFile",
                WorkspaceAgentConfig.fromFile(workspaceAgentConfigPath)
            );
        } catch (error) {
            logger.error("Failed to read workspace agent config file: %s", error);
            throw error;
        }
    }

    // Get options
    const port = args["port"];
    const sshPort = args["ssh-port"];
    const apiToken = await startupTimer.asyncEvent("readApiToken", readApiToken());
    const folderRoot = args["workspace-root"] || process.env.AUGMENT_WORKSPACE_ROOT || "";
    const instruction = args["instruction"];

    // Track the agent ID if provided via workspace agent config
    let remoteAgentId: string | undefined;
    const streamingChatHistory = args["streaming-chat-history"];
    const persistentRoot = args["persistent-root"];
    const apiUrl =
        args["api-url"] ||
        (wsCfg?.config().workspace_api_url() ?? "") ||
        process.env.AUGMENT_API_URL;

    if (!apiToken) {
        throw new Error("AUGMENT_API_TOKEN is not set");
    }
    if (!folderRoot) {
        throw new Error(
            "Folder root not specified. Use --workspace-root argument or set AUGMENT_WORKSPACE_ROOT environment variable"
        );
    }
    if (!apiUrl) {
        throw new Error(
            "apiUrl not specified. Use --api-url argument or set AUGMENT_API_URL environment variable"
        );
    }

    let startingNodes: ChatRequestNode[] = [];
    let initCommands: InitCommands | undefined;
    let agentMemories = "";
    let userGuidelines = "";
    let workspaceGuidelines = "";
    let modelId = "";
    let mcpServers: McpServerConfig[] = [];
    if (wsCfg) {
        try {
            remoteAgentId = wsCfg.remoteAgentId();
            const cfg = wsCfg.config();
            startingNodes = cfg.starting_nodes();
            agentMemories = cfg.agent_memories();
            userGuidelines = cfg.user_guidelines();
            workspaceGuidelines = cfg.workspace_guidelines();
            modelId = cfg.model_id();
            // We need to convert from the backend's snake_case to the frontend's camelCase.
            mcpServers = cfg.mcp_servers().map((server) => ({
                command: server.command,
                args: server.args,
                timeoutMs: server.timeout_ms,
                env: server.env,
                useShellInterpolation: server.use_shell_interpolation,
                name: server.name,
                disabled: server.disabled,
            }));
            logger.info(
                "Using instruction from config file: %s for remote agent ID: %s",
                workspaceAgentConfigPath,
                remoteAgentId
            );
            logger.info("Loaded %d MCP servers from config", mcpServers.length);
            initCommands = new InitCommands(cfg, folderRoot, persistentRoot);
        } catch (error) {
            logger.error("Failed to read workspace agent config file: %s", error);
            throw error;
        }
    } else if (instruction) {
        startingNodes = makeMessageNodes(instruction);
        logger.info("Using instruction from command line: %s", instruction);
    } else {
        throw new Error("No starting chat nodes specified.");
    }
    logger.info("Starting beachhead process at root: %s", folderRoot);

    // TODO(liornm): The magic string "SETUP_MODE" is a hack before we have a special flag for setup mode.
    const setupMode =
        args["setup-mode"] ||
        (startingNodes.length === 1 &&
            startingNodes[0].text_node?.content.startsWith("SETUP_MODE")) ||
        false;

    // TODO(mpauly): We need a more robust way to get and store the config, api token, and feature flags

    // Use a mock config listener, auth session store, and feature flags for now to avoid bringing in too many complex dependencies
    const config = {
        config: {
            apiToken: apiToken,
            completionURL: apiUrl,
            chat: {
                url: "",
            },
            // NOTE(mpauly): Though we don't use nextEdit, we need to provide these urls fields as
            // they are expected by the APIServer for some endpoints (e.g. checkpointBlobs)
            nextEdit: {
                url: "",
                locationUrl: "",
                generationUrl: "",
            },
        },
    } as AugmentConfigListener;
    const auth: AuthSessionStore = {
        useOAuth: false,
        getSession: () => {
            return Promise.resolve({
                accessToken: apiToken,
                scopes: [],
                tenantURL: apiUrl,
            });
        },
        removeSession: () => {
            return Promise.resolve();
        },
    };

    // Enforce agent_id presence
    if (!remoteAgentId) {
        if (instruction) {
            // Only generate a random UUID with a warning if instruction is provided (during test)
            remoteAgentId = uuidv4();
            logger.warn(
                "WARNING: No agent_id provided. Generated random UUID for testing: %s",
                remoteAgentId
            );
        } else {
            // Otherwise throw an error and fail
            throw new Error("agent_id must be present in workspace-agent-config");
        }
    }

    // Use the remote agent ID as the session ID
    const sessionId = remoteAgentId;
    logger.info("Using remote agent ID as session ID: %s", sessionId);

    const imageHash = args["image-hash"] || "0.0.0";
    const userAgent = `beachhead/${imageHash}`;

    // Initialize the API server
    const apiServer = new APIServerImpl(config, auth, sessionId, userAgent, global.fetch);

    // Get live feature flags from the API
    let featureFlags: FeatureFlags;
    let clientFeatureFlags: IClientFeatureFlags;
    try {
        logger.info("Fetching live feature flags from get-models API...");
        const modelConfig = await startupTimer.asyncEvent(
            "apiServer.getModelConfig",
            apiServer.getModelConfig()
        );
        const result = convertToFeatureFlags(modelConfig);
        featureFlags = result.featureFlags;
        clientFeatureFlags = result.clientFeatureFlags;
        logger.info("Successfully loaded live feature flags");
    } catch (error) {
        logger.warn("Failed to fetch live feature flags, using defaults: %s", error);
        // Fallback to default feature flags if API call fails
        featureFlags = defaultFeatureFlags;
        clientFeatureFlags = createClientFeatureFlags(featureFlags);
    }

    const sshManager = new SSHManager(
        persistentRoot ? path.join(persistentRoot, "..", "ssh-host-keys") : ""
    );
    const sshConnectionFinder = new SSHConnectionFinder(sshPort, featureFlags);

    // Start the SSH connection finder in the background
    void sshConnectionFinder.startBackgroundMonitoring();
    // Initialize Sentry with the feature flag, agent ID, tenant ID, and model ID
    initializeSentry(featureFlags, remoteAgentId, modelId);

    const workspaceManager = new BeachheadWorkspaceManagerImpl(folderRoot, apiServer, featureFlags);

    // Initialize the client interfaces
    setLibraryClientWorkspaces(new BeachheadClientWorkspaces(workspaceManager));
    setLibraryAPIClient(new SidecarAPIClient(apiServer, workspaceManager));
    setLibraryClientFeatureFlags(clientFeatureFlags);

    // Setup checkpoint manager
    // TODO(AU-8675): Integrate the checkpointing implementation from the beachhead workspace
    // manager to work with the sidecar aggregate checkpoint manager
    const mockShardStorage: ShardedStorage<SerializedStore> = {
        save: () => Promise.resolve(),
        load: () => Promise.resolve(undefined),
        saveShard: () => Promise.resolve(),
        loadShard: () => Promise.resolve(undefined),
        saveManifest: () => Promise.resolve(),
        loadManifest: () => Promise.resolve(undefined),
        deleteShard: () => Promise.resolve(),
    } as unknown as ShardedStorage<SerializedStore>;
    const checkpointManager = new AggregateCheckpointManager(
        mockShardStorage,
        () => undefined, // getAgentMemoriesAbsPath
        () => ({ dispose: () => { } }), // onDocumentChange
        () => ({ dispose: () => { } }), // onFileDeleted
        () => ({ dispose: () => { } }) // onFileDidMove
    );

    // Setup tools model
    const setupModeTools = [SidecarToolType.codebaseRetrieval];
    const beachheadUnsupportedSidecarTools = setupMode
        ? new Set(Object.values(SidecarToolType).filter((tool) => !setupModeTools.includes(tool)))
        : new Set([SidecarToolType.remember]);

    // Create a tool use request event reporter
    const toolUseRequestEventReporter = new ToolUseRequestEventReporter();
    toolUseRequestEventReporter.enableUpload(); // Enable upload
    logger.info("Tool use request event reporter initialized");

    const toolsModel = new ToolsModel(
        mcpServers,
        beachheadToolHostFactory(workspaceManager.workspaceRoot, setupMode),
        new BeachheadRemoteInfo(apiServer, setupMode),
        (details: ToolStartupError) => {
            logger.error("Tool startup error: %s", JSON.stringify(details));
        },
        clientFeatureFlags,
        checkpointManager,
        () => Promise.resolve(agentMemories),
        () => undefined, // getMemoriesAbsPath
        () => toolUseRequestEventReporter, // Return the tool use request event reporter
        {
            unsupportedSidecarTools: beachheadUnsupportedSidecarTools,
        }
    );
    toolsModel.setMode(ChatMode.remoteAgent);

    // Initialize state persistence directory
    let statePersistence: StatePersistence | undefined;
    let initialState: AgentState | undefined;
    let isResuming = false;
    if (persistentRoot) {
        const stateDir = path.join(persistentRoot, "agent_state");
        statePersistence = new StatePersistence(stateDir);
        logger.info("Using state persistence directory: %s", stateDir);
        // check and resume from state on disk
        const savedStateExists = statePersistence.checkSavedStateExists();
        if (savedStateExists) {
            isResuming = true;
            try {
                initialState = await startupTimer.asyncEvent(
                    "statePersistence.loadLatestAgentState",
                    statePersistence.loadLatestAgentState()
                );
            } catch (error) {
                logger.error("Failed to load state: %s", error);
                throw error;
            }
            logger.info("Resuming from last saved state");

            // NOTE(mpauly): We set the startingNodes as an empty list to signal to the agent loop
            // that it should act based on the current state. If it detects that we're resuming
            // from a crash, it will inject a response informing the user of the crash and wait for
            // further input.
            startingNodes = [];
        } else {
            logger.info("No previous state found. Starting from an empty state.");
            initialState = new AgentState(
                remoteAgentId,
                userGuidelines,
                workspaceGuidelines,
                agentMemories,
                modelId
            );
        }
    } else {
        logger.warn("No persistent root specified. State will not be persisted.");
        initialState = new AgentState(
            remoteAgentId,
            userGuidelines,
            workspaceGuidelines,
            agentMemories
        );
    }

    if (setupMode && !isResuming) {
        const setupScriptPrompt = `
            Follow the following steps carefully:
            1. Thoroughly analyze the codebase to identify languages, package managers, and build systems.
            2. Look for configuration files, build files, docker files, and documentation.
            3. Determine the build process, dependencies, and tools used in the codebase.
            4. Find the commands used to run unit tests, and if you cannot find any unit test commands, ask me to provide them.
            5. Use setup-script tool based on you findings.
            6. If the setup-script returns an error fix the error and run the setup-script tool again.
            7. Repeat steps 5-6 until the unit tests pass.

            Examples of valid test_commands in the setup-script tool:
            - npm test --test-path-pattern=src/
            - pytest tests/
            - go test ./...
            - dotnet test --filter FullyQualifiedNameOfTestClass
            - bazel test //services/test:all
            - mvn test -Dtest=FullyQualifiedNameOfTestClass

            Guidelines:
            - The setup script must be written in bash.
            - Installed executables must be added to PATH in the user's $HOME/.profile file.
            - If you create a virtual environment, add the command to activate it to the user's $HOME/.profile file.
            - For system-wide configurations (only if absolutely necessary), use 'sudo tee -a /etc/profile'.
            - Test commands are automatically executed after the setup script.
            - Therefore, you must NEVER include test commands in the setup script content.
            - The test commands must execute unit tests from the codebase successfully.
            - You must NEVER use the \`||\` operator in test commands.
            - You must NEVER use absolute paths in test commands, always assume command is in PATH.
            - You must NEVER \`cd\` into directories in the test commands.
            - Follow the instructions carefully, and never deviate from them.
            - You can use the retrieval tool to find file paths.
            - Each setup-script invocation runs in a completely fresh environment
            - Previous setup-script installations and configurations are NOT preserved between calls
            - Include ALL necessary setup steps in EACH script, even if they were included in previous scripts
            - Test commands will only have access to tools and dependencies installed in the current setup-script execution
            - You must NEVER describe, repeat or mention the provided steps.

            General information:
            - The operating system is Linux Ubuntu.
            - The codebase is located in the current working directory (CWD) at /mnt/persist/workspace.
            - The setup script is executed using Bash.
            - Test commands are executed in a separate Bash shell.
            - The setup script is executed with user privileges (use \`sudo\` for commands that require it).
        `;
        startingNodes = makeMessageNodes(setupScriptPrompt);
        logger.info("Using setup script prompt instead of provided instruction");
    }

    const agentLoop = new AgentLoop(
        apiServer,
        toolsModel,
        workspaceManager,
        streamingChatHistory,
        remoteAgentId,
        sshConnectionFinder,
        featureFlags,
        statePersistence,
        initialState
    );
    // Report this as early as we can
    await startupTimer.asyncEvent("agentLoop.reportAlive", agentLoop.reportAlive());

    const workspaceSetupStatusTracker = new WorkspaceSetupStatusTracker(initCommands);
    workspaceSetupStatusTracker.run_report_status_logs_loop(apiServer, sessionId);

    // Start the http server before blocking on slow initialization steps (init commands, workspace manager sync)
    if (port > 0) {
        startServer(port, agentLoop, workspaceSetupStatusTracker, sshManager);
    }
    // Run the initialization commands
    if (initCommands) {
        await startupTimer.asyncEvent("initCommands.run", initCommands.run());

        // Check for specific init command failures and handle them appropriately
        for (const command of initCommands.commands()) {
            if (command.status() === CommandStatus.FAILED) {
                const commandName = command.name();
                logger.error(`Init command '${commandName}' failed`);

                if (commandName === "git clone" || commandName === "git apply") {
                    logger.error("Critical git operation failed, exiting beachhead process");
                    process.exit(1);
                } else {
                    logger.warn(
                        `Init command '${commandName}' failed, continuing with workspace setup`
                    );
                }
            }
        }
    }

    // Run at the the workspace root. This isn't critical but will be more obvious if the agent creates
    // unintentional files relative to itself.
    process.chdir(folderRoot);

    // Start the workspace manager and wait for initial indexing to complete
    await startupTimer.asyncEvent("workspaceManager.initialize", workspaceManager.initialize());
    logger.info("Initial workspace indexing complete");

    logger.info("Startup complete, times: %s", startupTimer.getEventTimesKVString());

    try {
        // Start the agent loop, running in one-shot mode if no port is specified
        if (port > 0) {
            await agentLoop.runLoop(startingNodes);
        } else {
            const eventTimer = new EventTimer();
            await agentLoop.run(startingNodes, eventTimer);
            logger.info(
                "Agent loop completed one iteration, event times: %s",
                eventTimer.getEventTimesKVString()
            );
        }
    } catch (error) {
        logger.error("Agent loop failed: %s", error);

        // Capture the error with Sentry
        if (featureFlags.beachheadEnableSentry && error instanceof Error) {
            captureSentryException(error, "error", sessionId);
        }

        process.exit(1);
    }

    workspaceManager.dispose();
    // TODO(mpauly): Why is this not exiting? I think there's something else that needs to be disposed
    process.exit(0);
}

void main();
