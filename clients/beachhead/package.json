{"scripts": {"start": "pnpm run build-dev && node --enable-source-maps out/index.js", "build": "pnpm run clean && pnpm run esbuild", "build-dev": "pnpm run clean && pnpm run esbuild-dev", "clean": "rm -rf out", "esbuild": "esbuild src/index.ts --bundle --platform=node --tsconfig=./tsconfig.json --outfile=./out/index.js", "esbuild-dev": "esbuild src/index.ts --bundle --platform=node --tsconfig=./tsconfig.json --outfile=./out/index.js --sourcemap", "lint": "pnpm run eslint && pnpm run prettier", "eslint": "git ls-files -- . | xargs pre-commit run eslint --hook-stage=manual --files", "prettier": "git ls-files -- . | xargs pre-commit run prettier --hook-stage=manual --files", "test": "jest --config ./jest.config.js"}, "dependencies": {"@sentry/node": "^9.20.0", "denque": "^2.1.0", "yargs": "^17.7.2"}, "devDependencies": {"@types/express": "4.16", "@types/jest": "^29.5.11", "@types/lodash": "^4.17.16", "@types/node": "^22.13.9", "@types/yargs": "^17.0.32", "esbuild": "^0.25.0", "express": "^4.21.2", "jest-cli": "^29.7.0", "jest-junit": "^15.0.0", "jest": "^29.7.0", "lodash": "^4.17.21", "monaco-editor": "^0.52.2", "ts-jest": "^29.1.2", "uuid": "^11.0.3", "vscode-uri": "^3.0.8", "winston": "^3.11.0", "zod": "^3.23.8"}}