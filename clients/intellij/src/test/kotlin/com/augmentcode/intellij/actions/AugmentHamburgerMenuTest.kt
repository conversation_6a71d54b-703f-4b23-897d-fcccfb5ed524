package com.augmentcode.intellij.actions

import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.pluginstate.PluginStateService
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.intellij.openapi.actionSystem.ActionManager
import com.intellij.testFramework.TestActionEvent
import com.intellij.testFramework.registerOrReplaceServiceInstance
import com.intellij.util.application
import io.mockk.every
import io.mockk.mockk
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4

@RunWith(JUnit4::class)
class AugmentHamburgerMenuTest : AugmentBasePlatformTestCase() {
  private lateinit var mockPluginStateService: PluginStateService

  @Test
  fun testHamburgerMenuContainsExpectedActions() {
    // Mock PluginStateService to return SIGN_IN_REQUIRED state (signed out)
    mockPluginStateService = mockk(relaxed = true)
    every { mockPluginStateService.state } returns PluginState.SIGN_IN_REQUIRED
    every { mockPluginStateService.context } returns null

    application.registerOrReplaceServiceInstance(
      PluginStateService::class.java,
      mockPluginStateService,
      testRootDisposable,
    )

    val hamburgerMenu = AugmentHamburgerMenu(project)
    val event = TestActionEvent.createTestEvent()

    val actions = hamburgerMenu.getChildren(event)
    val actionIds =
      actions.map { action ->
        ActionManager.getInstance().getId(action) ?: action.javaClass.simpleName
      }

    // Test actions that should always be present regardless of sign-in state
    assertTrue(
      "Menu should contain ShowHelpAction",
      actionIds.contains("com.augmentcode.intellij.actions.ShowHelpAction"),
    )
    assertTrue(
      "Menu should contain ExtensionStatusAction",
      actionIds.contains("com.augmentcode.intellij.actions.ExtensionStatusAction"),
    )
    // When signed out, should contain SignInAction
    assertTrue(
      "Menu should contain SignInAction when signed out",
      actionIds.contains("com.augmentcode.intellij.actions.SignInAction"),
    )
  }

  @Test
  fun testHamburgerMenuSignedInActions() {
    // Mock PluginStateService to return ENABLED state
    mockPluginStateService = mockk(relaxed = true)
    every { mockPluginStateService.state } returns PluginState.ENABLED
    every { mockPluginStateService.context } returns null

    application.registerOrReplaceServiceInstance(
      PluginStateService::class.java,
      mockPluginStateService,
      testRootDisposable,
    )

    val hamburgerMenu = AugmentHamburgerMenu(project)
    val event = TestActionEvent.createTestEvent()

    val actions = hamburgerMenu.getChildren(event)
    val actionIds =
      actions.map { action ->
        ActionManager.getInstance().getId(action) ?: action.javaClass.simpleName
      }

    assertTrue(
      "Menu should contain ToggleCompletionsAction when signed in",
      actionIds.contains("com.augmentcode.intellij.actions.ToggleCompletionsAction"),
    )
    assertTrue(
      "Menu should contain ManageAccountAction when signed in",
      actionIds.contains("com.augmentcode.intellij.actions.ManageAccountAction"),
    )
    assertTrue(
      "Menu should contain ReindexAction when signed in",
      actionIds.contains("com.augmentcode.intellij.actions.ReindexAction"),
    )
    assertTrue(
      "Menu should contain SignOutAction when signed in",
      actionIds.contains("com.augmentcode.intellij.actions.SignOutAction"),
    )
  }

  @Test
  fun testHamburgerMenuSignedOutActions() {
    // Mock PluginStateService to return SIGN_IN_REQUIRED state
    mockPluginStateService = mockk(relaxed = true)
    every { mockPluginStateService.state } returns PluginState.SIGN_IN_REQUIRED
    every { mockPluginStateService.context } returns null

    application.registerOrReplaceServiceInstance(
      PluginStateService::class.java,
      mockPluginStateService,
      testRootDisposable,
    )

    val hamburgerMenu = AugmentHamburgerMenu(project)
    val event = TestActionEvent.createTestEvent()

    val actions = hamburgerMenu.getChildren(event)
    val actionIds =
      actions.map { action ->
        ActionManager.getInstance().getId(action) ?: action.javaClass.simpleName
      }

    assertTrue(
      "Menu should contain SignInAction when not signed in",
      actionIds.contains("com.augmentcode.intellij.actions.SignInAction"),
    )
    assertFalse(
      "Menu should not contain ToggleCompletionsAction when not signed in",
      actionIds.contains("com.augmentcode.intellij.actions.ToggleCompletionsAction"),
    )
    assertFalse(
      "Menu should not contain ReindexAction when not signed in",
      actionIds.contains("com.augmentcode.intellij.actions.ReindexAction"),
    )
  }

  override fun tearDown() {
    super.tearDown()
  }
}
