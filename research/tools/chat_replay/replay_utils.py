import dataclasses
import json
import re
import sys
import time
from collections import defaultdict
from copy import deepcopy
from typing import Collection
from dataclasses import dataclass

from anthropic import NOT_GIVEN
from google.cloud import storage
from google.genai.types import (
    Content,
    Part,
)

from base.datasets.gcp_creds import get_gcp_creds
from base.datasets.gcs_blob_cache import GCSBlobCache, GCSCheckpointCache
from base.datasets.gcs_client import GCSRequestInsightFetcher
from base.datasets.tenants import DOGFOOD_SHARD, DatasetTenant, get_tenant
from base.prompt_format.common import (
    ChatRequestNode,
    ChatRequestNodeType,
    ChatRequestText,
    ChatRequestToolResult,
    ChatResultNode,
    ChatResultNodeType,
    ChatResultToolUse,
    Exchange,
    PersonaType,
    PromptChunk,
)
from base.prompt_format_chat import get_structured_chat_prompt_formatter_by_name
from base.prompt_format_chat.lib.token_counter_claude import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from base.prompt_format_chat.prompt_formatter import (
    Chat<PERSON>okenApportionment,
    StructuredChatPromptOutput,
)
from base.third_party_clients.anthropic_direct_client import AnthropicDirectClient
from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient
from base.third_party_clients.google_genai_client import GoogleGenaiClient
from base.third_party_clients.third_party_model_client import (
    EndOfStream,
    ToolChoice,
    ToolDefinition,
)
from research.core.artifacts import collect_artifacts
from research.core.chat_prompt_input import ResearchChatPromptInput
from research.core.constants import GCP_PROJECT_ID, GCP_VERTEX_REGION
from research.core.types import Document
from research.environments import get_eng_secret
from research.eval.harness.systems.abs_system import ChatResult
from research.eval.harness.systems.remote_chat_system import RemoteChatSystem
from services.chat_host import chat_pb2
from services.chat_host.chat_proto_util import convert_history, request_to_message
from services.chat_host.server.chat_request_insight_builder import (
    _stringify_request_message,
    _stringify_response_message,
)
from services.chat_host.server.utils import (
    convert_changed_file_stats_from_proto,
)
from services.request_insight import request_insight_pb2

TEMPERAURE = 0

MAX_OUTPUT_TOKENS: dict[str, int] = defaultdict(
    lambda: 1024 * 8,
    {
        "haiku3": 1024 * 4,
    },
)
VERTEX_AI_MODEL_NAMES = {
    "sonnet3.5": "claude-3-5-sonnet@20240620",
    "sonnet3.5-v2": "claude-3-5-sonnet-v2@20241022",
    "sonnet3.7": "claude-3-7-sonnet@20250219",
}
ANTHROPIC_MODEL_NAMES = {
    "sonnet3.5": "claude-3-5-sonnet-20240620",
    "sonnet3.5-v2": "claude-3-5-sonnet-20241022",
    "sonnet3.7": "claude-3-7-sonnet-20250219",
    "sonnet4.0": "claude-sonnet-4-0",
    "haiku3": "claude-3-haiku-20240307",
    "haiku3.5": "claude-3-5-haiku-20241022",
}
GOOGLE_GENAI_MODEL_NAMES = {
    "gemini2.5": "gemini-2.5-pro-preview-03-25",
    "gemini2.5-0506": "gemini-2.5-pro-preview-05-06",
    "gemini2.5-flash": "gemini-2.5-flash-preview-05-20",
}
_VERTEX_AI_CLIENTS: dict[str, AnthropicVertexAiClient] = {}
_ANTHROPIC_CLIENTS: dict[str, AnthropicDirectClient] = {}
_GOOGLE_GENAI_CLIENTS: dict[str, GoogleGenaiClient] = {}

TOKEN_APPORTIONMENT = ChatTokenApportionment(
    prefix_len=1024 * 2,
    suffix_len=1024 * 2,
    path_len=256,
    message_len=-1,  # Deprecated field
    selected_code_len=-1,  # Deprecated field
    chat_history_len=1024 * 4,
    retrieval_len_per_each_user_guided_file=2000,
    retrieval_len_for_user_guided=3000,
    retrieval_len=-1,  # Fill the rest of the input prompt with retrievals
    max_prompt_len=1024 * 12,  # 12k for prompt
    inject_current_file_into_retrievals=True,
    tool_results_len=1024 * 32,  # 32k for tool results
)
TOKEN_COUNTER = ClaudeTokenCounter()
TOOL_DEFINITIONS = [
    ToolDefinition(
        name="save-file",
        description="Save a file. Use this tool to create new files.  It cannot modify existing files.",
        input_schema_json='{"type":"object","properties":{"file_path":{"type":"string","description":"The path of the file to save."},"file_content":{"type":"string","description":"The content of the file to save."},"add_last_line_newline":{"type":"boolean","description":"Whether to add a newline at the end of the file (default: true)."}},"required":["file_path","file_content"]}',
    ),
    ToolDefinition(
        name="read-file",
        description="Read a file.",
        input_schema_json='{"type":"object","properties":{"file_path":{"type":"string","description":"The path of the file to read."}},"required":["file_path"]}',
    ),
    ToolDefinition(
        name="edit-file",
        description="\nEdit a file. Accepts a file path and a description of the edit.\nThis tool can edit whole files.\nThe description should be detailed and precise, and include all required information to perform the edit.\nIt can include both natural language and code. It can include multiple code snippets to described different\nedits in the file. It can include descriptions of how to perform these edits precisely.\n\nAll the contents that should go in a file should be placed in a markdown code block, like this:\n\n<begin-example>\nAdd a function called foo.\n\n```\ndef foo():\n    ...\n```\n</end-example>\n\nThis includes all contents, even if it's not code.\n\nBe precise or I will take away your toys.\n\nPrefer to use this tool when editing parts of a file.\n",
        input_schema_json='{"type":"object","properties":{"file_path":{"type":"string","description":"The path of the file to edit."},"edit_summary":{"type":"string","description":"A brief description of the edit to be made. 1-2 sentences."},"detailed_edit_description":{"type":"string","description":"A detailed and precise description of the edit. Can include natural language and code snippets."}},"required":["file_path","edit_summary","detailed_edit_description"]}',
    ),
    ToolDefinition(
        name="remember",
        description="Call this tool when user asks you:\n- to remember something\n- to create memory/memories\n\nUse this tool only with information that can be useful in the long-term.\nDo not use this tool for temporary information.\n",
        input_schema_json='{"type":"object","properties":{"memory":{"type":"string","description":"The concise (1 sentence) memory to remember."}},"required":["memory"]}',
    ),
    ToolDefinition(
        name="open-browser",
        description="Open a URL in the default browser.",
        input_schema_json='{"type":"object","properties":{"url":{"type":"string","description":"The URL to open in the browser."}},"required":["url"]}',
    ),
    ToolDefinition(
        name="codebase-retrieval",
        description="Use this tool to request information from the codebase.\nIt will return relevant snippets for the requested information.",
        input_schema_json='{"type":"object","properties":{"information_request":{"type":"string","description":"A description of the information you need."}},"required":["information_request"]}',
    ),
    ToolDefinition(
        name="launch-process",
        description="Launch a new process.\nIf wait is specified, waits up to that many seconds for the process to complete.\nIf the process completes within wait seconds, returns its output.\nIf it doesn't complete within wait seconds, returns partial output and process ID.\nIf wait is not specified, returns immediately with just the process ID.\nThe process's stdin is always enabled, so you can use write_process to send input if needed.",
        input_schema_json='{"type":"object","properties":{"command":{"type":"string","description":"The shell command to execute"},"wait":{"type":"number","description":"Optional: number of seconds to wait for the command to complete."},"cwd":{"type":"string","description":"Working directory for the command. If not supplied, uses the current working directory."}},"required":["command"]}',
    ),
    ToolDefinition(
        name="kill-process",
        description="Kill a process by its process ID.",
        input_schema_json='{"type":"object","properties":{"process_id":{"type":"integer","description":"Process ID to kill."}},"required":["process_id"]}',
    ),
    ToolDefinition(
        name="read-process",
        description="Read output from a running process.",
        input_schema_json='{"type":"object","properties":{"process_id":{"type":"integer","description":"Process ID to read from."}},"required":["process_id"]}',
    ),
    ToolDefinition(
        name="write-process",
        description="Write input to a process's stdin.",
        input_schema_json='{"type":"object","properties":{"process_id":{"type":"integer","description":"Process ID to write to."},"input_text":{"type":"string","description":"Text to write to the process\'s stdin."}},"required":["process_id","input_text"]}',
    ),
    ToolDefinition(
        name="list-processes",
        description="List all known processes and their states.",
        input_schema_json='{"type":"object","properties":{},"required":[]}',
    ),
    ToolDefinition(
        name="wait-process",
        description="Wait for a process to complete or timeout.",
        input_schema_json='{"type":"object","properties":{"process_id":{"type":"integer","description":"Process ID to wait for."},"wait":{"type":"number","description":"Number of seconds to wait for the process to complete."}},"required":["process_id","wait"]}',
    ),
    ToolDefinition(
        name="web-search",
        description="Search the web for information. Returns results in markdown format.\nEach result includes the URL, title, and a snippet from the page if available.\n\nThis tool uses Google's Custom Search API to find relevant web pages.",
        input_schema_json='{"description": "Input schema for the web search tool.", "properties": {"query": {"description": "The search query to send.", "title": "Query", "type": "string"}, "num_results": {"default": 5, "description": "Number of results to return", "maximum": 10, "minimum": 1, "title": "Num Results", "type": "integer"}}, "required": ["query"], "title": "WebSearchInput", "type": "object"}',
    ),
    ToolDefinition(
        name="shell",
        description="Execute a shell command. The OS is win32. The shell is 'powershell'.",
        input_schema_json='{"type":"object","properties":{"command":{"type":"string","description":"The shell command to execute."}},"required":["command"]}',
    ),
    ToolDefinition(
        name="web-fetch",
        description="Fetches data from a webpage and converts it into Markdown.",
        input_schema_json='{"type":"object","properties":{"url":{"type":"string","description":"The URL to fetch."}},"required":["url"]}',
    ),
]


def get_attrs(object_):
    attributes = []
    for attr in dir(object_):
        if not attr.startswith("_"):
            attributes.append(attr)
    return attributes


def get_third_party_client(base_model_version="sonnet3.5-v2", client_type="vertex_ai"):
    if client_type == "vertex_ai":
        model_name = VERTEX_AI_MODEL_NAMES[base_model_version]
        if base_model_version not in _VERTEX_AI_CLIENTS:
            _VERTEX_AI_CLIENTS[base_model_version] = AnthropicVertexAiClient(
                project_id=GCP_PROJECT_ID,
                region=GCP_VERTEX_REGION,
                model_name=model_name,
                temperature=TEMPERAURE,
                max_output_tokens=MAX_OUTPUT_TOKENS[base_model_version],
            )
        return _VERTEX_AI_CLIENTS[base_model_version], model_name
    elif client_type == "anthropic":
        model_name = ANTHROPIC_MODEL_NAMES[base_model_version]
        if base_model_version not in _ANTHROPIC_CLIENTS:
            _ANTHROPIC_CLIENTS[base_model_version] = AnthropicDirectClient(
                api_key=get_eng_secret("seal-research-anthropic-key"),
                model_name=model_name,
                temperature=TEMPERAURE,
                max_output_tokens=MAX_OUTPUT_TOKENS[base_model_version],
            )
        return _ANTHROPIC_CLIENTS[base_model_version], model_name
    elif client_type == "google_genai":
        model_name = GOOGLE_GENAI_MODEL_NAMES[base_model_version]
        if base_model_version not in _GOOGLE_GENAI_CLIENTS:
            _GOOGLE_GENAI_CLIENTS[base_model_version] = GoogleGenaiClient(
                project_id=GCP_PROJECT_ID,
                region="us-central1",
                model_name=model_name,
                temperature=TEMPERAURE,
                max_output_tokens=MAX_OUTPUT_TOKENS[base_model_version],
            )
        return _GOOGLE_GENAI_CLIENTS[base_model_version], model_name
    else:
        raise ValueError(f"Unknown client type: {client_type}")


@dataclass
class RunModelResult:
    response_nodes: list[ChatResultNode]
    end_of_stream: EndOfStream | None = None


def run_model_v2(
    prompt_output: StructuredChatPromptOutput,
    system_prompt: str | None = None,
    base_model_version="sonnet3.5-v2",
    client_type="vertex_ai",
    retry_limit=1,
    use_caching=False,
    temperature: float = TEMPERAURE,
    tools: list[str] = [],
    tool_definitions: list[ToolDefinition] | None = None,
    tool_choice: ToolChoice | None = None,
    prefill: str | None = None,
    yield_final_parameters: bool = False,
) -> RunModelResult:
    third_party_client, _ = get_third_party_client(base_model_version, client_type)
    response = None
    response_nodes: list[ChatResultNode] = []
    end_of_stream: EndOfStream | None = None
    if prompt_output.tools:
        tools.extend(prompt_output.tools)
        tools = list(set(tools))
    for retry in range(retry_limit):
        try:
            response_nodes = []
            response_stream = third_party_client.generate_response_stream(
                model_caller="research-replay",
                system_prompt=system_prompt or prompt_output.system_prompt,
                cur_message=prompt_output.message,
                chat_history=list(prompt_output.chat_history),
                tools=tools,
                tool_definitions=tool_definitions or [],
                tool_choice=tool_choice,
                prefill=prefill,
                temperature=temperature,
                use_caching=use_caching,
                max_output_tokens=MAX_OUTPUT_TOKENS[base_model_version],
                yield_final_parameters=yield_final_parameters,
            )

            # Streaming call generates many short sequences of text; for our purposes,
            # we greatly prefer these be concatenated into larger nodes.
            text_buffer = []

            def add_text_node():
                if not text_buffer:
                    return
                response_nodes.append(
                    ChatResultNode(
                        type=ChatResultNodeType.RAW_RESPONSE,
                        content="".join(text_buffer),
                        tool_use=None,
                        id=len(response_nodes),
                    )
                )
                text_buffer.clear()

            for response in response_stream:
                if response.final_parameters:
                    response_nodes.append(
                        ChatResultNode(
                            type=ChatResultNodeType.FINAL_PARAMETERS,
                            content="",
                            tool_use=None,
                            id=len(response_nodes),
                            final_parameters=response.final_parameters,
                        )
                    )
                if response.tool_use is not None:
                    add_text_node()
                    response_nodes.append(
                        ChatResultNode(
                            type=ChatResultNodeType.TOOL_USE,
                            content="",
                            tool_use=ChatResultToolUse(
                                name=response.tool_use.tool_name,
                                input=response.tool_use.input,
                                tool_use_id=response.tool_use.tool_use_id,
                            ),
                            id=len(response_nodes),
                        )
                    )
                elif response.text:
                    text_buffer.append(response.text)

                if response.end_of_stream:
                    end_of_stream = response.end_of_stream

            add_text_node()
            return RunModelResult(response_nodes, end_of_stream)
        except Exception as e:
            print(f"Third-party error ({type(e)}): {e}")
            if retry == retry_limit - 1:
                return RunModelResult(response_nodes, end_of_stream)
            time.sleep(60)

    assert False, "Unreachable"


def run_model(
    prompt_output: StructuredChatPromptOutput,
    system_prompt: str | None = None,
    base_model_version="sonnet3.5-v2",
    client_type="vertex_ai",
    retry_limit=1,
    use_caching=False,
    temperature: float = TEMPERAURE,
    tools: list[str] = [],
    tool_definitions: list[ToolDefinition] | None = None,
    tool_choice: ToolChoice | None = None,
    prefill: str | None = None,
    yield_final_parameters: bool = False,
) -> list[ChatResultNode]:
    return run_model_v2(
        prompt_output,
        system_prompt,
        base_model_version,
        client_type,
        retry_limit,
        use_caching,
        temperature,
        tools,
        tool_definitions,
        tool_choice,
        prefill,
        yield_final_parameters,
    ).response_nodes


def get_caches(tenant):
    gcp_creds, _ = get_gcp_creds(None)
    storage_client = storage.Client(project=tenant.project_id, credentials=gcp_creds)
    blob_bucket = storage_client.bucket(tenant.blob_bucket_name)
    blob_cache_size_bytes = 2**30
    blob_cache_num_threads = 64
    blob_cache = GCSBlobCache(
        blob_bucket,
        tenant.blob_bucket_prefix,
        max_size_bytes=blob_cache_size_bytes,
        num_threads=blob_cache_num_threads,
    )
    checkpoint_bucket = storage_client.bucket(tenant.checkpoint_bucket_name)
    checkpoint_cache = GCSCheckpointCache(
        checkpoint_bucket,
        tenant.checkpoint_bucket_prefix,
        blob_cache_size_bytes,
        num_threads=blob_cache_num_threads,
    )
    return blob_cache, checkpoint_cache


def build_retrievals(request) -> list[PromptChunk]:
    prompt_chunks = []
    for chunk in request.retrieved_chunks:
        char_offset = chunk.char_offset
        chunk_index = str(chunk.chunk_index)
        prompt_chunk = PromptChunk(
            text=chunk.text,
            path=chunk.path,
            unique_id=chunk.blob_name + "-" + chunk_index,
            origin=chunk.origin,
            char_start=char_offset,
            char_end=chunk.char_end,
            blob_name=chunk.blob_name,
        )
        prompt_chunks.append(prompt_chunk)
    return prompt_chunks


class MultipleEventsFound(Exception):
    """Raised when some fields exceed the maximum allowed length."""

    def __init__(self, request_id: str, event_type: str):
        self.message = f"Multiple {event_type} events for {request_id}"
        super().__init__(self.message)


class BlobGetter:
    def __init__(self, tenant: DatasetTenant = DOGFOOD_SHARD):
        self.tenant = tenant
        self.blob_cache, self.checkpoint_cache = get_caches(tenant)
        self.request_fetcher = GCSRequestInsightFetcher.from_tenant(tenant)

    def get_request_event(
        self,
        request_id: str,
        event_type: str,
        raise_on_multiple: bool = True,
    ):
        request_info = self.request_fetcher.get_request(
            request_id=request_id,
        )
        events = [
            getattr(event, event_type)  # type: ignore
            for event in request_info.events
            if event.HasField(event_type)  # type: ignore
        ]
        if len(events) == 0:
            return None
        elif len(events) > 1:
            if raise_on_multiple:
                raise MultipleEventsFound(request_id, event_type)
            else:
                # Return the last event when multiple exist.
                # This handles cases like chat_host_request where an initial request
                # might have failed, triggering a fallback to another model.
                return events[-1]
        else:
            return events[0]

    def get_raw_chat_request(
        self,
        request_id: str,
        raise_on_multiple: bool = True,
    ) -> request_insight_pb2.RIChatRequest | None:
        event = self.get_request_event(
            request_id, "chat_host_request", raise_on_multiple
        )
        if event is None:
            return None
        assert isinstance(event, request_insight_pb2.RIChatRequest)
        return event

    def get_blob_names_from_chat_request(self, request: chat_pb2.ChatRequest):
        blob_object_list = request.blobs
        if len(blob_object_list) == 0:
            return []
        assert len(blob_object_list) == 1, len(blob_object_list)
        blob_object_dict = blob_object_list[0]
        checkpoint_id = blob_object_dict.baseline_checkpoint_id
        if checkpoint_id:
            checkpoint_content = self.checkpoint_cache.get([checkpoint_id])[0]  # type: ignore
            if checkpoint_content:
                checkpoint_blob_names = set(checkpoint_content.blob_names)
            else:
                checkpoint_blob_names = set()
        else:
            checkpoint_blob_names = set()
        added_blob_names = set([blob.hex() for blob in blob_object_dict.added])
        deleted_blob_names = set([blob.hex() for blob in blob_object_dict.deleted])
        blob_names = sorted(
            list((added_blob_names | checkpoint_blob_names) - deleted_blob_names)
        )
        return blob_names

    def get_blobs(self, blob_names: Collection[str]):
        blobs = self.blob_cache.get(blob_names)
        documents = [
            Document(id=blob_name, text=blob.content, path=str(blob.path))
            for blob_name, blob in zip(blob_names, blobs)
            if blob is not None
        ]
        return documents

    def get_chat_request(
        self, request_id, raise_on_multiple: bool = True
    ) -> ResearchChatPromptInput | None:
        """Get the chat request (without the retrieved chunks)."""
        ri_request = self.get_raw_chat_request(request_id, raise_on_multiple)
        if ri_request is None:
            return None
        request: chat_pb2.ChatRequest = ri_request.request

        if request.external_source_ids:
            external_source_ids = list(request.external_source_ids)
        else:
            external_source_ids = []

        tool_definitions = [
            ToolDefinition(
                tool.name,
                tool.description,
                tool.input_schema_json,
            )
            for tool in request.tool_definitions
        ]

        chat_prompt_input = ResearchChatPromptInput(
            message=request_to_message(request.message, request.nodes),
            path=request.path,
            prefix=request.prefix,
            selected_code=request.selected_code,
            suffix=request.suffix,
            chat_history=convert_history(request.chat_history),
            prefix_begin=request.position.prefix_begin,
            suffix_end=request.position.suffix_end,
            retrieved_chunks=build_retrievals(ri_request),
            external_source_ids=external_source_ids,
            context_code_exchange_request_id=request.context_code_exchange_request_id
            or None,
            user_guided_blobs=[
                user_guided_blob for user_guided_blob in request.user_guided_blobs
            ],
            changed_file_stats=convert_changed_file_stats_from_proto(
                request.changed_file_stats
            ),
            diff=request.diff,
            relevant_commit_messages=list(request.relevant_commit_messages),
            example_commit_messages=list(request.example_commit_messages),
            workspace_guidelines=request.workspace_guidelines,
            user_guidelines=request.user_guidelines,
            memories=request.agent_memories,
            persona_type=PersonaType(request.persona_type),
            tool_definitions=tool_definitions,
            doc_ids=self.get_blob_names_from_chat_request(request),
        )
        return chat_prompt_input


def ensure_tenant(tenant: DatasetTenant | str = DOGFOOD_SHARD):
    if isinstance(tenant, str):
        tenant = get_tenant(tenant)
    return tenant


def get_chat_host_request(request_id: str, tenant: DatasetTenant | str = DOGFOOD_SHARD):
    tenant = ensure_tenant(tenant)
    blob_getter = BlobGetter(tenant)
    return blob_getter.get_chat_request(request_id)


def get_chat_host_response(
    request_id: str, tenant: DatasetTenant | str = DOGFOOD_SHARD
):
    tenant = ensure_tenant(tenant)
    results = GCSRequestInsightFetcher.from_tenant(tenant).get_requests(
        request_ids=[request_id]
    )
    results = list(results)
    result = results[0]
    response = None
    for event in result.events:  # type: ignore
        if event.HasField("chat_host_response"):
            response = event.chat_host_response
            break
    assert response
    return response


def get_chat_host_response_text(
    request_id: str, tenant: DatasetTenant | str = DOGFOOD_SHARD
):
    response = get_chat_host_response(request_id, tenant)
    return response.response.text


def jsonify(x):
    if x is None or isinstance(x, (str, int, float, bool)):
        return x
    elif isinstance(x, (list, tuple)):
        return [jsonify(item) for item in x]
    elif isinstance(x, dict):
        return {str(k): jsonify(v) for k, v in x.items()}
    elif dataclasses.is_dataclass(x):
        return {f.name: jsonify(getattr(x, f.name)) for f in dataclasses.fields(x)}
    elif hasattr(x, "__dict__"):
        return jsonify(x.__dict__)
    else:
        return str(x)


def chat_history_request_to_dict(chat_history_request):
    return [
        {
            "request_message": exchange.request_message,
            "response_text": exchange.response_text,
            "request_id": exchange.request_id,
        }
        for exchange in chat_history_request
    ]


def retrieval_dict_to_request(retrieval_dict):
    return [
        PromptChunk(
            text=chunk["text"],
            path=chunk["path"],
            unique_id=chunk["unique_id"],
            origin=chunk["origin"],
            char_start=chunk["char_start"],
            char_end=chunk["char_end"],
            blob_name=chunk["blob_name"],
        )
        for chunk in retrieval_dict
    ]


def retrieval_request_to_dict(retrieval_request):
    return [
        {
            "text": chunk.text,
            "path": chunk.path,
            "unique_id": chunk.unique_id,
            "origin": chunk.origin,
            "char_start": chunk.char_start,
            "char_end": chunk.char_end,
            "blob_name": chunk.blob_name,
        }
        for chunk in retrieval_request
    ]


def chat_history_dict_to_request(chat_history_dict):
    return [
        Exchange(
            request_message=exchange["request_message"],
            response_text=exchange["response_text"],
            request_id=exchange["request_id"],
        )
        for exchange in chat_history_dict
    ]


def chat_prompt_input_from_dict(chat_prompt_dict: dict) -> ResearchChatPromptInput:
    return ResearchChatPromptInput(
        message=chat_prompt_dict["message"],
        path=chat_prompt_dict["path"],
        prefix=chat_prompt_dict["prefix"],
        selected_code=chat_prompt_dict["selected_code"],
        suffix=chat_prompt_dict["suffix"],
        chat_history=chat_history_dict_to_request(chat_prompt_dict["chat_history"]),
        prefix_begin=chat_prompt_dict["prefix_begin"],
        suffix_end=chat_prompt_dict["suffix_end"],
        retrieved_chunks=retrieval_dict_to_request(
            chat_prompt_dict["retrieved_chunks"]
        ),
        context_code_exchange_request_id=chat_prompt_dict[
            "context_code_exchange_request_id"
        ],
        user_guided_blobs=chat_prompt_dict["user_guided_blobs"],
        doc_ids=chat_prompt_dict["doc_ids"],
    )


def documents_from_dicts(document_dicts: list[dict]) -> list[Document]:
    return [
        Document(
            id=doc_dict["id"],
            text=doc_dict["text"],
            path=doc_dict["path"],
            meta=doc_dict["meta"],
        )
        for doc_dict in document_dicts
    ]


def documents_to_dicts(documents: list[Document]) -> list[dict]:
    return [
        {"id": doc.id, "text": doc.text, "path": doc.path, "meta": doc.meta}
        for doc in documents
    ]


def count_tags(text: str):
    opening_count = text.count("<augment_code_snippet ")
    closing_count = text.count("</augment_code_snippet>")
    return opening_count, closing_count


def render_prompt(prompt_output):
    string = ""
    divider = "----------------------------------------\n"
    string += (
        divider + "system_prompt:\n" + divider + prompt_output.system_prompt + "\n\n"
    )
    string += (
        divider
        + "current message:\n"
        + divider
        + _stringify_request_message(prompt_output.message)
        + "\n\n"
    )
    string += divider + "chat history (from most recent to least recent):\n" + divider
    for exchange in prompt_output.chat_history:
        string += (
            "    "
            + divider
            + "    user:\n"
            + "    "
            + divider
            + "    "
            + _stringify_request_message(exchange.request_message)
            + "\n"
        )
        string += (
            "\n    "
            + divider
            + "    assistant:\n"
            + "    "
            + divider
            + "    "
            + _stringify_response_message(exchange.response_text)
        )
        string += "\n\n"
    return string


def decode_prompt(rendered_str: str) -> StructuredChatPromptOutput:
    divider = "----------------------------------------\n"
    sections = rendered_str.split(divider)

    # Extract system prompt
    system_prompt = sections[2].strip()

    # Extract current message
    message = parse_request_message_nodes(sections[4].strip())

    # Extract chat history
    chat_history = []
    history_sections = sections[6:]  # Skip headers

    i = 0
    while i < len(history_sections):
        if not history_sections[i].strip():  # Skip empty sections
            i += 1
            continue

        if "user:" in history_sections[i]:
            request_message = history_sections[i + 1].replace("    ", "", 1).strip()
            response_text = history_sections[i + 3].replace("    ", "", 1).strip()
            request_nodes = parse_request_message_nodes(request_message)
            response_nodes = parse_response_message_nodes(response_text)
            chat_history.append(
                Exchange(
                    request_message=request_nodes,
                    response_text=response_nodes,
                )
            )
            i += 4
        else:
            i += 1

    tools = ["dummy_tool"] if "[Tool Use]" in rendered_str else []

    return StructuredChatPromptOutput(
        system_prompt=system_prompt,
        message=message,
        chat_history=chat_history,
        retrieved_chunks_in_prompt=[],
        tools=tools,
    )


def get_prompt_formatter(prompt_formatter_name: str):
    return get_structured_chat_prompt_formatter_by_name(
        prompt_formatter_name,  # type: ignore
        TOKEN_APPORTIONMENT,
    )


def fix_input(chat_prompt_input, backtick_count=4):
    chat_history = list(chat_prompt_input.chat_history)
    new_chat_history = []
    for exchange in chat_history:
        response_text = exchange.response_text
        # Replace opening tag with 4 backticks
        new_response_text = re.sub(
            r"(<augment_code_snippet\s+path=.*?>)\s*`+([^\n]*\n)",
            rf"\1\n{'`' * backtick_count}\2",
            response_text,
        )
        # Replace closing tag with 4 backticks
        new_response_text = re.sub(
            r"`+\s*</augment_code_snippet>",
            rf"{'`' * backtick_count}\n</augment_code_snippet>",
            new_response_text,
        )
        new_exchange = dataclasses.replace(exchange, response_text=new_response_text)
        new_chat_history.append(new_exchange)

    return dataclasses.replace(chat_prompt_input, chat_history=new_chat_history)


def fix_prompt_output(prompt_output, backtick_count=4):
    chat_history = list(prompt_output.chat_history)
    new_chat_history = []
    new_system_prompt = re.sub(
        r"(<augment_code_snippet\s+path=.*?>)\s*`+([^\n]*\n)",
        rf"\1\n{'`' * backtick_count}\2",
        prompt_output.system_prompt,
    )
    new_system_prompt = re.sub(
        r"`+\s*</augment_code_snippet>",
        rf"{'`' * backtick_count}\n</augment_code_snippet>",
        new_system_prompt,
    )
    for exchange in chat_history:
        response_text = exchange.response_text
        # Replace opening tag with 4 backticks
        new_response_text = re.sub(
            r"(<augment_code_snippet\s+path=.*?>)\s*`+([^\n]*\n)",
            rf"\1\n{'`' * backtick_count}\2",
            response_text,
        )
        # Replace closing tag with 4 backticks
        new_response_text = re.sub(
            r"`+\s*</augment_code_snippet>",
            rf"{'`' * backtick_count}\n</augment_code_snippet>",
            new_response_text,
        )
        new_exchange = dataclasses.replace(exchange, response_text=new_response_text)
        new_chat_history.append(new_exchange)

    return dataclasses.replace(
        prompt_output, system_prompt=new_system_prompt, chat_history=new_chat_history
    )


def results_to_printable(results):
    results_to_print = {
        "opening_count": results["opening_count"],
        "closing_count": results["closing_count"],
        "total_opening_count_tf": results["total_opening_count_tf"]
        if "total_opening_count_tf" in results
        else None,
        "total_closing_count_tf": results["total_closing_count_tf"]
        if "total_closing_count_tf" in results
        else None,
        "total_opening_count_ar": results["total_opening_count_ar"]
        if "total_opening_count_ar" in results
        else None,
        "total_closing_count_ar": results["total_closing_count_ar"]
        if "total_closing_count_ar" in results
        else None,
    }
    return results_to_print


def generate_chat_response(
    url, model_name, documents, model_input, retry_limit=1, raw=False
):
    remote_config = {
        "client": {"url": url},
        "model_name": model_name,
    }
    remote_chat = RemoteChatSystem.from_yaml_config(
        remote_config,
    )
    remote_chat.load()
    remote_chat.add_docs(documents)

    with collect_artifacts() as collector_manager:
        chat_response = ChatResult(generated_text="", prompt_tokens=[])
        artifacts = [{"request_id": ""}]
        for trial in range(retry_limit):
            try:
                chat_response = remote_chat.generate(model_input)
                artifacts = collector_manager.get_artifacts()
                break
            except Exception as e:
                print(f"Error generating response (trial {trial}): {e}")
                time.sleep(60)
    if raw:
        generated_text = ""
        for node in chat_response.nodes or []:
            if node.type == ChatResultNodeType.RAW_RESPONSE:
                generated_text += node.content
    else:
        generated_text = chat_response.generated_text
    assert artifacts
    request_id = artifacts[0]["request_id"]
    return generated_text, request_id


def parse_request_message_nodes(request_message: str) -> list[ChatRequestNode]:
    """Parse a request message into a list of ChatRequestNodes, handling both text and tool result sections.

    Args:
        request_message: Raw request message text that may contain tool result sections

    Returns:
        List of ChatRequestNode objects representing the message content
    """
    request_nodes = []
    tool_result_pattern = r"\[Tool Result\]\nToolUseId: (.*?)\nContent: (.*?)\nIsError: (.*?)(?=\[Tool Result\]|$)"

    tool_matches = re.finditer(tool_result_pattern, request_message, re.DOTALL)
    last_end = 0

    for match in tool_matches:
        # Add text node for content before tool result
        if match.start() > last_end:
            text_content = request_message[last_end : match.start()].strip()
            if text_content:
                request_nodes.append(
                    ChatRequestNode(
                        id=len(request_nodes),
                        type=ChatRequestNodeType.TEXT,
                        text_node=ChatRequestText(content=text_content),
                        tool_result_node=None,
                    )
                )

        # Add tool result node
        tool_use_id = match.group(1).strip()
        content = match.group(2).strip()
        is_error = match.group(3).strip().lower() == "true"

        request_nodes.append(
            ChatRequestNode(
                id=len(request_nodes),
                type=ChatRequestNodeType.TOOL_RESULT,
                text_node=None,
                tool_result_node=ChatRequestToolResult(
                    tool_use_id=tool_use_id,
                    content=content,
                    is_error=is_error,
                    # TODO: Add support for content nodes
                ),
            )
        )
        last_end = match.end()

    # Add remaining text as text node
    if last_end < len(request_message):
        remaining_text = request_message[last_end:].strip()
        if remaining_text:
            request_nodes.append(
                ChatRequestNode(
                    id=len(request_nodes),
                    type=ChatRequestNodeType.TEXT,
                    text_node=ChatRequestText(content=remaining_text),
                    tool_result_node=None,
                )
            )

    return request_nodes


def parse_response_message_nodes(response_message: str) -> list[ChatResultNode]:
    """Parse a response message into a list of ChatResultNodes, handling both text and tool use sections.

    Args:
        response_message: Raw response message text that may contain tool use sections

    Returns:
        List of ChatResultNode objects representing the message content
    """
    response_nodes = []
    tool_use_pattern = (
        r"\[Tool Use\]\nToolUseId: (.*?)\nName: (.*?)\nInput: (.*?)(?=\n\n|\Z)"
    )
    tool_matches = re.finditer(tool_use_pattern, response_message, re.DOTALL)
    last_end = 0

    for match in tool_matches:
        # Add text node for content before tool use
        if match.start() > last_end:
            text_content = response_message[last_end : match.start()].strip()
            if text_content:
                response_nodes.append(
                    ChatResultNode(
                        id=len(response_nodes),
                        type=ChatResultNodeType.RAW_RESPONSE,
                        content=text_content,
                        tool_use=None,
                    )
                )

        # Add tool use node
        tool_use_id = match.group(1).strip()
        tool_name = match.group(2).strip()
        tool_input = match.group(3).strip()

        try:
            input_dict = json.loads(tool_input) if tool_input.strip() else {}
        except json.JSONDecodeError:
            try:
                fixed_input = tool_input.replace("'", '"')
                input_dict = json.loads(fixed_input) if fixed_input.strip() else {}
            except json.JSONDecodeError:
                input_dict = {"raw_input": tool_input}

        response_nodes.append(
            ChatResultNode(
                id=len(response_nodes),
                type=ChatResultNodeType.TOOL_USE,
                content="",
                tool_use=ChatResultToolUse(
                    tool_use_id=tool_use_id,
                    name=tool_name,
                    input=input_dict,
                ),
            )
        )
        last_end = match.end()

    # Add remaining text as raw response node
    if last_end < len(response_message):
        remaining_text = response_message[last_end:].strip()
        if remaining_text:
            response_nodes.append(
                ChatResultNode(
                    id=len(response_nodes),
                    type=ChatResultNodeType.RAW_RESPONSE,
                    content=remaining_text,
                    tool_use=None,
                )
            )

    return response_nodes


def truncate_prompt_input(
    prompt_input: ResearchChatPromptInput, final_round: int
) -> ResearchChatPromptInput:
    """Truncate a prompt input to include only exchanges up to the specified round.

    Args:
        prompt_input: The prompt input to truncate
        final_round: The index of the last round to include

    Returns:
        A new prompt input with chat history truncated to the specified round
    """
    if final_round >= len(prompt_input.chat_history):  # type: ignore
        return prompt_input

    return dataclasses.replace(
        prompt_input,
        chat_history=prompt_input.chat_history[:final_round],  # type: ignore
        message=prompt_input.chat_history[final_round].request_message,  # type: ignore
    )


def truncate_prompt_output(
    prompt_output: StructuredChatPromptOutput, final_round: int
) -> StructuredChatPromptOutput:
    """Truncate a prompt output to include only exchanges up to the specified round.

    Args:
        prompt_output: The prompt output to truncate
        final_round: The index of the last round to include

    Returns:
        A new prompt output with chat history truncated to the specified round
    """
    if final_round >= len(prompt_output.chat_history):  # type: ignore
        return prompt_output

    return dataclasses.replace(
        prompt_output,
        chat_history=prompt_output.chat_history[:final_round],  # type: ignore
        message=prompt_output.chat_history[final_round].request_message,  # type: ignore
    )


def fix_tool_calls(
    prompt_output: StructuredChatPromptOutput,
) -> StructuredChatPromptOutput:
    """Fixes tool calls and results in chat history.

    1. Ensures all tool calls (ChatResultNode with type TOOL_USE) have a tool_use_id.
    2. Ensures tool results (ChatRequestNode with type TOOL_RESULT) in the next
       exchange have tool_use_ids matching the tool_use_ids from the previous exchange.
    3. If the number of tool results is less than the number of tool calls, pads
       the results with error nodes.

    Always returns a new StructuredChatPromptOutput object.
    Uses dataclasses.replace for modifications where possible.
    """
    if not hasattr(prompt_output, "chat_history") or not prompt_output.chat_history:
        return prompt_output

    new_chat_history = []
    outstanding_tool_calls = []
    tool_call_id_counter = 0

    for exchange in prompt_output.chat_history:
        if isinstance(exchange.request_message, str):
            request_message = [
                ChatRequestNode(
                    id=0,
                    type=ChatRequestNodeType.TEXT,
                    text_node=ChatRequestText(content=exchange.request_message),
                    tool_result_node=None,
                )
            ]
        else:
            request_message = exchange.request_message

        new_request_message = []
        for request_node in request_message:
            if request_node.type == ChatRequestNodeType.TOOL_RESULT:
                assert request_node.tool_result_node
                if outstanding_tool_calls:
                    tool_use_id, _ = outstanding_tool_calls.pop(0)
                    old_tool_use_id = request_node.tool_result_node.tool_use_id
                    if old_tool_use_id and old_tool_use_id != tool_use_id:
                        raise ValueError(
                            f"Tool result ID mismatch: expected {tool_use_id},"
                            f" got {request_node.tool_result_node.tool_use_id}."
                        )
                    new_tool_result = dataclasses.replace(
                        request_node.tool_result_node, tool_use_id=tool_use_id
                    )
                    new_request_node = dataclasses.replace(
                        request_node, tool_result_node=new_tool_result
                    )
                else:
                    raise ValueError("Too many tool results")
                new_request_message.append(new_request_node)

        for outstanding_tool_call in outstanding_tool_calls:
            tool_use_id, _ = outstanding_tool_call
            new_tool_result = ChatRequestNode(
                id=tool_use_id,
                type=ChatRequestNodeType.TOOL_RESULT,
                text_node=None,
                tool_result_node=ChatRequestToolResult(
                    tool_use_id=tool_use_id,
                    content="Failed tool call.",
                    is_error=True,
                ),
            )
            new_request_message.append(new_tool_result)

        for request_node in request_message:
            if request_node.type != ChatRequestNodeType.TOOL_RESULT:
                new_request_node = deepcopy(request_node)
                new_request_message.append(new_request_node)

        if isinstance(exchange.response_text, str):
            response_text = [
                ChatResultNode(
                    id=0,
                    type=ChatResultNodeType.RAW_RESPONSE,
                    content=exchange.response_text,
                    tool_use=None,
                )
            ]
        else:
            response_text = exchange.response_text

        outstanding_tool_calls = []
        new_response_text = []
        for response_node in response_text:
            if response_node.type == ChatResultNodeType.TOOL_USE:
                assert response_node.tool_use
                tool_use_id = response_node.tool_use.tool_use_id
                if not tool_use_id:
                    tool_use_id = str(tool_call_id_counter)
                    tool_call_id_counter += 1
                outstanding_tool_calls.append(
                    (
                        tool_use_id,
                        response_node.tool_use.name,
                    )
                )
                new_response_node = dataclasses.replace(
                    response_node,
                    tool_use=dataclasses.replace(
                        response_node.tool_use, tool_use_id=tool_use_id
                    ),
                )
            else:
                new_response_node = deepcopy(response_node)
            new_response_text.append(new_response_node)

        new_exchange = dataclasses.replace(
            exchange,
            request_message=new_request_message,
            response_text=new_response_text,
        )
        new_chat_history.append(new_exchange)

    return dataclasses.replace(prompt_output, chat_history=new_chat_history)


def print_request(
    request_message: str | list[ChatRequestNode],
    text_limit=-1,
    tool_limit=100,
    file_=sys.stdout,
):
    if isinstance(request_message, str):
        print("[USER]: " + request_message[:text_limit], file=file_)
    else:
        for node in request_message:
            if node.type == ChatRequestNodeType.TEXT:
                assert node.text_node
                print(node.text_node.content[:text_limit], file=file_)
            elif node.type == ChatRequestNodeType.TOOL_RESULT:
                assert node.tool_result_node
                print(
                    f"Tool Result ({node.tool_result_node.tool_use_id}):"
                    f" ({'ERROR' if node.tool_result_node.is_error else 'OK'})\n",
                    node.tool_result_node.content[:tool_limit],
                    file=file_,
                )


def jsonify_final_parameters(
    final_parameters: dict | list | None, string_limit: int = 100
) -> dict | list | None:
    if final_parameters is None:
        return None
    elif isinstance(final_parameters, list):
        return [jsonify_final_parameters(x, string_limit) for x in final_parameters]
    else:
        for k, v in final_parameters.items():
            if isinstance(v, list):
                final_parameters[k] = [
                    jsonify_final_parameters(x, string_limit)
                    if isinstance(x, dict)
                    else x
                    for x in v
                ]
            elif isinstance(v, dict):
                final_parameters[k] = jsonify_final_parameters(v, string_limit)
            elif isinstance(v, Part):
                part_dict = {}
                if v.text:
                    part_dict["text"] = (
                        v.text[:string_limit] + "..."
                        if string_limit and len(v.text) > string_limit
                        else v.text
                    )
                for attr in [
                    "thought",
                    "code_execution_result",
                    "executable_code",
                    "file_data",
                    "function_call",
                    "function_response",
                    "inline_data",
                    "video_metadata",
                ]:
                    attr_value = getattr(v, attr, None)
                    if attr_value is not None:
                        part_dict[attr] = attr_value
                final_parameters[k] = part_dict
            elif isinstance(v, Content):
                content_dict = {"role": v.role}
                if hasattr(v, "parts") and v.parts:
                    content_dict["parts"] = [  # type: ignore
                        jsonify_final_parameters({"part": part}, string_limit)["part"]  # type: ignore
                        for part in v.parts
                    ]
                final_parameters[k] = content_dict
            elif v == NOT_GIVEN:
                final_parameters[k] = None
            elif string_limit and isinstance(v, str) and len(v) > string_limit:
                final_parameters[k] = v[:string_limit] + "..."
        return final_parameters


def print_response(
    response_text: str | list[ChatResultNode],
    text_limit=-1,
    tool_limit=100,
    string_limit=100,
    file_=sys.stdout,
):
    if isinstance(response_text, str):
        print(response_text[:text_limit], file=file_)
    else:
        for node in response_text:
            if node.type == ChatResultNodeType.RAW_RESPONSE:
                print(node.content[:text_limit], file=file_)
            elif node.type == ChatResultNodeType.TOOL_USE:
                assert node.tool_use
                print(
                    f"Tool Use: {node.tool_use.name} ({node.tool_use.tool_use_id})"
                    f" {json.dumps(node.tool_use.input)[:tool_limit]}",
                    file=file_,
                )
            elif node.type == ChatResultNodeType.FINAL_PARAMETERS:
                print(
                    f"Final Parameters:"
                    f" {json.dumps(jsonify_final_parameters(node.final_parameters, string_limit))}",
                    file=file_,
                )


def print_exchange(exchange: Exchange, text_limit=-1, tool_limit=100, round=None):
    round_indicator = str(round) if round is not None else ""
    print("=" * 40 + round_indicator + "=" * 40)
    print_request(exchange.request_message, text_limit, tool_limit)
    print("-" * 81)
    print_response(exchange.response_text, text_limit, tool_limit)


def print_chat_history(chat_history: list[Exchange], text_limit=-1, tool_limit=100):
    for i, exchange in enumerate(chat_history):
        print_exchange(exchange, text_limit, tool_limit, i)
