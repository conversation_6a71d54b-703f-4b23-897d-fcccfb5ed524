package main

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strconv"
	"sync"
	"text/template"
	"time"

	"cloud.google.com/go/bigquery"
	authclient "github.com/augmentcode/augment/services/auth/central/auth_client"
	authpb "github.com/augmentcode/augment/services/auth/central/server/auth"
	auth_entities "github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	teammanagementclient "github.com/augmentcode/augment/services/auth/central/team_management_client"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tenantpb "github.com/augmentcode/augment/services/tenant_watcher/proto"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	tokenexchangepb "github.com/augmentcode/augment/services/token_exchange/proto"
	"github.com/rs/zerolog/log"
)

// NB: Most of this code isn't unit tested because the query used to copy data from temp tables to
// the main table uses TRUNCATE, which isn't supported by the BigQuery emulator:
// https://github.com/goccy/bigquery-emulator/issues/378

const (
	tenantTableName                    = "tenant"
	userTableName                      = "user"
	subscriptionTableName              = "subscription"
	tenantSubscriptionMappingTableName = "tenant_subscription_mapping"
	userTenantMappingTableName         = "user_tenant_mapping"
	maxInsertBatchSize                 = 1000

	// Keep temporary tables around for a day for debugging purposes.
	tempTableExpiration = 24 * time.Hour
)

type SyncJob struct {
	bqClient             *bigquery.Client
	datasetName          string
	tempDatasetName      string
	authClient           authclient.AuthClient
	teamManagementClient teammanagementclient.TeamManagementClient
	tenantWatcherClient  tenantwatcherclient.TenantWatcherClient
	tokenExchangeClient  tokenexchange.TokenExchangeClient
}

// This struct should be kept in sync with the tenant table in
// services/request_insight/analytics_dataset/schema.jsonnet.
// It's important that this struct and all of its fields be public; the bigquery library's schema
// inference doesn't work otherwise.
type TenantRow struct {
	ID                      string                 `bigquery:"id"`
	Name                    string                 `bigquery:"name"`
	ShardNamespace          string                 `bigquery:"shard_namespace"`
	Cloud                   string                 `bigquery:"cloud"`
	Tier                    bigquery.NullString    `bigquery:"tier"`
	OtherNamespace          bigquery.NullString    `bigquery:"other_namespace"`
	EncryptionKeyName       bigquery.NullString    `bigquery:"encryption_key_name"`
	EncryptionKeyTTL        bigquery.NullInt64     `bigquery:"encryption_key_ttl"`
	Version                 bigquery.NullString    `bigquery:"version"`
	DeletedAt               bigquery.NullTimestamp `bigquery:"deleted_at"`
	SupportAccessControl    bigquery.NullBool      `bigquery:"support_access_control"`
	MultiTenantAllowed      bigquery.NullBool      `bigquery:"multi_tenant_allowed"`
	SupportTenant           bigquery.NullBool      `bigquery:"support_tenant"`
	DefaultSupportTenant    bigquery.NullBool      `bigquery:"default_support_tenant"`
	SlackbotAllowlist       []string               `bigquery:"slackbot_allowlist_channels"`
	BlockGenieRequestAccess bigquery.NullBool      `bigquery:"block_genie_request_access"`
	IpAllowlist             bigquery.NullString    `bigquery:"ip_allowlist"`
	IsSelfServeTeam         bigquery.NullBool      `bigquery:"is_self_serve_team"`
	IsLegacySelfServeTeam   bigquery.NullBool      `bigquery:"is_legacy_self_serve_team"`
	// Auth configuration fields
	Domain                   bigquery.NullString `bigquery:"domain"`
	UsernameDomains          []string            `bigquery:"username_domains"`
	EmailAddressDomains      []string            `bigquery:"email_address_domains"`
	AllowedIdentityProviders []string            `bigquery:"allowed_identity_providers"`
}

// This struct should be kept in sync with the user table in
// services/request_insight/analytics_dataset/schema.jsonnet.
// It's important that this struct and all of its fields be public; the bigquery library's schema
// inference doesn't work otherwise.
type UserSuspensionRow struct {
	SuspensionID   string    `bigquery:"suspension_id"`
	CreatedTime    time.Time `bigquery:"created_time"`
	SuspensionType string    `bigquery:"suspension_type"`
	Evidence       string    `bigquery:"evidence"`
}

type UserTierChangeRow struct {
	ID         string    `bigquery:"id"`
	TargetTier string    `bigquery:"target_tier"`
	CreatedAt  time.Time `bigquery:"created_at"`
	UpdatedAt  time.Time `bigquery:"updated_at"`
}

type UserSubscriptionCreationInfoRow struct {
	ID        string    `bigquery:"id"`
	Status    string    `bigquery:"status"`
	CreatedAt time.Time `bigquery:"created_at"`
	UpdatedAt time.Time `bigquery:"updated_at"`
}

type UserRow struct {
	ID                       string                           `bigquery:"id"`
	Email                    string                           `bigquery:"email"`
	TenantIDs                []string                         `bigquery:"tenant_ids"`
	CreatedAt                time.Time                        `bigquery:"created_at"`
	InUsa                    bigquery.NullTimestamp           `bigquery:"in_usa"`
	StripeCustomerID         bigquery.NullString              `bigquery:"stripe_customer_id"`
	Blocked                  bool                             `bigquery:"blocked"`
	IDPUserIDs               []string                         `bigquery:"idp_user_ids"`
	SubscriptionID           bigquery.NullString              `bigquery:"subscription_id"`
	OrbCustomerID            bigquery.NullString              `bigquery:"orb_customer_id"`
	CheckSubscriptionStatus  bigquery.NullBool                `bigquery:"check_subscription_status"`
	BillingMethod            string                           `bigquery:"billing_method"`
	OrbSubscriptionID        bigquery.NullString              `bigquery:"orb_subscription_id"`
	SubscriptionCreationID   bigquery.NullString              `bigquery:"subscription_creation_id"`
	Suspensions              []UserSuspensionRow              `bigquery:"suspensions"`
	TierChange               *UserTierChangeRow               `bigquery:"tier_change"`
	SubscriptionCreationInfo *UserSubscriptionCreationInfoRow `bigquery:"subscription_creation_info"`
	SuspensionExempt         bool                             `bigquery:"suspension_exempt"`
}

// This struct should be kept in sync with the subscription table in
// services/request_insight/analytics_dataset/schema.jsonnet.
// It's important that this struct and all of its fields be public; the bigquery library's schema
// inference doesn't work otherwise.
type SubscriptionRow struct {
	SubscriptionID       string                 `bigquery:"subscription_id"`
	StripeCustomerID     string                 `bigquery:"stripe_customer_id"`
	PriceID              string                 `bigquery:"price_id"`
	Status               string                 `bigquery:"status"`
	Seats                int32                  `bigquery:"seats"`
	StartDate            bigquery.NullTimestamp `bigquery:"start_date"`
	TrialEnd             bigquery.NullTimestamp `bigquery:"trial_end"`
	EndDate              bigquery.NullTimestamp `bigquery:"end_date"`
	CancelAtPeriodEnd    bool                   `bigquery:"cancel_at_period_end"`
	HasPaymentMethod     bool                   `bigquery:"has_payment_method"`
	TenantID             bigquery.NullString    `bigquery:"tenant_id"`
	UserID               bigquery.NullString    `bigquery:"user_id"`
	CreatedAt            bigquery.NullTimestamp `bigquery:"created_at"`
	UpdatedAt            bigquery.NullTimestamp `bigquery:"updated_at"`
	OrbCustomerID        bigquery.NullString    `bigquery:"orb_customer_id"`
	OrbStatus            bigquery.NullString    `bigquery:"orb_status"`
	HasUnpaidInvoice     bigquery.NullBool      `bigquery:"has_unpaid_invoice"`
	ExternalPlanID       bigquery.NullString    `bigquery:"external_plan_id"`
	BillingMethod        bigquery.NullString    `bigquery:"billing_method"`
	SeatsID              bigquery.NullString    `bigquery:"seats_id"`
	IncludedCreditsID    bigquery.NullString    `bigquery:"included_credits_id"`
	CreditsPerMonth      bigquery.NullFloat64   `bigquery:"credits_per_month"`
	UsageBalanceDepleted bigquery.NullBool      `bigquery:"usage_balance_depleted"`
}

// This struct should be kept in sync with the tenant_subscription_mapping table in
// services/request_insight/analytics_dataset/schema.jsonnet.
// It's important that this struct and all of its fields be public; the bigquery library's schema
// inference doesn't work otherwise.
type TenantSubscriptionMappingRow struct {
	TenantID                  string                 `bigquery:"tenant_id"`
	StripeSubscriptionID      bigquery.NullString    `bigquery:"stripe_subscription_id"`
	StripeCustomerID          bigquery.NullString    `bigquery:"stripe_customer_id"`
	OrbCustomerID             bigquery.NullString    `bigquery:"orb_customer_id"`
	OrbSubscriptionID         bigquery.NullString    `bigquery:"orb_subscription_id"`
	BillingMethod             bigquery.NullString    `bigquery:"billing_method"`
	CreatedAt                 bigquery.NullTimestamp `bigquery:"created_at"`
	PlanChangeID              bigquery.NullString    `bigquery:"plan_change_id"`
	PlanChangeTargetOrbPlanID bigquery.NullString    `bigquery:"plan_change_target_orb_plan_id"`
	PlanChangeCreatedAt       bigquery.NullTimestamp `bigquery:"plan_change_created_at"`
	PlanChangeUpdatedAt       bigquery.NullTimestamp `bigquery:"plan_change_updated_at"`
}

// This struct should be kept in sync with the user_tenant_mapping table in
// services/request_insight/analytics_dataset/schema.jsonnet.
// It's important that this struct and all of its fields be public; the bigquery library's schema
// inference doesn't work otherwise.
type UserTenantMappingRow struct {
	Tenant          string   `bigquery:"tenant"`
	UserID          string   `bigquery:"user_id"`
	CustomerUIRoles []string `bigquery:"customer_ui_roles"`
}

func NewSyncJob(
	ctx context.Context,
	projectId string,
	datasetName string,
	tempDatasetName string,
	tenantWatcherClient tenantwatcherclient.TenantWatcherClient,
	authClient authclient.AuthClient,
	teamManagementClient teammanagementclient.TeamManagementClient,
	tokenExchangeClient tokenexchange.TokenExchangeClient,
) (*SyncJob, error) {
	// The BigQuery API doesn't let us parameterize dataset/table names, so we have to inject this
	// into our query with string manipulation. Make sure it doesn't contain contain anything that
	// could be malicious.
	if !checkDatasetName(datasetName) {
		return nil, fmt.Errorf("Invalid dataset name %s", datasetName)
	}

	bqClient, err := bigquery.NewClient(ctx, projectId)
	if err != nil {
		return nil, fmt.Errorf("error creating bigquery client: %w", err)
	}

	return &SyncJob{
		bqClient:             bqClient,
		datasetName:          datasetName,
		tempDatasetName:      tempDatasetName,
		authClient:           authClient,
		teamManagementClient: teamManagementClient,
		tenantWatcherClient:  tenantWatcherClient,
		tokenExchangeClient:  tokenExchangeClient,
	}, nil
}

// Check that the provided dataset name contains only letters and underscores. Returns true iff the
// provided name is valid.
func checkDatasetName(name string) bool {
	return regexp.MustCompile(`^[a-zA-Z_]+$`).MatchString(name)
}

func (s *SyncJob) Close() {
	s.bqClient.Close()
}

func (s *SyncJob) Run(ctx context.Context) error {
	tenants, err := s.tenantWatcherClient.GetTenants(ctx, "")
	if err != nil {
		return fmt.Errorf("error getting tenants: %w", err)
	}

	// Perform tenant, user, subscription, tenant subscription mapping, and user tenant mapping syncs in parallel.
	var tenantSyncErr, userSyncErr, subscriptionSyncErr, tenantSubscriptionMappingSyncErr, userTenantMappingSyncErr error
	var wg sync.WaitGroup
	wg.Add(5)
	go func() {
		defer wg.Done()
		tenantSyncErr = s.syncTenants(ctx, tenants)
	}()
	go func() {
		defer wg.Done()
		userSyncErr = s.syncUsers(ctx, s.authClient, s.tokenExchangeClient)
	}()
	go func() {
		defer wg.Done()
		subscriptionSyncErr = s.syncSubscriptions(ctx, s.teamManagementClient, s.tokenExchangeClient)
	}()
	go func() {
		defer wg.Done()
		tenantSubscriptionMappingSyncErr = s.syncTenantSubscriptionMappings(ctx, s.authClient, s.tokenExchangeClient)
	}()
	go func() {
		defer wg.Done()
		userTenantMappingSyncErr = s.syncUserTenantMappings(ctx)
	}()
	wg.Wait()

	if tenantSyncErr != nil || userSyncErr != nil || subscriptionSyncErr != nil || tenantSubscriptionMappingSyncErr != nil || userTenantMappingSyncErr != nil {
		return fmt.Errorf("sync failed: tenant: %w, user: %v, subscription: %v, tenant_subscription_mapping: %v, user_tenant_mapping: %v", tenantSyncErr, userSyncErr, subscriptionSyncErr, tenantSubscriptionMappingSyncErr, userTenantMappingSyncErr)
	}

	// Run verification checks after successful sync
	if err := s.RunVerifications(ctx); err != nil {
		log.Warn().Err(err).Msg("Verification checks failed, but sync was successful")
	}

	return nil
}

func (s *SyncJob) syncTenants(ctx context.Context, tenants []*tenantpb.Tenant) error {
	tenantRows := make([]*TenantRow, 0, len(tenants))
	for _, tenant := range tenants {
		tenantRows = append(tenantRows, toTenantRow(tenant))
	}

	// Create a temporary table for the current list of tenants.
	tempTableName := fmt.Sprintf("temp_tenant_%d", time.Now().Unix())
	log.Info().Msgf("Creating temp table %s", tempTableName)
	tenantSchema, err := bigquery.InferSchema(TenantRow{})
	if err != nil {
		return fmt.Errorf("error inferring schema from tenantRow struct: %w", err)
	} else if len(tenantSchema) == 0 {
		return fmt.Errorf("inferred tenant schema is empty")
	}
	tempTable := s.bqClient.Dataset(s.tempDatasetName).Table(tempTableName)

	// Delete the table if it already exists (from a previous failed run)
	if err := tempTable.Delete(ctx); err != nil {
		log.Debug().Err(err).Msgf("Could not delete existing temp table %s (this is normal if it doesn't exist)", tempTableName)
	}

	err = tempTable.Create(ctx, &bigquery.TableMetadata{
		Schema:         tenantSchema,
		ExpirationTime: time.Now().Add(tempTableExpiration),
	})
	if err != nil {
		return fmt.Errorf("error creating temp_tenants table: %v", err)
	}

	// Ensure cleanup happens even if the function fails
	defer func() {
		if err := tempTable.Delete(ctx); err != nil {
			log.Warn().Err(err).Msgf("Failed to clean up temp table %s", tempTableName)
		} else {
			log.Info().Msgf("Successfully cleaned up temp table %s", tempTableName)
		}
	}()

	// Insert the rows into the temporary table in batches to avoid "request too large" errors.
	inserter := tempTable.Inserter()
	for batchStart := 0; batchStart < len(tenantRows); batchStart += maxInsertBatchSize {
		batchEnd := min(batchStart+maxInsertBatchSize, len(tenantRows))

		batch := tenantRows[batchStart:batchEnd]
		err = inserter.Put(ctx, batch)
		if err != nil {
			return fmt.Errorf(
				"error inserting batch %d-%d into %s: %w",
				batchStart, batchEnd, tempTableName, err,
			)
		}
		log.Debug().Msgf(
			"Inserted batch of %d rows into %s (%d/%d)",
			len(batch), tempTableName, batchEnd, len(tenantRows),
		)
	}

	// Replace the contents of the tenant table with the contents of the temporary table.
	log.Info().Msgf("Syncing tenants from %s to %s", tempTableName, tenantTableName)
	err = s.updateTable(
		ctx,
		fmt.Sprintf("`%s.%s`", s.tempDatasetName, tempTableName),
		fmt.Sprintf("`%s.%s`", s.datasetName, tenantTableName),
		[]string{
			"id", "name", "shard_namespace", "cloud", "tier", "other_namespace",
			"encryption_key_name", "encryption_key_ttl", "version", "deleted_at",
			"support_access_control", "multi_tenant_allowed", "support_tenant",
			"default_support_tenant", "slackbot_allowlist_channels", "block_genie_request_access",
			"ip_allowlist", "is_self_serve_team", "is_legacy_self_serve_team",
			"domain", "username_domains", "email_address_domains", "allowed_identity_providers",
		},
		"id",
	)
	if err != nil {
		return fmt.Errorf("error replacing tenant table: %w", err)
	}

	log.Info().Msgf("Synced %d tenants", len(tenants))
	return nil
}

func (s *SyncJob) syncUsers(
	ctx context.Context,
	authClient authclient.AuthClient,
	tokenExchangeClient tokenexchange.TokenExchangeClient,
) error {
	// Gather user information from auth-central. We store this information in a map to avoid writing
	// duplicate rows for users present in multiple tenants. The User proto returned by auth-central
	// will be identical for a given user across all tenants it is a member of.
	usersByID := make(map[string]*auth_entities.User, 0)
	sessionId := requestcontext.NewRandomRequestSessionId()

	token, err := tokenExchangeClient.GetSignedTokenForServiceWithNamespace(
		ctx, "", "", []tokenexchangepb.Scope{tokenexchangepb.Scope_AUTH_R},
	)
	if err != nil {
		return fmt.Errorf("error getting service token: %w", err)
	}

	var nextPageToken *string

	for {
		requestCtx := requestcontext.New(
			requestcontext.NewRandomRequestId(), sessionId, "request-insight-sync", token,
		)

		var pageToken string
		if nextPageToken != nil {
			pageToken = *nextPageToken
		}

		users, err := authClient.GetUsers(ctx, requestCtx, &authpb.GetUsersRequest{
			PageSize:  1000,
			PageToken: pageToken,
		})
		if err != nil {
			return fmt.Errorf("error getting users: %w", err)
		}

		for _, user := range users.Users {
			usersByID[user.Id] = user
		}

		if users.NextPageToken == "" {
			break
		}

		nextPageToken = &users.NextPageToken
	}

	// Convert users to rows that can be written to BigQuery.
	userRows := make([]*UserRow, 0, len(usersByID))
	for _, user := range usersByID {
		userRows = append(userRows, toUserRow(user))
	}

	// Create a temporary table for the current list of users.
	tempTableName := fmt.Sprintf("temp_user_%d", time.Now().Unix())
	log.Info().Msgf("Creating temp table %s", tempTableName)
	userSchema, err := bigquery.InferSchema(UserRow{})
	if err != nil {
		return fmt.Errorf("error inferring schema from userRow struct: %w", err)
	} else if len(userSchema) == 0 {
		return fmt.Errorf("inferred user schema is empty")
	}
	tempTable := s.bqClient.Dataset(s.tempDatasetName).Table(tempTableName)

	// Delete the table if it already exists (from a previous failed run)
	if err := tempTable.Delete(ctx); err != nil {
		log.Debug().Err(err).Msgf("Could not delete existing temp table %s (this is normal if it doesn't exist)", tempTableName)
	}

	err = tempTable.Create(ctx, &bigquery.TableMetadata{
		Schema:         userSchema,
		ExpirationTime: time.Now().Add(tempTableExpiration),
	})
	if err != nil {
		return fmt.Errorf("error creating temp_users table: %v", err)
	}

	// Ensure cleanup happens even if the function fails
	defer func() {
		if err := tempTable.Delete(ctx); err != nil {
			log.Warn().Err(err).Msgf("Failed to clean up temp table %s", tempTableName)
		} else {
			log.Info().Msgf("Successfully cleaned up temp table %s", tempTableName)
		}
	}()

	// Insert the rows into the temporary table in batches to avoid "request too large" errors.
	inserter := tempTable.Inserter()
	for batchStart := 0; batchStart < len(userRows); batchStart += maxInsertBatchSize {
		batchEnd := min(batchStart+maxInsertBatchSize, len(userRows))
		batch := userRows[batchStart:batchEnd]
		err = inserter.Put(ctx, batch)
		if err != nil {
			return fmt.Errorf(
				"error inserting batch %d-%d into %s: %w",
				batchStart, batchEnd, tempTableName, err,
			)
		}
		log.Debug().Msgf(
			"Inserted batch of %d rows into %s (%d/%d)",
			len(batch), tempTableName, batchEnd, len(userRows),
		)
	}

	// Replace the contents of the user table with the contents of the temporary table.
	log.Info().Msgf("Syncing users from %s to %s", tempTableName, userTableName)
	err = s.updateTable(
		ctx,
		fmt.Sprintf("`%s.%s`", s.tempDatasetName, tempTableName),
		fmt.Sprintf("`%s.%s`", s.datasetName, userTableName),
		[]string{
			"id", "email", "tenant_ids", "created_at", "in_usa",
			"stripe_customer_id", "blocked", "idp_user_ids", "subscription_id",
			"orb_customer_id", "check_subscription_status", "billing_method", "orb_subscription_id",
			"subscription_creation_id", "suspensions", "tier_change", "subscription_creation_info",
			"suspension_exempt",
		},
		"id",
	)
	if err != nil {
		return fmt.Errorf("error replacing user table: %w", err)
	}

	log.Info().Msgf("Synced %d users", len(userRows))
	return nil
}

// Helper for merging the contents of srcTable with those of dstTable. Requires both tables to have
// an identical schema.
func (s *SyncJob) updateTable(ctx context.Context, srcTable, dstTable string, columns []string, joinColumn string) error {
	// Generic MERGE query that works for tenant, user, and subscription tables
	queryTemplate := `
		MERGE {{.dstTable}} D
		USING {{.srcTable}} S
		ON D.{{.joinColumn}} = S.{{.joinColumn}}
		WHEN MATCHED THEN
			UPDATE SET {{range $i, $col := .columns}}{{if $i}},{{end}}
				{{$col}} = S.{{$col}}{{end}}
		WHEN NOT MATCHED THEN
			INSERT ({{range $i, $col := .columns}}{{if $i}},{{end}}{{$col}}{{end}})
			VALUES ({{range $i, $col := .columns}}{{if $i}},{{end}}S.{{$col}}{{end}})
	`
	t := template.New("query")
	t, err := t.Parse(queryTemplate)
	if err != nil {
		return fmt.Errorf("error parsing query template: %w", err)
	}

	var queryBuf bytes.Buffer
	err = t.Execute(
		&queryBuf,
		map[string]interface{}{
			"srcTable":   srcTable,
			"dstTable":   dstTable,
			"columns":    columns,
			"joinColumn": joinColumn,
		},
	)
	if err != nil {
		return fmt.Errorf("error executing query template: %w", err)
	}

	query := s.bqClient.Query(queryBuf.String())
	_, err = query.Read(ctx)
	if err != nil {
		return fmt.Errorf("error running query: %w", err)
	}
	return nil
}

// Convert a tenant proto to a row that can be written to BigQuery.
func toTenantRow(tenant *tenantpb.Tenant) *TenantRow {
	result := &TenantRow{
		ID:             tenant.Id,
		Name:           tenant.Name,
		ShardNamespace: tenant.ShardNamespace,
		Cloud:          tenant.Cloud,
	}

	// Set tier if present
	if tenant.Tier != tenantpb.TenantTier_TENANT_TIER_UNKNOWN {
		result.Tier = bigquery.NullString{
			Valid:     true,
			StringVal: tenant.Tier.String(),
		}
	}

	// Set other namespace if present
	if tenant.OtherNamespace != "" {
		result.OtherNamespace = bigquery.NullString{
			Valid:     true,
			StringVal: tenant.OtherNamespace,
		}
	}

	// Set encryption key name if present
	if tenant.EncryptionKeyName != "" {
		result.EncryptionKeyName = bigquery.NullString{
			Valid:     true,
			StringVal: tenant.EncryptionKeyName,
		}
	}

	// Set encryption key TTL if present
	if tenant.EncryptionKeyTtl != nil {
		result.EncryptionKeyTTL = bigquery.NullInt64{
			Valid: true,
			Int64: int64(tenant.EncryptionKeyTtl.Seconds),
		}
	}

	// Set version if present
	if tenant.Version != "" {
		result.Version = bigquery.NullString{
			Valid:     true,
			StringVal: tenant.Version,
		}
	}

	// Set deleted_at if present
	if tenant.DeletedAt != "" {
		deletedAt, err := time.Parse(time.RFC3339, tenant.DeletedAt)
		if err != nil {
			// Log the error but continue to write the row in this case.
			log.Warn().Err(err).Msgf(
				"Failed to parse deleted_at %s for tenant %s", tenant.DeletedAt, tenant.Id,
			)
		} else {
			result.DeletedAt = bigquery.NullTimestamp{
				Valid:     true,
				Timestamp: deletedAt,
			}
		}
	}

	// Set tenant flags from config if present
	if tenant.Config != nil && tenant.Config.Configs != nil {
		// Support Access Control
		if val, ok := tenant.Config.Configs["support_access_control"]; ok {
			boolVal, err := strconv.ParseBool(val)
			if err == nil {
				result.SupportAccessControl = bigquery.NullBool{
					Valid: true,
					Bool:  boolVal,
				}
			}
		}

		// Multi-tenant Allowed
		if val, ok := tenant.Config.Configs["multi_tenant_allowed"]; ok {
			boolVal, err := strconv.ParseBool(val)
			if err == nil {
				result.MultiTenantAllowed = bigquery.NullBool{
					Valid: true,
					Bool:  boolVal,
				}
			}
		}

		// Support Tenant
		if val, ok := tenant.Config.Configs["support_tenant"]; ok {
			boolVal, err := strconv.ParseBool(val)
			if err == nil {
				result.SupportTenant = bigquery.NullBool{
					Valid: true,
					Bool:  boolVal,
				}
			}
		}

		// Default Support Tenant
		if val, ok := tenant.Config.Configs["default_support_tenant"]; ok {
			boolVal, err := strconv.ParseBool(val)
			if err == nil {
				result.DefaultSupportTenant = bigquery.NullBool{
					Valid: true,
					Bool:  boolVal,
				}
			}
		}

		// Slackbot Allowlist Channels
		if val, ok := tenant.Config.Configs["slackbot_allowlist_channels"]; ok {
			// Parse JSON array of strings
			var channels []string
			if err := json.Unmarshal([]byte(val), &channels); err == nil {
				result.SlackbotAllowlist = channels
			}
		}

		// Block Genie Request Access
		if val, ok := tenant.Config.Configs["block_genie_request_access"]; ok {
			boolVal, err := strconv.ParseBool(val)
			if err == nil {
				result.BlockGenieRequestAccess = bigquery.NullBool{
					Valid: true,
					Bool:  boolVal,
				}
			}
		}

		// IP Allowlist
		if val, ok := tenant.Config.Configs["ip_allowlist"]; ok {
			result.IpAllowlist = bigquery.NullString{
				Valid:     true,
				StringVal: val,
			}
		}

		// Is Self-Serve Team
		if val, ok := tenant.Config.Configs["is_self_serve_team"]; ok {
			boolVal, err := strconv.ParseBool(val)
			if err == nil {
				result.IsSelfServeTeam = bigquery.NullBool{
					Valid: true,
					Bool:  boolVal,
				}
			}
		}

		// Is Legacy Self-Serve Team
		if val, ok := tenant.Config.Configs["is_legacy_self_serve_team"]; ok {
			boolVal, err := strconv.ParseBool(val)
			if err == nil {
				result.IsLegacySelfServeTeam = bigquery.NullBool{
					Valid: true,
					Bool:  boolVal,
				}
			}
		}
	}

	// Set auth configuration fields if present
	if tenant.AuthConfiguration != nil {
		// Domain
		if tenant.AuthConfiguration.Domain != "" {
			result.Domain = bigquery.NullString{
				Valid:     true,
				StringVal: tenant.AuthConfiguration.Domain,
			}
		}

		// Username domains
		if len(tenant.AuthConfiguration.UsernameDomains) > 0 {
			result.UsernameDomains = tenant.AuthConfiguration.UsernameDomains
		}

		// Email address domains
		if len(tenant.AuthConfiguration.EmailAddressDomains) > 0 {
			result.EmailAddressDomains = tenant.AuthConfiguration.EmailAddressDomains
		}

		// Allowed identity providers
		if len(tenant.AuthConfiguration.AllowedIdentityProviders) > 0 {
			result.AllowedIdentityProviders = tenant.AuthConfiguration.AllowedIdentityProviders
		}
	}

	return result
}

// Convert a user proto to a row that can be written to BigQuery.
func toUserRow(user *auth_entities.User) *UserRow {
	result := &UserRow{
		ID:            user.Id,
		Email:         user.Email,
		TenantIDs:     user.Tenants,
		CreatedAt:     user.CreatedAt.AsTime(),
		Blocked:       user.Blocked,
		IDPUserIDs:    user.IdpUserIds,
		BillingMethod: user.BillingMethod.String(),
	}
	if user.InUsa != nil {
		result.InUsa = bigquery.NullTimestamp{
			Valid:     true,
			Timestamp: user.InUsa.AsTime(),
		}
	}
	if user.StripeCustomerId != "" {
		result.StripeCustomerID = bigquery.NullString{
			Valid:     true,
			StringVal: user.StripeCustomerId,
		}
	}
	if user.GetSubscriptionId() != "" {
		result.SubscriptionID = bigquery.NullString{
			Valid:     true,
			StringVal: *user.SubscriptionId,
		}
	}
	if user.OrbCustomerId != "" {
		result.OrbCustomerID = bigquery.NullString{
			Valid:     true,
			StringVal: user.OrbCustomerId,
		}
	}

	// Set check_subscription_status field to false by default
	// The User proto doesn't have this field yet
	result.CheckSubscriptionStatus = bigquery.NullBool{
		Valid: true,
		Bool:  false,
	}

	if user.OrbSubscriptionId != "" {
		result.OrbSubscriptionID = bigquery.NullString{
			Valid:     true,
			StringVal: user.OrbSubscriptionId,
		}
	}
	if user.SubscriptionCreationId != "" {
		result.SubscriptionCreationID = bigquery.NullString{
			Valid:     true,
			StringVal: user.SubscriptionCreationId,
		}
	}

	// Handle suspensions
	if len(user.Suspensions) > 0 {
		suspensions := make([]UserSuspensionRow, 0, len(user.Suspensions))
		for _, suspension := range user.Suspensions {
			suspensions = append(suspensions, UserSuspensionRow{
				SuspensionID:   suspension.SuspensionId,
				CreatedTime:    suspension.CreatedTime.AsTime(),
				SuspensionType: suspension.SuspensionType.String(),
				Evidence:       suspension.Evidence,
			})
		}
		result.Suspensions = suspensions
	}

	// Handle suspension exempt flag
	result.SuspensionExempt = user.SuspensionExempt

	// Handle tier change info
	if user.TierChange != nil {
		tierChange := &UserTierChangeRow{
			ID:         user.TierChange.Id,
			TargetTier: user.TierChange.TargetTier.String(),
		}

		// Set created_at if present
		if user.TierChange.CreatedAt != nil {
			tierChange.CreatedAt = user.TierChange.CreatedAt.AsTime()
		} else {
			// Default to current time if not set
			tierChange.CreatedAt = time.Now()
		}

		// Set updated_at if present
		if user.TierChange.UpdatedAt != nil {
			tierChange.UpdatedAt = user.TierChange.UpdatedAt.AsTime()
		} else {
			// Default to same as created_at if not set
			tierChange.UpdatedAt = tierChange.CreatedAt
		}

		result.TierChange = tierChange
	} else {
		result.TierChange = &UserTierChangeRow{}
	}

	// Handle subscription creation info
	if user.SubscriptionCreationInfo != nil {
		subscriptionCreationInfo := &UserSubscriptionCreationInfoRow{
			ID:     user.SubscriptionCreationInfo.Id,
			Status: user.SubscriptionCreationInfo.Status.String(),
		}

		// Set created_at if present
		if user.SubscriptionCreationInfo.CreatedAt != nil {
			subscriptionCreationInfo.CreatedAt = user.SubscriptionCreationInfo.CreatedAt.AsTime()
		} else {
			// Default to current time if not set
			subscriptionCreationInfo.CreatedAt = time.Now()
		}

		// Set updated_at if present
		if user.SubscriptionCreationInfo.UpdatedAt != nil {
			subscriptionCreationInfo.UpdatedAt = user.SubscriptionCreationInfo.UpdatedAt.AsTime()
		} else {
			// Default to same as created_at if not set
			subscriptionCreationInfo.UpdatedAt = subscriptionCreationInfo.CreatedAt
		}

		result.SubscriptionCreationInfo = subscriptionCreationInfo
	} else {
		result.SubscriptionCreationInfo = &UserSubscriptionCreationInfoRow{}
	}

	return result
}

// syncSubscriptions fetches all subscriptions from auth-central and syncs them to BigQuery.
func (s *SyncJob) syncSubscriptions(
	ctx context.Context,
	teamManagementClient teammanagementclient.TeamManagementClient,
	tokenExchangeClient tokenexchange.TokenExchangeClient,
) error {
	// Get a service-to-service token with AUTH_RW scope
	// We can use empty strings for tenant ID and namespace as mentioned in the requirements
	sessionId := requestcontext.NewRandomRequestSessionId()
	token, err := tokenExchangeClient.GetSignedTokenForServiceWithNamespace(
		ctx, "", "", []tokenexchangepb.Scope{tokenexchangepb.Scope_AUTH_R},
	)
	if err != nil {
		return fmt.Errorf("error getting service token: %w", err)
	}

	// Create request context with the token
	requestCtx := requestcontext.New(
		requestcontext.NewRandomRequestId(), sessionId, "request-insight-sync", token,
	)

	// Create a temporary table for the current list of subscriptions
	tempTableName := fmt.Sprintf("temp_subscription_%d", time.Now().Unix())
	log.Info().Msgf("Creating temp table %s", tempTableName)
	subscriptionSchema, err := bigquery.InferSchema(SubscriptionRow{})
	if err != nil {
		return fmt.Errorf("error inferring schema from subscriptionRow struct: %w", err)
	} else if len(subscriptionSchema) == 0 {
		return fmt.Errorf("inferred subscription schema is empty")
	}

	tempTable := s.bqClient.Dataset(s.tempDatasetName).Table(tempTableName)

	// Delete the table if it already exists (from a previous failed run)
	if err := tempTable.Delete(ctx); err != nil {
		log.Debug().Err(err).Msgf("Could not delete existing temp table %s (this is normal if it doesn't exist)", tempTableName)
	}

	err = tempTable.Create(ctx, &bigquery.TableMetadata{
		Schema:         subscriptionSchema,
		ExpirationTime: time.Now().Add(tempTableExpiration),
	})
	if err != nil {
		return fmt.Errorf("error creating temp_subscriptions table: %v", err)
	}

	// Ensure cleanup happens even if the function fails
	defer func() {
		if err := tempTable.Delete(ctx); err != nil {
			log.Warn().Err(err).Msgf("Failed to clean up temp table %s", tempTableName)
		} else {
			log.Info().Msgf("Successfully cleaned up temp table %s", tempTableName)
		}
	}()

	// Get subscriptions using the iterator to stream them directly to BigQuery
	batchSize := uint32(maxInsertBatchSize)
	iterator := teamManagementClient.GetSubscriptionIterator(ctx, requestCtx, batchSize)

	// Insert the rows into the temporary table
	inserter := tempTable.Inserter()
	totalCount := 0

	// Create a buffer to batch subscriptions for more efficient BigQuery inserts
	batch := make([]*SubscriptionRow, 0, maxInsertBatchSize)

	// Helper function to insert a batch and update counts
	insertBatch := func() error {
		if len(batch) == 0 {
			return nil
		}

		err := inserter.Put(ctx, batch)
		if err != nil {
			return err
		}

		batchCount := len(batch)
		totalCount += batchCount

		// Reset the batch
		batch = make([]*SubscriptionRow, 0, maxInsertBatchSize)
		return nil
	}

	for subscription, err := range iterator {
		if err != nil {
			return fmt.Errorf("error fetching subscriptions: %w", err)
		}

		// Convert subscription to a row that can be written to BigQuery
		batch = append(batch, toSubscriptionRow(subscription))

		// When we reach the batch size, insert into BigQuery
		if len(batch) >= maxInsertBatchSize {
			if err := insertBatch(); err != nil {
				log.Error().Err(err).Msgf("Error inserting batch into %s", tempTableName)
				return fmt.Errorf("error inserting batch into %s: %w", tempTableName, err)
			}
		}
	}

	// Insert any remaining items in the final batch
	if err := insertBatch(); err != nil {
		return fmt.Errorf("error inserting final batch into %s: %w", tempTableName, err)
	}

	// Replace the contents of the subscription table with the contents of the temporary table
	log.Info().Msgf("Syncing subscriptions from %s to %s", tempTableName, subscriptionTableName)
	err = s.updateTable(
		ctx,
		fmt.Sprintf("`%s.%s`", s.tempDatasetName, tempTableName),
		fmt.Sprintf("`%s.%s`", s.datasetName, subscriptionTableName),
		[]string{
			"subscription_id", "stripe_customer_id", "price_id", "status", "seats",
			"start_date", "trial_end", "end_date", "cancel_at_period_end", "has_payment_method",
			"tenant_id", "user_id", "created_at", "updated_at", "orb_customer_id", "orb_status",
			"has_unpaid_invoice", "external_plan_id", "billing_method", "seats_id",
			"included_credits_id", "credits_per_month", "usage_balance_depleted",
		},
		"subscription_id",
	)
	if err != nil {
		return fmt.Errorf("error replacing subscription table: %w", err)
	}

	log.Info().Msgf("Synced %d subscriptions", totalCount)
	return nil
}

// Convert a subscription to a row that can be written to BigQuery.
func toSubscriptionRow(subscription *auth_entities.Subscription) *SubscriptionRow {
	// Convert status enum to string
	statusStr := subscription.Status.String()

	result := &SubscriptionRow{
		SubscriptionID:    subscription.SubscriptionId,
		StripeCustomerID:  subscription.StripeCustomerId,
		PriceID:           subscription.PriceId,
		Status:            statusStr,
		Seats:             subscription.Seats,
		CancelAtPeriodEnd: subscription.CancelAtPeriodEnd,
		HasPaymentMethod:  subscription.HasPaymentMethod,
	}

	// Handle nullable timestamp fields
	if subscription.StartDate != nil {
		result.StartDate = bigquery.NullTimestamp{
			Valid:     true,
			Timestamp: subscription.StartDate.AsTime(),
		}
	}

	if subscription.TrialEnd != nil {
		result.TrialEnd = bigquery.NullTimestamp{
			Valid:     true,
			Timestamp: subscription.TrialEnd.AsTime(),
		}
	}

	if subscription.EndDate != nil {
		result.EndDate = bigquery.NullTimestamp{
			Valid:     true,
			Timestamp: subscription.EndDate.AsTime(),
		}
	}

	if subscription.CreatedAt != nil {
		result.CreatedAt = bigquery.NullTimestamp{
			Valid:     true,
			Timestamp: subscription.CreatedAt.AsTime(),
		}
	}

	if subscription.UpdatedAt != nil {
		result.UpdatedAt = bigquery.NullTimestamp{
			Valid:     true,
			Timestamp: subscription.UpdatedAt.AsTime(),
		}
	}

	// Handle tenant or user ID from the oneof field
	switch owner := subscription.Owner.(type) {
	case *auth_entities.Subscription_TenantId:
		result.TenantID = bigquery.NullString{
			Valid:     true,
			StringVal: owner.TenantId,
		}
	case *auth_entities.Subscription_UserId:
		result.UserID = bigquery.NullString{
			Valid:     true,
			StringVal: owner.UserId,
		}
	}

	// Handle new Orb-related fields
	if subscription.OrbCustomerId != "" {
		result.OrbCustomerID = bigquery.NullString{
			Valid:     true,
			StringVal: subscription.OrbCustomerId,
		}
	}

	if subscription.OrbStatus != auth_entities.Subscription_ORB_STATUS_UNKNOWN {
		result.OrbStatus = bigquery.NullString{
			Valid:     true,
			StringVal: subscription.OrbStatus.String(),
		}
	}

	result.HasUnpaidInvoice = bigquery.NullBool{
		Valid: true,
		Bool:  subscription.HasUnpaidInvoice,
	}

	if subscription.ExternalPlanId != "" {
		result.ExternalPlanID = bigquery.NullString{
			Valid:     true,
			StringVal: subscription.ExternalPlanId,
		}
	}

	if subscription.BillingMethod != auth_entities.BillingMethod_BILLING_METHOD_UNKNOWN {
		result.BillingMethod = bigquery.NullString{
			Valid:     true,
			StringVal: subscription.BillingMethod.String(),
		}
	}

	if subscription.SeatsId != "" {
		result.SeatsID = bigquery.NullString{
			Valid:     true,
			StringVal: subscription.SeatsId,
		}
	}

	if subscription.IncludedCreditsId != "" {
		result.IncludedCreditsID = bigquery.NullString{
			Valid:     true,
			StringVal: subscription.IncludedCreditsId,
		}
	}

	if subscription.CreditsPerMonth != 0 {
		result.CreditsPerMonth = bigquery.NullFloat64{
			Valid:   true,
			Float64: subscription.CreditsPerMonth,
		}
	}

	result.UsageBalanceDepleted = bigquery.NullBool{
		Valid: true,
		Bool:  subscription.UsageBalanceDepleted,
	}

	return result
}

// syncTenantSubscriptionMappings fetches all tenant subscription mappings from auth-central and syncs them to BigQuery.
func (s *SyncJob) syncTenantSubscriptionMappings(
	ctx context.Context,
	authClient authclient.AuthClient,
	tokenExchangeClient tokenexchange.TokenExchangeClient,
) error {
	log.Info().Msg("Starting TenantSubscriptionMapping sync")

	// Create a temporary table for the current list of tenant subscription mappings
	tempTableName := fmt.Sprintf("temp_tenant_subscription_mapping_%d", time.Now().Unix())
	log.Info().Msgf("Creating temp table %s", tempTableName)
	mappingSchema, err := bigquery.InferSchema(TenantSubscriptionMappingRow{})
	if err != nil {
		return fmt.Errorf("error inferring schema from TenantSubscriptionMappingRow struct: %w", err)
	} else if len(mappingSchema) == 0 {
		return fmt.Errorf("inferred tenant subscription mapping schema is empty")
	}

	tempTable := s.bqClient.Dataset(s.tempDatasetName).Table(tempTableName)

	// Delete the table if it already exists (from a previous failed run)
	if err := tempTable.Delete(ctx); err != nil {
		log.Debug().Err(err).Msgf("Could not delete existing temp table %s (this is normal if it doesn't exist)", tempTableName)
	}

	err = tempTable.Create(ctx, &bigquery.TableMetadata{
		Schema:         mappingSchema,
		ExpirationTime: time.Now().Add(tempTableExpiration),
	})
	if err != nil {
		return fmt.Errorf("error creating temp_tenant_subscription_mapping table: %v", err)
	}

	// Ensure cleanup happens even if the function fails
	defer func() {
		if err := tempTable.Delete(ctx); err != nil {
			log.Warn().Err(err).Msgf("Failed to clean up temp table %s", tempTableName)
		} else {
			log.Info().Msgf("Successfully cleaned up temp table %s", tempTableName)
		}
	}()

	// Get a service-to-service token with AUTH_R scope
	sessionId := requestcontext.NewRandomRequestSessionId()
	token, err := tokenExchangeClient.GetSignedTokenForServiceWithNamespace(
		ctx, "", "", []tokenexchangepb.Scope{tokenexchangepb.Scope_AUTH_R},
	)
	if err != nil {
		return fmt.Errorf("error getting service token: %w", err)
	}

	// Create request context with the token
	requestCtx := requestcontext.New(
		requestcontext.NewRandomRequestId(), sessionId, "request-insight-sync", token,
	)

	// Get tenant subscription mappings using the iterator
	batchSize := uint32(maxInsertBatchSize)
	iterator := s.teamManagementClient.GetTenantSubscriptionMappingIterator(ctx, requestCtx, batchSize)

	// Process mappings in batches
	var tenantSubscriptionMappingRows []*TenantSubscriptionMappingRow
	totalCount := 0

	for tenantSubscriptionMapping, err := range iterator {
		if err != nil {
			log.Error().Err(err).Msg("Error iterating over tenant subscription mappings")
			return fmt.Errorf("error iterating over tenant subscription mappings: %w", err)
		}

		// Convert to BigQuery row
		row := toTenantSubscriptionMappingRow(tenantSubscriptionMapping)
		tenantSubscriptionMappingRows = append(tenantSubscriptionMappingRows, row)
		totalCount++

		// Insert in batches
		if len(tenantSubscriptionMappingRows) >= maxInsertBatchSize {
			log.Info().Msgf("Inserting batch of %d tenant subscription mappings", len(tenantSubscriptionMappingRows))
			inserter := tempTable.Inserter()
			if err := inserter.Put(ctx, tenantSubscriptionMappingRows); err != nil {
				return fmt.Errorf("error inserting tenant subscription mapping batch: %w", err)
			}
			tenantSubscriptionMappingRows = tenantSubscriptionMappingRows[:0] // Reset slice
		}
	}

	// Insert any remaining rows
	if len(tenantSubscriptionMappingRows) > 0 {
		log.Info().Msgf("Inserting final batch of %d tenant subscription mappings", len(tenantSubscriptionMappingRows))
		inserter := tempTable.Inserter()
		if err := inserter.Put(ctx, tenantSubscriptionMappingRows); err != nil {
			return fmt.Errorf("error inserting final tenant subscription mapping batch: %w", err)
		}
	}

	// Replace the contents of the tenant subscription mapping table with the contents of the temporary table
	log.Info().Msgf("Syncing tenant subscription mappings from %s to %s", tempTableName, tenantSubscriptionMappingTableName)
	err = s.updateTable(
		ctx,
		fmt.Sprintf("`%s.%s`", s.tempDatasetName, tempTableName),
		fmt.Sprintf("`%s.%s`", s.datasetName, tenantSubscriptionMappingTableName),
		[]string{
			"tenant_id", "stripe_subscription_id", "stripe_customer_id", "orb_customer_id",
			"orb_subscription_id", "billing_method", "created_at", "plan_change_id",
			"plan_change_target_orb_plan_id", "plan_change_created_at", "plan_change_updated_at",
		},
		"tenant_id",
	)
	if err != nil {
		return fmt.Errorf("error replacing tenant subscription mapping table: %w", err)
	}

	log.Info().Msgf("Synced %d tenant subscription mappings", totalCount)
	return nil
}

// Convert a tenant subscription mapping to a row that can be written to BigQuery.
func toTenantSubscriptionMappingRow(mapping *auth_entities.TenantSubscriptionMapping) *TenantSubscriptionMappingRow {
	result := &TenantSubscriptionMappingRow{
		TenantID: mapping.TenantId,
	}

	// Handle nullable string fields
	if mapping.StripeSubscriptionId != "" {
		result.StripeSubscriptionID = bigquery.NullString{
			Valid:     true,
			StringVal: mapping.StripeSubscriptionId,
		}
	}

	if mapping.StripeCustomerId != "" {
		result.StripeCustomerID = bigquery.NullString{
			Valid:     true,
			StringVal: mapping.StripeCustomerId,
		}
	}

	if mapping.OrbCustomerId != "" {
		result.OrbCustomerID = bigquery.NullString{
			Valid:     true,
			StringVal: mapping.OrbCustomerId,
		}
	}

	if mapping.OrbSubscriptionId != "" {
		result.OrbSubscriptionID = bigquery.NullString{
			Valid:     true,
			StringVal: mapping.OrbSubscriptionId,
		}
	}

	// Handle billing method
	if mapping.BillingMethod != auth_entities.BillingMethod_BILLING_METHOD_UNKNOWN {
		result.BillingMethod = bigquery.NullString{
			Valid:     true,
			StringVal: mapping.BillingMethod.String(),
		}
	}

	// Handle created_at timestamp
	if mapping.CreatedAt != nil {
		result.CreatedAt = bigquery.NullTimestamp{
			Valid:     true,
			Timestamp: mapping.CreatedAt.AsTime(),
		}
	}

	// Handle plan change info if present
	if mapping.PlanChange != nil {
		if mapping.PlanChange.Id != "" {
			result.PlanChangeID = bigquery.NullString{
				Valid:     true,
				StringVal: mapping.PlanChange.Id,
			}
		}

		if mapping.PlanChange.TargetOrbPlanId != "" {
			result.PlanChangeTargetOrbPlanID = bigquery.NullString{
				Valid:     true,
				StringVal: mapping.PlanChange.TargetOrbPlanId,
			}
		}

		if mapping.PlanChange.CreatedAt != nil {
			result.PlanChangeCreatedAt = bigquery.NullTimestamp{
				Valid:     true,
				Timestamp: mapping.PlanChange.CreatedAt.AsTime(),
			}
		}

		if mapping.PlanChange.UpdatedAt != nil {
			result.PlanChangeUpdatedAt = bigquery.NullTimestamp{
				Valid:     true,
				Timestamp: mapping.PlanChange.UpdatedAt.AsTime(),
			}
		}
	}

	return result
}

// Convert a user tenant mapping to a row that can be written to BigQuery.
func toUserTenantMappingRow(mapping *auth_entities.UserTenantMapping) *UserTenantMappingRow {
	result := &UserTenantMappingRow{
		Tenant: mapping.Tenant,
		UserID: mapping.UserId,
	}

	// Convert customer UI roles to strings
	if len(mapping.CustomerUiRoles) > 0 {
		roles := make([]string, 0, len(mapping.CustomerUiRoles))
		for _, role := range mapping.CustomerUiRoles {
			roles = append(roles, role.String())
		}
		result.CustomerUIRoles = roles
	}

	return result
}

// Sync user tenant mappings from auth-central to BigQuery.
func (s *SyncJob) syncUserTenantMappings(ctx context.Context) error {
	log.Info().Msg("Starting user tenant mapping sync")

	// Get a service-to-service token with AUTH_R scope
	sessionId := requestcontext.NewRandomRequestSessionId()
	token, err := s.tokenExchangeClient.GetSignedTokenForServiceWithNamespace(
		ctx, "", "", []tokenexchangepb.Scope{tokenexchangepb.Scope_AUTH_R},
	)
	if err != nil {
		return fmt.Errorf("error getting service token: %w", err)
	}

	requestCtx := requestcontext.New(
		requestcontext.NewRandomRequestId(), sessionId, "request-insight-sync", token,
	)

	// Get all user tenant mappings from auth-central
	userTenantMappings, err := s.teamManagementClient.GetUserTenantMappings(ctx, requestCtx)
	if err != nil {
		return fmt.Errorf("failed to get user tenant mappings: %w", err)
	}

	log.Info().Int("count", len(userTenantMappings)).Msg("Retrieved user tenant mappings")

	// Convert to BigQuery rows and deduplicate by tenant+user_id
	rowMap := make(map[string]*UserTenantMappingRow)
	for _, mapping := range userTenantMappings {
		row := toUserTenantMappingRow(mapping)
		key := fmt.Sprintf("%s#%s", row.Tenant, row.UserID)
		rowMap[key] = row // This will overwrite duplicates with the last occurrence
	}

	// Convert map back to slice
	rows := make([]*UserTenantMappingRow, 0, len(rowMap))
	for _, row := range rowMap {
		rows = append(rows, row)
	}

	// Create a temporary table for the current list of user tenant mappings
	tempTableName := fmt.Sprintf("temp_user_tenant_mapping_%d", time.Now().Unix())
	log.Info().Msgf("Creating temp table %s", tempTableName)
	userTenantMappingSchema, err := bigquery.InferSchema(UserTenantMappingRow{})
	if err != nil {
		return fmt.Errorf("error inferring schema from UserTenantMappingRow struct: %w", err)
	} else if len(userTenantMappingSchema) == 0 {
		return fmt.Errorf("inferred user tenant mapping schema is empty")
	}
	tempTable := s.bqClient.Dataset(s.tempDatasetName).Table(tempTableName)

	// Delete the table if it already exists (from a previous failed run)
	if err := tempTable.Delete(ctx); err != nil {
		// Ignore "not found" errors, but log other errors
		log.Debug().Err(err).Msgf("Could not delete existing temp table %s (this is normal if it doesn't exist)", tempTableName)
	}

	err = tempTable.Create(ctx, &bigquery.TableMetadata{
		Schema:         userTenantMappingSchema,
		ExpirationTime: time.Now().Add(tempTableExpiration),
	})
	if err != nil {
		return fmt.Errorf("error creating temp_user_tenant_mapping table: %v", err)
	}

	// Ensure cleanup happens even if the function fails
	defer func() {
		if err := tempTable.Delete(ctx); err != nil {
			log.Warn().Err(err).Msgf("Failed to clean up temp table %s", tempTableName)
		} else {
			log.Info().Msgf("Successfully cleaned up temp table %s", tempTableName)
		}
	}()

	// Insert the rows into the temporary table in batches to avoid "request too large" errors
	inserter := tempTable.Inserter()
	for batchStart := 0; batchStart < len(rows); batchStart += maxInsertBatchSize {
		batchEnd := min(batchStart+maxInsertBatchSize, len(rows))
		batch := rows[batchStart:batchEnd]
		err = inserter.Put(ctx, batch)
		if err != nil {
			return fmt.Errorf(
				"error inserting batch %d-%d into %s: %w",
				batchStart, batchEnd, tempTableName, err,
			)
		}
		log.Debug().Msgf(
			"Inserted batch of %d rows into %s (%d/%d)",
			len(batch), tempTableName, batchEnd, len(rows),
		)
	}

	// Replace the contents of the user tenant mapping table with the contents of the temporary table
	log.Info().Msgf("Syncing user tenant mappings from %s to %s", tempTableName, userTenantMappingTableName)
	err = s.updateUserTenantMappingTable(
		ctx,
		fmt.Sprintf("`%s.%s`", s.tempDatasetName, tempTableName),
		fmt.Sprintf("`%s.%s`", s.datasetName, userTenantMappingTableName),
	)
	if err != nil {
		return fmt.Errorf("error replacing user tenant mapping table: %w", err)
	}

	log.Info().Msgf("Synced %d user tenant mappings", len(rows))
	return nil
}

// Helper for merging the contents of srcTable with those of dstTable for user tenant mapping table.
// This handles the composite key (tenant, user_id) properly.
func (s *SyncJob) updateUserTenantMappingTable(ctx context.Context, srcTable, dstTable string) error {
	// MERGE query specifically for user tenant mapping table with composite key
	query := fmt.Sprintf(`
		MERGE %s D
		USING %s S
		ON D.tenant = S.tenant AND D.user_id = S.user_id
		WHEN MATCHED THEN
			UPDATE SET
				tenant = S.tenant,
				user_id = S.user_id,
				customer_ui_roles = S.customer_ui_roles
		WHEN NOT MATCHED THEN
			INSERT (tenant, user_id, customer_ui_roles)
			VALUES (S.tenant, S.user_id, S.customer_ui_roles)
	`, dstTable, srcTable)

	log.Debug().Msgf("Executing query: %s", query)
	q := s.bqClient.Query(query)
	q.DefaultDatasetID = s.datasetName
	job, err := q.Run(ctx)
	if err != nil {
		return fmt.Errorf("error running query: %w", err)
	}
	status, err := job.Wait(ctx)
	if err != nil {
		return fmt.Errorf("error waiting for query: %w", err)
	}
	if err := status.Err(); err != nil {
		return fmt.Errorf("query failed: %w", err)
	}
	return nil
}
