package authclient

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"

	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
)

func TestTeamManagementClientInterface(t *testing.T) {
	// Verify that teamManagementClientImpl implements TeamManagementClient interface
	var _ TeamManagementClient = &teamManagementClientImpl{}

	// This test just verifies that the interface is correctly implemented
	// and that all methods exist with the correct signatures
	assert.True(t, true, "Interface implementation verified")
}

func TestMethodSignatures(t *testing.T) {
	// Test that all methods have the correct signatures by checking they can be assigned
	// to function variables with the expected types

	var getSubscriptionIterator func(context.Context, *requestcontext.RequestContext, uint32) interface{}
	var getTenantSubscriptionMappingIterator func(context.Context, *requestcontext.RequestContext, uint32) interface{}
	var getUserTenantMappingIterator func(context.Context, *requestcontext.RequestContext, uint32) interface{}
	var getUserTenantMappings func(context.Context, *requestcontext.RequestContext) (interface{}, error)

	client := &teamManagementClientImpl{}

	// These assignments will fail to compile if the method signatures are wrong
	getSubscriptionIterator = func(ctx context.Context, rc *requestcontext.RequestContext, bs uint32) interface{} {
		return client.GetSubscriptionIterator(ctx, rc, bs)
	}
	getTenantSubscriptionMappingIterator = func(ctx context.Context, rc *requestcontext.RequestContext, bs uint32) interface{} {
		return client.GetTenantSubscriptionMappingIterator(ctx, rc, bs)
	}
	getUserTenantMappingIterator = func(ctx context.Context, rc *requestcontext.RequestContext, bs uint32) interface{} {
		return client.GetUserTenantMappingIterator(ctx, rc, bs)
	}
	getUserTenantMappings = func(ctx context.Context, rc *requestcontext.RequestContext) (interface{}, error) {
		return client.GetUserTenantMappings(ctx, rc)
	}

	// Verify the function variables are not nil (they were assigned successfully)
	assert.NotNil(t, getSubscriptionIterator)
	assert.NotNil(t, getTenantSubscriptionMappingIterator)
	assert.NotNil(t, getUserTenantMappingIterator)
	assert.NotNil(t, getUserTenantMappings)
}
