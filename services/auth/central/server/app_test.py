"""Test Auth Central REST API exposed by Flask."""

import base64
import http.cookies
import re
import secrets
from dataclasses import dataclass
from typing import Generator
from urllib.parse import parse_qs, urlencode, urlparse

import config
import flask
import grpc
import pytest
from authlib.integrations.flask_client import OAuthError
from bs4 import BeautifulSoup
from flask import session
from pydantic import SecretStr

import base.feature_flags
from base.test_utils import bigtable_setup
from services.auth.central.server import app as auth_app
from services.auth.central.server import (
    auth_central_test_setup,
    auth_entities_pb2,
    auth_fixtures,
    front_end_token_service_pb2,
    front_end_token_service_pb2_grpc,
)
from services.auth.central.server.app import create_app
from services.tenant_watcher import tenant_watcher_pb2
from services.tenant_watcher.server import tenant_watcher_test_setup
from services.token_exchange.server import token_exchange_test_setup

TEST_TENANTS = [
    tenant_watcher_pb2.Tenant(
        id="foo567",
        name="test",
        shard_namespace="test",
        auth_configuration=tenant_watcher_pb2.AuthConfiguration(
            domain="test.com",
        ),
        tier=tenant_watcher_pb2.TenantTier.ENTERPRISE,
    ),
    tenant_watcher_pb2.Tenant(
        id="foo568",
        name="other",
        shard_namespace="test",
        auth_configuration=tenant_watcher_pb2.AuthConfiguration(
            domain="",
        ),
        tier=tenant_watcher_pb2.TenantTier.ENTERPRISE,
    ),
    tenant_watcher_pb2.Tenant(
        id="restrictsidp",
        name="restrictsidp",
        shard_namespace="test",
        auth_configuration=tenant_watcher_pb2.AuthConfiguration(
            domain="restrictsidp.com",
            allowed_identity_providers=["idp"],
        ),
        tier=tenant_watcher_pb2.TenantTier.ENTERPRISE,
    ),
    tenant_watcher_pb2.Tenant(
        id="bar567",
        name="bar",
        shard_namespace="bar",
        auth_configuration=tenant_watcher_pb2.AuthConfiguration(
            domain="bar.com",
        ),
        tier=tenant_watcher_pb2.TenantTier.COMMUNITY,
    ),
    tenant_watcher_pb2.Tenant(
        id="id-vanguard1",
        name="vanguard1",
        shard_namespace="vanguard",
        tier=tenant_watcher_pb2.TenantTier.COMMUNITY,
    ),
    tenant_watcher_pb2.Tenant(
        id="id-vanguard2",
        name="vanguard2",
        shard_namespace="vanguard",
        tier=tenant_watcher_pb2.TenantTier.COMMUNITY,
    ),
    tenant_watcher_pb2.Tenant(
        id="id-discovery1",
        name="discovery1",
        shard_namespace="discovery",
        tier=tenant_watcher_pb2.TenantTier.PROFESSIONAL,
    ),
    tenant_watcher_pb2.Tenant(
        id="id-discovery2",
        name="discovery2",
        shard_namespace="discovery",
        tier=tenant_watcher_pb2.TenantTier.PROFESSIONAL,
    ),
    tenant_watcher_pb2.Tenant(
        id="id-team1",
        name="team1",
        shard_namespace="discovery2",
        tier=tenant_watcher_pb2.TenantTier.PROFESSIONAL,
        config=tenant_watcher_pb2.Config(
            configs={"is_self_serve_team": "true"},
        ),
    ),
]


def get_self_serve_tenant():
    for tenant in TEST_TENANTS:
        if tenant.tier == tenant_watcher_pb2.TenantTier.PROFESSIONAL:
            return tenant
    assert False, "No self-serve tenant found"


def get_self_serve_team_tenant():
    for tenant in TEST_TENANTS:
        if (
            tenant.tier == tenant_watcher_pb2.TenantTier.PROFESSIONAL
            and tenant.config
            and tenant.config.configs.get("is_self_serve_team") == "true"
        ):
            return tenant
    assert False, "No self-serve team tenant found"


def get_enterprise_tenant():
    for tenant in TEST_TENANTS:
        if tenant.tier == tenant_watcher_pb2.TenantTier.ENTERPRISE:
            return tenant
    assert False, "No enterprise tenant found"


@pytest.fixture(scope="module")
def tenant_watcher_server():
    """Start a test tenant watcher server."""
    yield from tenant_watcher_test_setup.start_fake_tenant_watcher_server(
        tenants=TEST_TENANTS
    )


@pytest.fixture(scope="module")
def auth_central_bigtable(bigtable_table):
    """Create a test bigtable configuration."""
    return bigtable_setup.BigtableTable(
        instance=auth_fixtures.INSTANCE_ID,
        table_name=auth_fixtures.TABLE_NAME,
        project=auth_fixtures.PROJECT_ID,
    )


@pytest.fixture(scope="module")
def auth_central_grpc_config(
    tenant_watcher_server,
    token_exchange_server,
    auth_central_bigtable,
    async_ops_pubsub_topic,
    async_ops_pubsub_subscription,
):
    """Create gRPC server configuration."""
    return auth_central_test_setup.AuthCentralGrpcConfig(
        bigtable_table=auth_central_bigtable,
        auth_config=config.AuthConfig(
            token_exchange_endpoint=f"localhost:{token_exchange_server.port}",
        ),
        grpc_server=auth_central_test_setup.GrpcGoConfig(ports=[0]),
        tenant_watcher=config.TenantWatcherConfig(
            tenant_watcher_endpoint=f"localhost:{tenant_watcher_server}",
            api_proxy_hostname_domain="t.augmentcode.com",
        ),
        prometheus_bind_address="127.0.0.1:0",
        async_ops=auth_central_test_setup.AsyncOpsConfig(
            topic_name=auth_fixtures.ASYNC_OPS_TOPIC,
            subscription_name=auth_fixtures.ASYNC_OPS_SUBSCRIPTION,
        ),
    )


@pytest.fixture(scope="module")
def auth_central_grpc_server(auth_central_grpc_config):
    """Start the auth central gRPC server."""
    yield from auth_central_test_setup.start_auth_central_grpc_server(
        namespace="test",
        config=auth_central_grpc_config,
    )


@pytest.fixture
def feature_flags() -> Generator[base.feature_flags.LocalFeatureFlagSetter, None, None]:
    yield from base.feature_flags.feature_flag_fixture()


@pytest.fixture(scope="session")
def auth0_credentials_path(tmp_path_factory):
    tmp_dir = tmp_path_factory.mktemp("auth")
    creds_file = tmp_dir / "auth0-login.txt"
    creds_file.write_text("test-client-id\ntest-client-secret")
    return str(creds_file)


@pytest.fixture(scope="module")
def token_exchange_server(tenant_watcher_server):
    """Fixture to start the token exchange server."""
    config = token_exchange_test_setup.TokenExchangeConfig(
        port=0,  # Find some unused port
        tenant_watcher_endpoint=f"localhost:{tenant_watcher_server}",
        service_token_configs=[
            token_exchange_test_setup.TokenForServiceConfig(
                regex=".*",
                scopes=["AUTH_RW"],
                expiration="60m",
            )
        ],
    )
    yield from token_exchange_test_setup.start_token_exchange_server(config)


@pytest.fixture(scope="module")
def oidc_server():
    """Start an OIDC server for testing."""
    yield from auth_central_test_setup.start_fake_oidc_server()


@pytest.fixture(scope="module")
def auth_central_grpc_server_with_shutdown(auth_central_grpc_server):
    """Start the auth central HTTP server."""
    yield auth_central_grpc_server
    auth_central_grpc_server.shutdown()


@dataclass
class SharedFixtures:
    # Services
    auth_central_bigtable: bigtable_setup.BigtableTable
    tenant_watcher_server_port: int
    auth_central_grpc_server: auth_central_test_setup.AuthCentralGrpcServer
    token_exchange_server: token_exchange_test_setup.TokenExchangeServer
    oidc_server: auth_central_test_setup.FakeOidcServer

    # Files, tokens, etc.
    auth0_credentials_path: str

    test_tenant_id: str


@pytest.fixture(scope="module")
def shared_fixtures(
    auth_central_bigtable,
    tenant_watcher_server,
    auth_central_grpc_server_with_shutdown,
    token_exchange_server,
    oidc_server,
    auth0_credentials_path,
):
    """Fixtures shared amongst tests."""

    yield SharedFixtures(
        auth_central_bigtable=auth_central_bigtable,
        # Tenant watcher server currently only returns the port it is listening on
        tenant_watcher_server_port=tenant_watcher_server,
        auth_central_grpc_server=auth_central_grpc_server_with_shutdown,
        auth0_credentials_path=auth0_credentials_path,
        token_exchange_server=token_exchange_server,
        oidc_server=oidc_server,
        test_tenant_id=TEST_TENANTS[0].id,
    )


def create_config(
    tenant_watcher_port: int,
    backend_port: int,
    auth0_credentials_path: str,
    oidc_well_known_url: str,
    allowed_discovery_user_email_regex: str | None = None,
) -> config.Config:
    auth_config = config.AuthConfig(
        token_exchange_endpoint="http://localhost:8080",
    )
    client_config_map = {
        "augment-vscode-extension": config.OAuthClientConfig(
            name="Visual Studio Code",
            redirect_uris=[
                "vscode://augment.vscode-augment/auth/result",
                "cursor://augment.vscode-augment/auth/result",
                "vscode-insiders://augment.vscode-augment/auth/result",
                "code-oss://augment.vscode-augment/auth/result",
                "haystack-editor://augment.vscode-augment/auth/result",
                "trae://augment.vscode-augment/auth/result",
                "windsurf://augment.vscode-augment/auth/result",
                "windsurf-next://augment.vscode-augment/auth/result",
                "codellm://augment.vscode-augment/auth/result",
                "flexpilot://augment.vscode-augment/auth/result",
                "aide://augment.vscode-augment/auth/result",
                "pearai://augment.vscode-augment/auth/result",
                "vscodium://augment.vscode-augment/auth/result",
                "cursor-nightly://augment.vscode-augment/auth/result",
                "positron://augment.vscode-augment/auth/result",
                "void-editor://augment.vscode-augment/auth/result",
                "trae-cn://augment.vscode-augment/auth/result",
                "void://augment.vscode-augment/auth/result",
            ],
        ),
        "augment-intellij-plugin": config.OAuthClientConfig(
            name="Intellij",
            redirect_uris=[
                "http://127.0.0.1/api/augment/auth/result",
            ],
        ),
        "augment-vim-extension": config.OAuthClientConfig(
            name="Vim",
            redirect_uris=[""],
        ),
        "v": config.OAuthClientConfig(
            name="Vim",
            redirect_uris=[""],
        ),
        "customer-ui": config.OAuthClientConfig(
            name="Customer UI",
            redirect_uris=[
                "https://auth.augmentcode.com/auth/result",
            ],
            instant_redirect=True,
        ),
    }

    login_page_config = config.LoginPageConfig(
        auth0_login_url="https://login.dev.augmentcode.com/",
    )

    login_auth0_config = config.Auth0Config(
        credentials_path=auth0_credentials_path,
        server_metadata_url=oidc_well_known_url,
    )

    signup_auth0_config = config.Auth0Config(
        credentials_path=auth0_credentials_path,
        server_metadata_url=oidc_well_known_url,
    )

    gcp_config = config.GcpConfig(
        instance_id="test-instance",
        table_name="test-table",
    )

    grpc_config = config.GrpcConfig(
        client_mtls=None,
    )

    tenant_watcher_config = config.TenantWatcherConfig(
        tenant_watcher_endpoint=f"127.0.0.1:{tenant_watcher_port}",
        api_proxy_hostname_domain="t.augmentcode.com",
    )

    return config.Config(
        auth_config=auth_config,
        auth_url="https://auth.bogus.url/",
        backend_port=backend_port,
        client_config_map=client_config_map,
        code_ttl_seconds=600,
        feature_flags_sdk_key_path=None,
        dynamic_feature_flags_endpoint=None,
        gcp=gcp_config,
        grpc=grpc_config,
        login_page=login_page_config,
        login_auth0=login_auth0_config,
        tenant_watcher=tenant_watcher_config,
        public_bind_address="0.0.0.0:8080",
        prometheus_bind_address="0.0.0.0:9090",
        secrets_path="bogus-path/auth.json",  # pragma: allowlist secret
        signup_auth0=signup_auth0_config,
        signup_tenant="vanguard1",
        individual_tenant="discovery1",
        staging_url="https://staging-redirect.augmentcode.com",
        staging_user_email_regex="^[a-zA-Z0-9\\._%+-]+@redirecttest\\.com$",
        customer_ui_url="https://app.test.augmentcode.com",
        allowed_discovery_user_email_regex=allowed_discovery_user_email_regex,
    )


class AppUnderTest:
    def __init__(
        self,
        app: flask.Flask,
        fake_oidc_server: auth_central_test_setup.FakeOidcServer,
        front_end_token_service: front_end_token_service_pb2_grpc.FrontEndTokenServiceStub,
        shared_fixtures: SharedFixtures,
    ):
        self.app = app
        self.fake_oidc_server = fake_oidc_server
        self.front_end_token_service = front_end_token_service
        self.shared_fixtures = shared_fixtures
        self.client = app.test_client()

    def accept_get_call(
        self,
        idp_user_id: str,
        email_address: str,
        query_params: dict[str, str] | None = None,
    ):
        query_params = query_params or working_query_params()

        with self.client.session_transaction() as session:
            session["user_email"] = email_address
            session["idp_user_id"] = idp_user_id

        return self.client.get(
            "/terms-accept",
            query_string=query_params,
        )

    def accept_post_call(
        self,
        email_address: str,
        data: dict[str, str],
        query_params: dict[str, str],
        idp_user_id: str | None = None,
    ):
        with self.client.session_transaction() as session:
            session["user_email"] = email_address
            session["idp_user_id"] = idp_user_id or "idp|" + email_address

        return self.client.post(
            "/terms-accept",
            data=data,
            query_string=query_params,
        )

    def create_user(self, email_address: str, tenant_id: str) -> auth_entities_pb2.User:
        return self.front_end_token_service.EnsureUserInTenant(
            front_end_token_service_pb2.EnsureUserInTenantRequest(
                email_address=email_address,
                tenant_id=tenant_id,
                mode=front_end_token_service_pb2.TenantEnsureMode.TENANT_ENSURE_MODE_ADD_IF_EMPTY,
            )
        ).user

    @staticmethod
    def create(shared_fixtures: SharedFixtures):
        """Create the test app with both HTTP and gRPC servers running."""
        app = create_app(
            config=create_config(
                tenant_watcher_port=shared_fixtures.tenant_watcher_server_port,
                backend_port=shared_fixtures.auth_central_grpc_server.private_port,
                auth0_credentials_path=shared_fixtures.auth0_credentials_path,
                oidc_well_known_url=shared_fixtures.oidc_server.well_known_config_url(),
            ),
            secret_key=SecretStr("test_secret_key"),  # pragma: allowlist secret
            prometheus=False,
        )
        app.config["PREFERRED_URL_SCHEME"] = "https"

        front_end_token_service = (
            front_end_token_service_pb2_grpc.FrontEndTokenServiceStub(
                grpc.insecure_channel(
                    f"localhost:{shared_fixtures.auth_central_grpc_server.private_port}"
                )
            )
        )

        return AppUnderTest(
            app=app,
            fake_oidc_server=shared_fixtures.oidc_server,
            front_end_token_service=front_end_token_service,
            shared_fixtures=shared_fixtures,
        )


# I'm undecided as to the value of these fixtures. They save one line
# of code in tests. They are here for now to keep the initial code
# review that created AppUnderTest manageable.
#
# AppUnderTest will continue to be supported and extended. These may
# wither over time.
@pytest.fixture()
def app(shared_fixtures):
    yield AppUnderTest.create(shared_fixtures).app


@pytest.fixture()
def client_kit(shared_fixtures):
    yield AppUnderTest.create(shared_fixtures)


@pytest.fixture()
def client(shared_fixtures):
    yield AppUnderTest.create(shared_fixtures).client


@pytest.fixture()
def app_with_login_invitations_enabled(shared_fixtures, feature_flags):
    """Create an app with the login invitations feature flag enabled."""
    feature_flags.set_flag(auth_app._LOGIN_INVITATIONS_ENABLED, True)
    yield AppUnderTest.create(shared_fixtures).app


def working_query_params() -> dict[str, str]:
    return {
        "redirect_uri": "vscode://augment.vscode-augment/auth/result",
        "client_id": "augment-vscode-extension",
        "response_type": "code",
        "state": "fake-state",
        # Sha256 of "fake-code-verifier"
        "code_challenge": "x5da_kVcK0DEIPa4IVED1lKBbMzfXwf8OqRGAW4QA9U",
        "code_challenge_method": "S256",
    }


def test_health(client):
    response = client.get("/health")
    assert response.status_code == 200


def location_from_login_response(response):
    if response.status_code == 200:
        soup = BeautifulSoup(response.data, "html.parser")
        return soup.find("a", id="redirect_url").attrs["href"]
    elif response.status_code == 302:
        return response.headers["Location"]
    else:
        assert False, f"Unexpected status code: {response.status_code}"


def test_login_redirect(client_kit):
    client = client_kit.client

    response = client.get(
        "/login",
        query_string=working_query_params(),
    )

    location = location_from_login_response(response)

    assert location.startswith(client_kit.fake_oidc_server.url() + "/authorize")
    assert "redirect_uri=https%3A%2F%2Flocalhost%2Foauth2%2Fcallback" in location
    assert "state=" in location
    assert "response_type=code" in location
    assert "client_id=test-client-id" in location
    assert "scope=openid+email+profile" in location


def test_accept_no_tenant(client_kit):
    response = client_kit.accept_get_call(
        idp_user_id="123",
        email_address="<EMAIL>",
        query_params=working_query_params(),
    )

    assert response.status_code == 401
    assert "Different Account" in str(response.data)
    assert "<EMAIL>" in str(response.data)


def test_accept_passes_query_params(client_kit):
    response = client_kit.accept_get_call(
        idp_user_id="123",
        email_address="<EMAIL>",
        query_params=working_query_params(),
    )
    assert "accept?redirect_uri=vscode" in str(response.data)
    assert response.status_code == 200


def test_accept_passes_enterprise_tos(client_kit):
    response = client_kit.accept_get_call(
        idp_user_id="123",
        email_address="<EMAIL>",
        query_params=working_query_params(),
    )
    assert response.status_code == 200
    assert "www.augmentcode.com/terms-of-service/enterprise" in str(response.text)


def test_accept_passes_community_tos(client_kit):
    response = client_kit.accept_get_call(
        idp_user_id="456",
        email_address="<EMAIL>",
        query_params=working_query_params(),
    )
    assert response.status_code == 200
    assert "www.augmentcode.com/terms-of-service/community" in str(response.text)


def test_accept_domain_case_insensitive(client_kit):
    """Test that tenant domain matching is case-insensitive."""
    # TEST_TENANTS[0] has domain "test.com"
    response = client_kit.accept_get_call(
        idp_user_id="case_insensitive_user",
        email_address="<EMAIL>",  # Mixed case domain
        query_params=working_query_params(),
    )
    assert response.status_code == 200
    # Check for content that indicates the correct tenant ("test") was matched.
    # For example, if the enterprise TOS is shown for this tenant.
    assert "www.augmentcode.com/terms-of-service/enterprise" in str(response.text)


def test_accept_creates_user(feature_flags, client_kit):
    unique_user = "<EMAIL>"
    unique_id = f"id_{unique_user}"

    response = client_kit.accept_get_call(
        idp_user_id=unique_id,
        email_address=unique_user,
        query_params=working_query_params(),
    )

    assert response.status_code == 200

    user = client_kit.front_end_token_service.GetUser(
        front_end_token_service_pb2.GetUserRequest(
            idp_user_id=unique_id,
            email_address=unique_user,
        )
    ).user
    assert user.tenants[0] == "bar567"
    assert len(user.idp_user_ids) == 1, "Expected exactly one IDP user ID"
    assert user.idp_user_ids[0] == unique_id, f"Expected IDP user ID {unique_id}"


def test_staging_redirect(client_kit):
    response = client_kit.accept_get_call(
        idp_user_id="123",
        email_address="<EMAIL>",
        query_params=working_query_params(),
    )

    # This should return a page that will redirect the user to the staging url
    # after a few seconds
    assert response.status_code == 200
    assert (
        '<a id="redirect_url" href="https://staging-redirect.augmentcode.com/login?redirect_uri=vscode'
        in response.text
    )


def test_staging_redirect_for_customer_ui_fails(client_kit):
    response = client_kit.accept_get_call(
        idp_user_id="123",
        email_address="<EMAIL>",
        query_params=working_query_params()
        | {
            "redirect_uri": "https://auth.augmentcode.com/auth/result",
            "client_id": "customer-ui",
        },
    )
    assert response.status_code == 401


def test_accept_no_terms(client_kit):
    response = client_kit.accept_post_call(
        idp_user_id="123",
        email_address="<EMAIL>",
        data={"continue": "continue"},
        query_params=working_query_params(),
    )
    assert "You must accept the terms of service to use Augment." in str(response.data)
    assert response.status_code == 200


def test_accept_malformed(client_kit):
    response = client_kit.accept_post_call(
        idp_user_id="123",
        email_address="<EMAIL>",
        data={"continue": "not-continue", "terms-of-service": "accepted"},
        query_params=working_query_params(),
    )
    assert "We encountered an unexpected error, please try again." in str(response.data)
    assert response.status_code == 200


def test_accept_toc_required_once(request, client_kit):
    email_address = f"test_once{request.node.name}@test.com"

    # First time user visits the page, the user should see the terms of service checkbox
    response = client_kit.accept_get_call(
        idp_user_id="123",
        email_address=email_address,
        query_params=working_query_params(),
    )

    assert 'id="terms-of-service-checkbox"' in str(response.data)
    assert response.status_code == 200

    # accept the terms of service
    response = client_kit.accept_post_call(
        idp_user_id="123",
        email_address=email_address,
        data={"continue": "continue", "terms-of-service": "accepted"},
        query_params=working_query_params(),
    )
    assert response.status_code == 200
    assert 'id="terms-of-service-checkbox"' not in str(response.data)

    # Second time user visits the page, the user should not see the terms of service checkbox since they have already accepted the terms of service.
    response = client_kit.accept_get_call(
        idp_user_id="123",
        email_address=email_address,
        query_params=working_query_params(),
    )

    assert response.status_code == 200
    assert 'id="terms-of-service-checkbox"' not in str(response.data)


def test_accept_successful(client_kit):
    response = client_kit.accept_post_call(
        idp_user_id="123",
        email_address="<EMAIL>",
        data={"continue": "continue", "terms-of-service": "accepted"},
        query_params=working_query_params(),
    )
    assert response.status_code == 200
    assert (
        '<h2 class="c-tagline">Switching back to<br />Visual Studio Code</h2>'
        in str(response.data)
    )


def test_authorize_missing_redirect(client_kit):
    query_params = working_query_params()
    del query_params["redirect_uri"]

    response = client_kit.accept_post_call(
        idp_user_id="123",
        email_address="<EMAIL>",
        data={"continue": "continue", "terms-of-service": "accepted"},
        query_params=query_params,
    )
    assert response.status_code == 200
    assert "We were unable to sign you in.<br />" in str(response.data)
    assert "Error (invalid_request): No redirect defined" in str(response.data)


def test_authorize_missing_argument(client_kit):
    query_params = working_query_params()
    del query_params["state"]

    response = client_kit.accept_post_call(
        idp_user_id="123",
        email_address="<EMAIL>",
        data={"continue": "continue", "terms-of-service": "accepted"},
        query_params=query_params,
    )
    assert response.status_code == 200
    assert "We were unable to sign you in.<br />" in str(response.data)
    assert "Error (invalid_request): Missing argument" in str(response.data)


def test_authorize_with_idp_successful(client_kit):
    response = client_kit.accept_get_call(
        idp_user_id="idp|123",
        email_address="<EMAIL>",
        query_params=working_query_params()
        | {
            "redirect_uri": "http://bad/uri",
        },
    )
    assert response.status_code == 200


def test_authorize_unsupported_editor(client_kit):
    response = client_kit.accept_post_call(
        idp_user_id="123",
        email_address="<EMAIL>",
        data={"continue": "continue", "terms-of-service": "accepted"},
        query_params=working_query_params()
        | {
            "redirect_uri": "veeesskowd://augment.vscode-augment/auth/result",
        },
    )
    assert response.status_code == 200
    assert "We were unable to sign you in.<br />" in str(response.data)
    assert "Error (invalid_request): Unsupported editor veeesskowd" in str(
        response.data
    )


def test_authorize_with_idp_unauthorized(client_kit):
    response = client_kit.accept_post_call(
        idp_user_id="2af378",
        email_address="<EMAIL>",
        data={"continue": "continue", "terms-of-service": "accepted"},
        query_params=working_query_params(),
    )
    assert response.status_code == 401
    assert (
        "Your organization does not allow login through this method. Please try another method."
        in str(response.data)
    )


def test_authorize_client_mismatch(client_kit):
    response = client_kit.accept_post_call(
        idp_user_id="123",
        email_address="<EMAIL>",
        data={"continue": "continue", "terms-of-service": "accepted"},
        query_params={
            "redirect_uri": "http://bad/uri",
            "client_id": "augment-vscode-extension",
            "response_type": "code",
            "state": "fake-state",
            "code_challenge": "eqEvNh2v4-XpxstYZNyL0t2YwSjxTdRQFObvgMTestU",
            "code_challenge_method": "S256",
        },
    )
    assert response.status_code == 200
    assert "We were unable to sign you in.<br />" in str(response.data)
    assert "Error (invalid_request): redirect uri is incorrect" in str(response.data)


def test_authorize_redirect_uri_prioritized_first(client_kit):
    """Tests a security concern (au-3704) an invalid redirect_uri must not appear in the response.
    In this edge case - we remove a required argument. (state)
    The expectation is that even though there's a required argument missing, we will fail the request
    on the redirect_uri as it is prioritized higher than the missing required argument.
    """

    bogus_redirect_uri = "vscode://augment.vscode-augment/auth/result/bogus"
    response = client_kit.accept_post_call(
        idp_user_id="123",
        email_address="<EMAIL>",
        data={"continue": "continue", "terms-of-service": "accepted"},
        query_params=working_query_params() | {"redirect_uri": bogus_redirect_uri},
    )
    assert response.status_code == 200
    assert "redirect uri is incorrect" in str(response.data)


def test_token_flow_success(client_kit):
    response = client_kit.accept_post_call(
        idp_user_id="123",
        email_address="<EMAIL>",
        data={"continue": "continue", "terms-of-service": "accepted"},
        query_params=working_query_params(),
    )
    assert response.status_code == 200
    href = re.search(r'href="([^"]*)"\>clicking here', str(response.data))
    assert href
    assert "vscode://augment.vscode-augment/auth/result" in href.group(1)
    parsed = urlparse(href.group(1))
    query_params = parse_qs(parsed.query)
    assert "code" in query_params
    assert query_params["code"][0].startswith("_")

    response = client_kit.client.post(
        "/token",
        json={
            "code": query_params["code"][0],
            "client_id": "augment-vscode-extension",
            "redirect_uri": "vscode://augment.vscode-augment/auth/result",
            "grant_type": "authorization_code",
            "code_verifier": "fake-code-verifier",
        },
    )
    assert response.status_code == 200
    assert "access_token" in response.json


def test_token_flow_no_code_challenge_method(client_kit):
    """Test the token flow when no code challenge method is provided.
    The flow should be successful, as we'll default to using S256.
    """

    query_params = working_query_params()
    del query_params["code_challenge_method"]

    response = client_kit.accept_post_call(
        idp_user_id="123",
        email_address="<EMAIL>",
        data={"continue": "continue", "terms-of-service": "accepted"},
        query_params=query_params,
    )
    assert response.status_code == 200
    href = re.search(r'href="([^"]*)"\>clicking here', str(response.data))
    assert href
    assert "vscode://augment.vscode-augment/auth/result" in href.group(1)
    parsed = urlparse(href.group(1))
    query_params = parse_qs(parsed.query)
    assert "code" in query_params
    assert query_params["code"][0].startswith("_")

    response = client_kit.client.post(
        "/token",
        json={
            "code": query_params["code"][0],
            "client_id": "augment-vscode-extension",
            "redirect_uri": "vscode://augment.vscode-augment/auth/result",
            "grant_type": "authorization_code",
            "code_verifier": "fake-code-verifier",
        },
    )
    assert response.status_code == 200
    assert "access_token" in response.json


@pytest.mark.parametrize("client_id", ["augment-vim-extension", "v"])
@pytest.mark.parametrize("include_redirect", [True, False])
def test_token_flow_success_vim_client(client_kit, client_id, include_redirect):
    query_string = working_query_params() | {
        "redirect_uri": "",
        "client_id": client_id,
    }
    if not include_redirect:
        del query_string["redirect_uri"]

    response = client_kit.accept_post_call(
        idp_user_id="123",
        email_address="<EMAIL>",
        data={"continue": "continue", "terms-of-service": "accepted"},
        query_params=query_string,
    )
    assert response.status_code == 200

    # The html is expected to contain strings for code, state, and tenant_url.
    # Extracting the fields in this way bypasses the json encoding of the entire
    # string, unfortunately.
    code_ref = re.search(r"code: \"(.+)\"", str(response.text))
    state_ref = re.search(r"state: \"(.+)\"", str(response.text))
    tenant_ref = re.search(r"tenant_url: \"(.+)\"", str(response.text))
    assert code_ref
    assert state_ref
    assert tenant_ref

    code = code_ref.group(1)
    state = state_ref.group(1)
    tenant_url = tenant_ref.group(1)
    assert code.startswith("_")
    assert state == query_string["state"]
    assert tenant_url

    response = client_kit.client.post(
        "/token",
        json={
            "code": code,
            "client_id": client_id,
            "redirect_uri": "",
            "grant_type": "authorization_code",
            "code_verifier": "fake-code-verifier",
        },
    )
    assert response.status_code == 200
    assert "access_token" in response.json


def test_token_flow_bad_token(client_kit):
    response = client_kit.accept_post_call(
        idp_user_id="123",
        email_address="<EMAIL>",
        data={"continue": "continue", "terms-of-service": "accepted"},
        query_params=working_query_params(),
    )
    assert response.status_code == 200
    href = re.search(r'href="([^"]*)"\>clicking here', str(response.data))
    assert href
    assert "vscode://augment.vscode-augment/auth/result" in href.group(1)
    parsed = urlparse(href.group(1))
    query_params = parse_qs(parsed.query)
    assert "code" in query_params

    response = client_kit.client.post(
        "/token",
        json={
            "code": "not-a-real-code",
            "client_id": "augment-vscode-extension",
            "redirect_uri": "vscode://augment.vscode-augment/auth/result",
            "grant_type": "authorization_code",
            "code_verifier": "fake-code-verifier",
        },
    )
    assert response.status_code == 400


def test_token_flow_bad_verifier(client_kit):
    response = client_kit.accept_post_call(
        idp_user_id="123",
        email_address="<EMAIL>",
        data={"continue": "continue", "terms-of-service": "accepted"},
        query_params=working_query_params(),
    )
    assert response.status_code == 200
    href = re.search(r'href="([^"]*)"\>clicking here', str(response.data))
    assert href
    assert "vscode://augment.vscode-augment/auth/result" in href.group(1)
    parsed = urlparse(href.group(1))
    query_params = parse_qs(parsed.query)
    assert "code" in query_params

    response = client_kit.client.post(
        "/token",
        json={
            "code": query_params["code"][0],
            "client_id": "augment-vscode-extension",
            "redirect_uri": "vscode://augment.vscode-augment/auth/result",
            "grant_type": "authorization_code",
            "code_verifier": "another-fake-code-verifier",
        },
    )
    assert response.status_code == 400


def test_token_flow_missing_arg(client_kit):
    response = client_kit.accept_post_call(
        idp_user_id="123",
        email_address="<EMAIL>",
        data={"continue": "continue", "terms-of-service": "accepted"},
        query_params=working_query_params(),
    )
    assert response.status_code == 200
    href = re.search(r'href="([^"]*)"\>clicking here', str(response.data))
    assert href
    assert "vscode://augment.vscode-augment/auth/result" in href.group(1)
    parsed = urlparse(href.group(1))
    query_params = parse_qs(parsed.query)
    assert "code" in query_params

    response = client_kit.client.post(
        "/token",
        json={
            "code": query_params["code"][0],
            "redirect_uri": "vscode://augment.vscode-augment/auth/result",
            "grant_type": "authorization_code",
            "code_verifier": "fake-code-verifier",
        },
    )
    assert response.status_code == 400


def test_token_redirect_uri_prioritized_first(client_kit):
    response = client_kit.accept_post_call(
        idp_user_id="123",
        email_address="<EMAIL>",
        data={"continue": "continue", "terms-of-service": "accepted"},
        query_params=working_query_params(),
    )
    assert response.status_code == 200
    href = re.search(r'href="([^"]*)"\>clicking here', str(response.data))
    assert href
    assert "vscode://augment.vscode-augment/auth/result" in href.group(1)
    parsed = urlparse(href.group(1))
    query_params = parse_qs(parsed.query)
    assert "code" in query_params

    bogus_redirect_uri = "vscode://augment.vscode-augment/auth/result/bogus"
    response = client_kit.client.post(
        "/token",
        json={
            "code": query_params["code"][0],
            "redirect_uri": bogus_redirect_uri,
            "code_verifier": "fake-code-verifier",
        },
    )
    assert response.data == b'{"error":"invalid_request"}\n'
    assert response.status_code == 400


def test_token_flow_reject_duplicate(client_kit):
    response = client_kit.accept_post_call(
        idp_user_id="123",
        email_address="<EMAIL>",
        data={"continue": "continue", "terms-of-service": "accepted"},
        query_params=working_query_params(),
    )
    assert response.status_code == 200
    href = re.search(r'href="([^"]*)"\>clicking here', str(response.data))
    assert href
    assert "vscode://augment.vscode-augment/auth/result" in href.group(1)
    parsed = urlparse(href.group(1))
    query_params = parse_qs(parsed.query)
    assert "code" in query_params

    response = client_kit.client.post(
        "/token",
        json={
            "code": query_params["code"][0],
            "client_id": "augment-vscode-extension",
            "redirect_uri": "vscode://augment.vscode-augment/auth/result",
            "grant_type": "authorization_code",
            "code_verifier": "fake-code-verifier",
        },
    )
    assert response.status_code == 200
    assert "access_token" in response.json

    response = client_kit.client.post(
        "/token",
        json={
            "code": query_params["code"][0],
            "client_id": "augment-vscode-extension",
            "redirect_uri": "vscode://augment.vscode-augment/auth/result",
            "grant_type": "authorization_code",
            "code_verifier": "fake-code-verifier",
        },
    )
    assert response.status_code == 400


def test_token_flow_customer_ui(client_kit):
    customer_ui_redirect_uri = "https://auth.augmentcode.com/auth/result"
    response = client_kit.accept_post_call(
        idp_user_id="123",
        email_address="<EMAIL>",
        data={"continue": "continue", "terms-of-service": "accepted"},
        query_params=working_query_params()
        | {
            "redirect_uri": customer_ui_redirect_uri,
            "client_id": "customer-ui",
        },
    )
    assert response.status_code == 302
    url = response.headers["Location"]
    assert customer_ui_redirect_uri in url
    parsed = urlparse(url)
    query_params = parse_qs(parsed.query)
    assert "code" in query_params

    json_body = {
        "code": query_params["code"][0],
        "client_id": "customer-ui",
        "redirect_uri": customer_ui_redirect_uri,
        "grant_type": "authorization_code",
        "code_verifier": "fake-code-verifier",
    }
    response = client_kit.client.post("/token", json=json_body)
    assert response.status_code == 200
    assert "access_token" in response.json

    response = client_kit.client.post("/token", json=json_body)
    assert response.status_code == 400


def test_auth_cookie_flow(client_kit):
    """
    Ensure that:
        - post to /terms-accept will create a session cookie
        - get to /logout will clear the session cookie
        - get to /authorize with a session cookie will redirect to customer UI
        - get to /authorize without a session cookie will redirect to /login
    """
    client = client_kit.client

    with client.session_transaction() as session:
        session["user_email"] = "<EMAIL>"
        session["idp_user_id"] = "123"

    # Posting to accept will create a session cookie if we don't already have one
    customer_ui_redirect_uri = "https://auth.augmentcode.com/auth/result"
    response = client.post(
        "/terms-accept",
        data={"continue": "continue", "terms-of-service": "accepted"},
        query_string=working_query_params()
        | {
            "redirect_uri": customer_ui_redirect_uri,
            "client_id": "customer-ui",
        },
    )
    assert response.status_code == 302
    redirected_url = response.headers["Location"]
    assert redirected_url.startswith(customer_ui_redirect_uri)

    # Now getting authorize should redirect to accept, since we have a cookie
    response = client.get(
        "/authorize",
        query_string=working_query_params()
        | {
            "redirect_uri": customer_ui_redirect_uri,
            "client_id": "customer-ui",
        },
    )
    assert response.status_code == 302
    redirected_url = response.headers["Location"]
    assert redirected_url.startswith(customer_ui_redirect_uri)

    # Clear the cookie by logging out
    response = client.get(
        "/logout",
        query_string=working_query_params()
        | {
            "redirect_uri": customer_ui_redirect_uri,
            "client_id": "customer-ui",
        },
    )
    assert response.status_code == 302
    redirected_url = response.headers["Location"]
    assert redirected_url.startswith(customer_ui_redirect_uri)

    # Cookie should be clear, so getting authorize should redirect to login
    response = client.get(
        "/authorize",
        query_string=working_query_params()
        | {
            "redirect_uri": customer_ui_redirect_uri,
            "client_id": "customer-ui",
        },
    )
    assert response.status_code == 302
    redirected_url = response.headers["Location"]
    assert redirected_url.startswith("/login")


def test_auth_cookie_mismatch(client_kit):
    """
    Ensure that:
        - an invalid cookie will be properly cleared by authorize
    """

    # Common metadata for requests
    customer_ui_redirect_uri = "https://auth.augmentcode.com/auth/result"
    query_string = working_query_params() | {
        "redirect_uri": customer_ui_redirect_uri,
        "client_id": "customer-ui",
    }

    # Posting to accept will create a valid auth cookie. We'll then modify the
    # nonce to invalidate the cookie for the subsequent call to authorize.
    curr_nonce = 0
    with client_kit.client:
        response = client_kit.accept_post_call(
            idp_user_id="123",
            email_address="<EMAIL>",
            data={"continue": "continue", "terms-of-service": "accepted"},
            query_params=query_string,
        )
        assert response.status_code == 302
        assert response.headers["Location"].startswith(customer_ui_redirect_uri)
        curr_nonce = session["nonce"]

    # Invalidate the cookie. We're leveraging an internal detail of the session,
    # but it's easy to test.
    with client_kit.client.session_transaction() as sess:
        sess["nonce"] = curr_nonce + 1

    # The cookie should be invalid now. Authorize should clear the cookie and
    # redirect to /login for authentication.
    response = client_kit.client.get(
        "/authorize",
        query_string=query_string,
    )
    assert response.status_code == 302
    redirected_url = response.headers["Location"]
    assert redirected_url.startswith("/login")

    # Since we no longer have a valid cookie, posting to accept will give us a
    # new, valid cookie, which we can validate against /authorize.
    response = client_kit.accept_post_call(
        idp_user_id="123",
        email_address="<EMAIL>",
        data={"continue": "continue", "terms-of-service": "accepted"},
        query_params=query_string,
    )
    assert response.status_code == 302
    assert response.headers["Location"].startswith(customer_ui_redirect_uri)

    # Cookie should be valid now
    response = client_kit.client.get(
        "/authorize",
        query_string=query_string,
    )
    assert response.status_code == 302
    redirected_url = response.headers["Location"]
    assert redirected_url.startswith(customer_ui_redirect_uri)


def test_cookie_security_settings(client_kit):
    """Test that cookies have proper security settings.

    The approach will make a request to set the session cookie, and then parse the
    attributes of the cookie.
    """

    # Common metadata for requests
    customer_ui_redirect_uri = "https://auth.augmentcode.com/auth/result"
    query_params = working_query_params() | {
        "redirect_uri": customer_ui_redirect_uri,
        "client_id": "customer-ui",
    }

    # Get the auth cookie through the /accept request
    with client_kit.client:
        response = client_kit.accept_post_call(
            idp_user_id="123",
            email_address="<EMAIL>",
            data={"continue": "continue", "terms-of-service": "accepted"},
            query_params=query_params,
        )
        assert response.status_code == 302
        assert response.headers["Location"].startswith(customer_ui_redirect_uri)

    # This is low level, but is a better end-to-end test than directly checking
    # flask configuration parameters.

    # Get the session cookie from response. We know the name of the cookie.
    cookies = response.headers.getlist("Set-Cookie")
    cookies = [http.cookies.SimpleCookie(cookie) for cookie in cookies]
    session_cookies = [x for x in cookies if "session" in x]
    assert len(session_cookies) == 1, "Expected exactly one session cookie"
    session_cookie = session_cookies[0]["session"]

    # Check the security attributes of the cookie
    assert session_cookie["secure"] is True
    assert session_cookie["httponly"] is True
    assert session_cookie["samesite"] == "Lax"


def test_signup_login_sets_ext_plan_to_community(app):
    """Test that the ext-plan query parameter is set to community for the
    community tenant.
    """
    with app.test_client() as client:
        response = client.get("/signup/login?us=true")
        location = location_from_login_response(response)
        parsed_url = urlparse(location)
        query = parse_qs(parsed_url.query)
        assert query["ext-plan"] == ["community"], f"{query}"


def test_signup_callback(request, app):
    """Test successful sign-up flow."""
    with app.test_client() as client:
        app.config.update(
            augment_override_authorize_access_token={
                "userinfo": {
                    "email_verified": True,
                    "email": f"signup_{request.node.name}@gmail.com",
                }
            }
        )

        _ = client.get("/signup/login?us=true")

        response = client.get("/signup/callback")

        assert response.status_code == 302
        parsed_url = urlparse(response.headers["Location"])
        query = parse_qs(parsed_url.query)
        assert query["status"] == ["success"]


def test_signup_callback_not_verified(app):
    """Test status on unverified e-mail address."""
    with app.test_client() as client:
        app.config.update(
            augment_override_authorize_access_token={
                "userinfo": {"email_verified": False, "email": "<EMAIL>"}
            }
        )

        _ = client.get("/signup/login?us=true")

        response = client.get("/signup/callback")

        assert response.status_code == 302
        parsed_url = urlparse(response.headers["Location"])
        query = parse_qs(parsed_url.query)
        assert query["status"] == ["unverified-email-address"]


def test_signup_callback_no_email_address(app):
    """Test status on no e-mail address."""
    with app.test_client() as client:
        app.config.update(augment_override_authorize_access_token={"userinfo": {}})

        _ = client.get("/signup/login?us=true")

        response = client.get("/signup/callback")

        assert response.status_code == 302
        parsed_url = urlparse(response.headers["Location"])
        query = parse_qs(parsed_url.query)
        assert query["status"] == ["no-email-address"]


@pytest.mark.parametrize("email", ["1234567890", "test@gmail", "test@<EMAIL>"])
def test_signup_callback_invalid_email(app, email):
    """Test status on invalid email."""
    with app.test_client() as client:
        app.config.update(
            augment_override_authorize_access_token={
                "userinfo": {"email_verified": True, "email": email}
            }
        )

        _ = client.get("/signup/login?us=true")

        response = client.get("/signup/callback")

        assert response.status_code == 302
        parsed_url = urlparse(response.headers["Location"])
        query = parse_qs(parsed_url.query)
        assert query["status"] == ["invalid-email"]


@pytest.mark.parametrize(
    "email,should_be_allowed,description",
    [
        # Allowed emails - should NOT get "invalid-email" status
        ("<EMAIL>", True, "basic valid @augm.io email"),
        ("<EMAIL>", True, "@augm.io email with numbers"),
        # Blocked emails - should get "invalid-email" status
        ("<EMAIL>", False, "different domain"),
        (
            "<EMAIL>",
            False,
            "enterprise domain blocked for individual signup",
        ),
        ("<EMAIL>", False, "similar but wrong domain"),
        ("<EMAIL>", False, "subdomain attempt"),
        ("<EMAIL>", False, "subdomain of augm.io"),
    ],
)
def test_staging_individual_signup_domain_restrictions(
    shared_fixtures, email, should_be_allowed, description
):
    """Test domain restrictions for individual signup in staging environment."""
    # Create app with staging domain restrictions
    app = create_app(
        config=create_config(
            tenant_watcher_port=shared_fixtures.tenant_watcher_server_port,
            backend_port=shared_fixtures.auth_central_grpc_server.private_port,
            auth0_credentials_path=shared_fixtures.auth0_credentials_path,
            oidc_well_known_url=shared_fixtures.oidc_server.well_known_config_url(),
            allowed_discovery_user_email_regex="^[a-zA-Z0-9\\._%+-]+@augm\\.io$",
        ),
        secret_key=SecretStr("test_secret_key"),  # pragma: allowlist secret
        prometheus=False,
    )
    app.config["PREFERRED_URL_SCHEME"] = "https"

    with app.test_client() as client:
        app.config.update(
            augment_override_authorize_access_token={
                "userinfo": {
                    "email_verified": True,
                    "email": email,
                    "sub": f"auth0|{email}",
                }
            }
        )

        # Test individual signup (us=true means individual/vanguard tenant)
        _ = client.get("/signup/login?us=true")
        response = client.get("/signup/callback")

        assert response.status_code == 302
        parsed_url = urlparse(response.headers["Location"])
        query = parse_qs(parsed_url.query)

        if should_be_allowed:
            # Email should be allowed for individual signup in staging
            assert (
                query["status"] != ["invalid-email"]
            ), f"Email {email} should be allowed for individual signup in staging ({description})"
        else:
            # Email should be blocked for individual signup in staging
            assert (
                query["status"] == ["invalid-email"]
            ), f"Email {email} should be blocked for individual signup in staging ({description})"


def test_signup_callback_limit_reached(client_kit, feature_flags):
    """Test status on signup limit reached."""
    app = client_kit.app

    with app.test_client() as client:
        app.config.update(
            augment_override_authorize_access_token={
                "userinfo": {"email_verified": True, "email": "<EMAIL>"}
            }
        )

        _ = client.get("/signup/login?us=true")

        client_kit.front_end_token_service.TestOnlySetSignupParameters(
            front_end_token_service_pb2.TestOnlySetSignupParametersRequest(
                max_burst=0, signup_per_day=1000
            )
        )

        try:
            response = client.get("/signup/callback")

            assert response.status_code == 302
            parsed_url = urlparse(response.headers["Location"])
            query = parse_qs(parsed_url.query)
            assert query["status"] == ["signup-limit-reached"]
        finally:
            client_kit.front_end_token_service.TestOnlySetSignupParameters(
                front_end_token_service_pb2.TestOnlySetSignupParametersRequest(
                    max_burst=500, signup_per_day=1000
                )
            )


def test_signup_callback_already_signed_up(request, app):
    """Test status on already signed up."""
    with app.test_client() as client:
        app.config.update(
            augment_override_authorize_access_token={
                "userinfo": {
                    "email_verified": True,
                    "email": f"test_dup_{request.node.name}@gmail.com",
                }
            }
        )

        _ = client.get("/signup/login?us=true")

        response = client.get("/signup/callback")

        assert response.status_code == 302
        parsed_url = urlparse(response.headers["Location"])
        query = parse_qs(parsed_url.query)
        assert query["status"] == ["success"]

        _ = client.get("/signup/login?us=true")

        response = client.get("/signup/callback")

        assert response.status_code == 302
        parsed_url = urlparse(response.headers["Location"])
        query = parse_qs(parsed_url.query)
        assert query["status"] == ["already-signed-up"]


def test_signup_callback_similar_user(request, app):
    """Test status on already signed up."""
    with app.test_client() as client:
        app.config.update(
            augment_override_authorize_access_token={
                "userinfo": {
                    "email_verified": True,
                    "email": f"test_similar_{request.node.name}@gmail.com",
                }
            }
        )

        _ = client.get("/signup/login?us=true")

        response = client.get("/signup/callback")

        assert response.status_code == 302
        parsed_url = urlparse(response.headers["Location"])
        query = parse_qs(parsed_url.query)
        assert query["status"] == ["success"]

        app.config.update(
            augment_override_authorize_access_token={
                "userinfo": {
                    "email_verified": True,
                    "email": f"test_similar_{request.node.name}+<EMAIL>",
                }
            }
        )

        _ = client.get("/signup/login?us=true")

        response = client.get("/signup/callback")

        assert response.status_code == 302
        parsed_url = urlparse(response.headers["Location"])
        query = parse_qs(parsed_url.query)
        assert query["status"] == ["already-signed-up"]


def test_signup_callback_user_in_other_tenant(client_kit):
    """Test status on user already in other tenant."""
    app = client_kit.app

    with app.test_client() as client:
        client_kit.front_end_token_service.EnsureUserInTenant(
            front_end_token_service_pb2.EnsureUserInTenantRequest(
                email_address="<EMAIL>",
                tenant_id="foo567",
                mode=front_end_token_service_pb2.TenantEnsureMode.TENANT_ENSURE_MODE_ADD_IF_EMPTY,
            )
        )

        app.config.update(
            augment_override_authorize_access_token={
                "userinfo": {"email_verified": True, "email": "<EMAIL>"}
            }
        )

        _ = client.get("/signup/login?us=true")

        response = client.get("/signup/callback")

        assert response.status_code == 302
        parsed_url = urlparse(response.headers["Location"])
        query = parse_qs(parsed_url.query)
        assert query["status"] == ["in-another-tenant"]


def test_signup_callback_user_in_corporate_tenant(shared_fixtures):
    """Test status on user eligible for a corporate tenant."""
    app_under_test = AppUnderTest.create(shared_fixtures)
    app = app_under_test.app

    with app.test_client() as client:
        response = client.get("/signup/login?us=true")
        location = location_from_login_response(response)
        parsed_url = urlparse(location)
        query = parse_qs(parsed_url.query)

        app_under_test.fake_oidc_server.set_userinfo(
            {
                "sub": "sub_other_tenant",
                "email_verified": True,
                "email": "<EMAIL>",
                "nonce": query["nonce"][0],
            }
        )

        response = client.get(f"/signup/callback?code=foo&state={query['state'][0]}")

        assert response.status_code == 302
        parsed_url = urlparse(response.headers["Location"])
        query = parse_qs(parsed_url.query)
        assert query["status"] == ["in-another-tenant"]


def test_signup_login_no_users_available(client_kit, feature_flags):
    """Test status on no users available."""
    app = client_kit.app

    with app.test_client() as client:
        client_kit.front_end_token_service.TestOnlySetSignupParameters(
            front_end_token_service_pb2.TestOnlySetSignupParametersRequest(
                max_burst=0, signup_per_day=1000
            )
        )

        try:
            response = client.get("/signup/login?us=true")

            assert response.status_code == 302
            parsed_url = urlparse(response.headers["Location"])
            query = parse_qs(parsed_url.query)
            assert query["status"] == ["signup-limit-reached"]
        finally:
            client_kit.front_end_token_service.TestOnlySetSignupParameters(
                front_end_token_service_pb2.TestOnlySetSignupParametersRequest(
                    max_burst=500, signup_per_day=1000
                )
            )


def test_signup_not_in_usa(app):
    """Test status on not in usa."""
    with app.test_client() as client:
        app.config.update(
            augment_override_authorize_access_token={
                "userinfo": {"email_verified": True, "email": "<EMAIL>"}
            }
        )

        response = client.get("/signup/login")

        assert response.status_code == 302
        parsed_url = urlparse(response.headers["Location"])
        query = parse_qs(parsed_url.query)
        assert query["status"] == ["not-in-usa"]

        response = client.get("/signup/login?us=false")

        assert response.status_code == 302
        parsed_url = urlparse(response.headers["Location"])
        query = parse_qs(parsed_url.query)
        assert query["status"] == ["not-in-usa"]

        response = client.get("/signup/callback")

        assert response.status_code == 302
        parsed_url = urlparse(response.headers["Location"])
        query = parse_qs(parsed_url.query)
        assert query["status"] == ["not-in-usa"]


@pytest.mark.parametrize(
    ["first_is_community", "second_is_community", "expected_status"],
    [
        (True, True, "already-signed-up"),
        (True, False, "in-another-tenant"),
        (False, True, "in-another-tenant"),
        (False, False, "already-signed-up"),
    ],
)
def test_signup_user_in_x_signs_up_for_y(
    shared_fixtures, first_is_community, second_is_community, expected_status
):
    """Test that a user in the community tenant can sign up for the community tenant."""
    app_under_test = AppUnderTest.create(shared_fixtures)
    app = app_under_test.app

    with app.test_client() as client:
        first_tenant_id = "id-vanguard2" if first_is_community else "id-discovery2"
        email_address = "<EMAIL>" % (
            first_is_community,
            second_is_community,
        )
        app_under_test.front_end_token_service.EnsureUserInTenant(
            front_end_token_service_pb2.EnsureUserInTenantRequest(
                email_address=email_address,
                tenant_id=first_tenant_id,
                mode=front_end_token_service_pb2.TenantEnsureMode.TENANT_ENSURE_MODE_ADD_IF_EMPTY,
            )
        )

        response = client.get(
            "/signup/login?us=true"
            if second_is_community
            else "/signup/login?individual=true"
        )
        location = location_from_login_response(response)
        parsed_url = urlparse(location)
        query = parse_qs(parsed_url.query)

        app_under_test.fake_oidc_server.set_userinfo(
            {
                "sub": "sub_" + email_address,
                "email_verified": True,
                "email": email_address,
                "nonce": query["nonce"][0],
            }
        )

        response = client.get(f"/signup/callback?code=foo&state={query['state'][0]}")

        assert response.status_code == 302
        parsed_url = urlparse(response.headers["Location"])
        query = parse_qs(parsed_url.query)
        assert query["status"] == [expected_status]


def test_signup_disallow_similar_signups(shared_fixtures):
    """Test that a user with a similar email cannot sign up."""
    app_under_test = AppUnderTest.create(shared_fixtures)

    _ = app_under_test.create_user(
        "<EMAIL>",
        tenant_id=shared_fixtures.test_tenant_id,
    )

    response = app_under_test.accept_post_call(
        email_address="<EMAIL>",
        data={
            "continue": "continue",
            "terms-of-service": "accepted",
            "sign_up": "true",
        },
        query_params=working_query_params(),
    )

    assert "already signed up" in str(response.data)


# Tests for invitation-related endpoints


def _test_invitations_page_with_data(app_with_login_invitations_enabled, invitations):
    """Helper function to test the invitations page with the given invitations data."""
    from services.auth.central.server import invitation_service

    # Save the original get_user_invitations function
    original_get_invitations = invitation_service.get_user_invitations

    # Mock the invitation_service.get_user_invitations function
    def mock_get_user_invitations(email):
        return invitations

    try:
        # Replace the function with our mock
        invitation_service.get_user_invitations = mock_get_user_invitations

        with app_with_login_invitations_enabled.test_client() as client:
            with client.session_transaction() as session:
                session["user_email"] = "<EMAIL>"
                session["idp_user_id"] = "123"

            response = client.get("/invitations")
            assert response.status_code == 200

            response_text = response.data.decode("utf-8")

            if not invitations:
                assert "No Team Invitations" in response_text
                assert "Continue to Augment" in response_text
            else:
                assert "No Team Invitations" not in response_text

                for invitation in invitations:
                    assert invitation["inviter_email"] in response_text
                    assert invitation["created_at"] in response_text

                assert "Accept" in response_text
    finally:
        # Restore the original function
        invitation_service.get_user_invitations = original_get_invitations


def test_invitations_page_one_invitation(app_with_login_invitations_enabled):
    """Test the /invitations endpoint with one invitation."""
    invitation = {
        "id": "invitation-id-1",
        "tenant_id": "tenant-id-1",
        "inviter_email": "<EMAIL>",
        "created_at": "2023-01-01",
    }

    _test_invitations_page_with_data(app_with_login_invitations_enabled, [invitation])


def test_invitations_page_multiple_invitations(app_with_login_invitations_enabled):
    """Test the /invitations endpoint with multiple invitations."""
    invitations = [
        {
            "id": "invitation-id-1",
            "tenant_id": "tenant-id-1",
            "inviter_email": "<EMAIL>",
            "created_at": "2023-01-01",
        },
        {
            "id": "invitation-id-2",
            "tenant_id": "tenant-id-2",
            "inviter_email": "<EMAIL>",
            "created_at": "2023-01-02",
        },
        {
            "id": "invitation-id-3",
            "tenant_id": "tenant-id-3",
            "inviter_email": "<EMAIL>",
            "created_at": "2023-01-03",
        },
    ]

    _test_invitations_page_with_data(app_with_login_invitations_enabled, invitations)


def test_invitations_page_unauthenticated(app_with_login_invitations_enabled):
    """Test the /invitations endpoint with an unauthenticated user."""

    with app_with_login_invitations_enabled.test_client() as client:
        # Make the request without setting up a session
        response = client.get("/invitations")

        # Check that the response is a redirect to the login page
        assert response.status_code == 302
        assert response.headers["Location"] == "/login?client_id=invitations"


def _test_invitation_resolve_api(
    app_with_login_invitations_enabled,
    request_data,
    expected_status=200,
    expected_response=None,
):
    """Helper function to test the invitation resolve API with the given request data."""
    with app_with_login_invitations_enabled.test_client() as client:
        with client.session_transaction() as session:
            session["user_email"] = "<EMAIL>"
            session["idp_user_id"] = "123"

        response = client.post("/api/invitation/resolve", json=request_data)
        assert response.status_code == expected_status

        if expected_response:
            for key, value in expected_response.items():
                assert response.json[key] == value

    return response


def test_api_invitation_resolve_accept(app_with_login_invitations_enabled):
    """Test the /api/invitation/resolve endpoint for accepting an invitation."""
    # Test accepting an invitation that doesn't exist
    # The endpoint returns 404 when the invitation ID is not found
    _test_invitation_resolve_api(
        app_with_login_invitations_enabled,
        {"accept_invitation_id": "test-invitation-id"},
        expected_status=404,
        expected_response={"error": "Invitation not found"},
    )


def test_api_invitation_resolve_decline(app_with_login_invitations_enabled):
    """Test the /api/invitation/resolve endpoint for declining invitations."""
    # Test declining invitations
    # The endpoint returns 404 when the invitation IDs are not found
    _test_invitation_resolve_api(
        app_with_login_invitations_enabled,
        {"decline_invitation_ids": ["test-invitation-id-1", "test-invitation-id-2"]},
        expected_status=404,
        expected_response={
            "error": "Invalid invitation IDs: ['test-invitation-id-1', 'test-invitation-id-2']"
        },
    )


def test_api_invitation_resolve_invalid_request(app_with_login_invitations_enabled):
    """Test the /api/invitation/resolve endpoint with an invalid request."""
    with app_with_login_invitations_enabled.test_client() as client:
        with client.session_transaction() as session:
            session["user_email"] = "<EMAIL>"
            session["idp_user_id"] = "123"

        # Test with empty JSON
        response = client.post("/api/invitation/resolve", json={})
        assert response.status_code == 400

        # Test with invalid JSON format
        response = client.post(
            "/api/invitation/resolve", data="not json", content_type="application/json"
        )
        assert response.status_code == 500


def test_api_invitation_resolve_unauthenticated(app_with_login_invitations_enabled):
    """Test the /api/invitation/resolve endpoint with an unauthenticated user."""
    with app_with_login_invitations_enabled.test_client() as client:
        # Make a request without setting up a session
        response = client.post(
            "/api/invitation/resolve",
            json={"accept_invitation_id": "test-invitation-id"},
        )

        # Check that the response is a 401 status code with the expected error message
        assert response.status_code == 401
        assert response.json == {"error": "Not authenticated"}


def test_api_invitation_status_success(app_with_login_invitations_enabled):
    """Test the /api/invitation/status endpoint with a successful resolution."""
    mock_status = {
        "id": "test-resolution-id",
        "status": "SUCCESS",
        "created_at": "2023-01-01 12:00:00",
    }

    _test_invitation_status_api(
        app_with_login_invitations_enabled,
        resolution_id="test-resolution-id",
        expected_status=200,
        expected_response=mock_status,
        mock_status=mock_status,
    )


def test_api_invitation_status_pending(app_with_login_invitations_enabled):
    """Test the /api/invitation/status endpoint with a pending resolution."""
    mock_status = {
        "id": "test-resolution-id",
        "status": "PENDING",
        "created_at": "2023-01-01 12:00:00",
    }

    _test_invitation_status_api(
        app_with_login_invitations_enabled,
        resolution_id="test-resolution-id",
        expected_status=200,
        expected_response=mock_status,
        mock_status=mock_status,
    )


def test_api_invitation_status_error(app_with_login_invitations_enabled):
    """Test the /api/invitation/status endpoint with an error resolution."""
    mock_status = {
        "id": "test-resolution-id",
        "status": "ERROR",
        "created_at": "2023-01-01 12:00:00",
    }

    _test_invitation_status_api(
        app_with_login_invitations_enabled,
        resolution_id="test-resolution-id",
        expected_status=200,
        expected_response=mock_status,
        mock_status=mock_status,
    )


def test_api_invitation_status_unauthenticated(app_with_login_invitations_enabled):
    """Test the /api/invitation/status endpoint with an unauthenticated user."""
    with app_with_login_invitations_enabled.test_client() as client:
        # Make a request without setting up a session
        response = client.get("/api/invitation/status/test-resolution-id")

        # Check that the response is a 401 status code with the expected error message
        assert response.status_code == 401
        assert response.json == {"error": "Not authenticated"}


def test_api_invitation_status_invalid_id(app_with_login_invitations_enabled):
    """Test the /api/invitation/status endpoint with an invalid resolution ID."""
    from services.auth.central.server import invitation_service

    # Save the original function
    original_get_status = invitation_service.get_invitation_resolution_status

    try:
        # Mock the function to raise a ValueError
        def mock_get_status(res_id):
            raise ValueError("Invalid resolution ID")

        invitation_service.get_invitation_resolution_status = mock_get_status

        with app_with_login_invitations_enabled.test_client() as client:
            with client.session_transaction() as session:
                session["user_email"] = "<EMAIL>"
                session["idp_user_id"] = "123"

            response = client.get("/api/invitation/status/invalid-id")
            assert response.status_code == 400
            assert response.json == {"error": "Invalid resolution ID"}
    finally:
        # Restore the original function
        invitation_service.get_invitation_resolution_status = original_get_status


def _test_invitation_status_api(
    app_with_login_invitations_enabled,
    resolution_id,
    expected_status=200,
    expected_response=None,
    mock_status=None,
):
    """Helper function to test the invitation status API with the given resolution ID.

    Args:
        app: The Flask app to test
        resolution_id: The resolution ID to check
        expected_status: The expected HTTP status code
        expected_response: The expected response JSON
        mock_status: Optional mock status to return from the invitation service
    """
    from services.auth.central.server import invitation_service

    # Save the original function
    original_get_status = invitation_service.get_invitation_resolution_status

    try:
        # If a mock status is provided, replace the function
        if mock_status is not None:

            def mock_get_status(res_id):
                if res_id != resolution_id:
                    raise ValueError(f"Unexpected resolution ID: {res_id}")
                return mock_status

            invitation_service.get_invitation_resolution_status = mock_get_status

        with app_with_login_invitations_enabled.test_client() as client:
            with client.session_transaction() as session:
                session["user_email"] = "<EMAIL>"
                session["idp_user_id"] = "123"

            response = client.get(f"/api/invitation/status/{resolution_id}")
            assert response.status_code == expected_status

            if expected_response:
                for key, value in expected_response.items():
                    assert response.json[key] == value

        return response
    finally:
        # Restore the original function
        invitation_service.get_invitation_resolution_status = original_get_status


def test_existing_user_with_invitations_redirect(app_with_login_invitations_enabled):
    """Test that existing users with invitations are redirected to the invitations page."""
    from services.auth.central.server import invitation_service

    original_has_invitations = invitation_service.has_invitations

    try:

        def mock_has_invitations(_):
            return True

        invitation_service.has_invitations = mock_has_invitations

        with app_with_login_invitations_enabled.test_client() as client:
            # Set up a session for an existing user
            with client.session_transaction() as session:
                session["user_email"] = "<EMAIL>"
                session["idp_user_id"] = "test123"
                session["nonce"] = "test-nonce"

            response = client.get(
                "/terms-accept",
                query_string=working_query_params(),
            )

            assert response.status_code == 302
            assert "/invitations" in response.headers["Location"]
            assert "continue_url=" in response.headers["Location"]
    finally:
        invitation_service.has_invitations = original_has_invitations


def test_existing_user_without_invitations_no_redirect(
    app_with_login_invitations_enabled,
):
    """Test that existing users without invitations are not redirected to the invitations page."""
    from services.auth.central.server import invitation_service

    original_has_invitations = invitation_service.has_invitations

    try:

        def mock_has_invitations(_):
            return False

        invitation_service.has_invitations = mock_has_invitations

        with app_with_login_invitations_enabled.test_client() as client:
            # Set up a session for an existing user
            with client.session_transaction() as session:
                session["user_email"] = "<EMAIL>"
                session["idp_user_id"] = "test123"
                session["nonce"] = "test-nonce"

            response = client.get(
                "/authorize",
                query_string=working_query_params(),
            )

            assert response.status_code == 302
            assert "/invitations" not in response.headers["Location"]
    finally:
        invitation_service.has_invitations = original_has_invitations


def test_invitations_check_disabled_by_feature_flag(app, feature_flags):
    """Test that invitation checks are skipped when the feature flag is disabled."""
    from services.auth.central.server import app as auth_app
    from services.auth.central.server import invitation_service

    feature_flags.set_flag(auth_app._LOGIN_INVITATIONS_ENABLED, False)

    original_has_invitations = invitation_service.has_invitations

    try:
        # Mock the invitation_service.has_invitations function to raise an exception
        # This will verify the function is not called when the feature flag is disabled
        def mock_has_invitations(_):
            raise Exception(
                "This function should not be called when feature flag is disabled"
            )

        invitation_service.has_invitations = mock_has_invitations

        with app.test_client() as client:
            # Set up a session for an existing user
            with client.session_transaction() as session:
                session["user_email"] = "<EMAIL>"
                session["idp_user_id"] = "test123"
                session["nonce"] = "test-nonce"

            response = client.get(
                "/authorize",
                query_string=working_query_params(),
            )

            assert response.status_code == 302
            assert "/invitations" not in response.headers["Location"]
    finally:
        invitation_service.has_invitations = original_has_invitations


def test_no_tenant_invitation_redirect(app_with_login_invitations_enabled):
    """Test that users with no tenant are redirected to invitations page when they have pending invitations."""
    from services.auth.central.server import invitation_service

    original_has_invitations = invitation_service.has_invitations

    try:

        def mock_has_invitations(_):
            return True

        invitation_service.has_invitations = mock_has_invitations

        with app_with_login_invitations_enabled.test_client() as client:
            # Set up a session for a user with no tenant
            with client.session_transaction() as session:
                session["user_email"] = "<EMAIL>"
                session["idp_user_id"] = "notenant123"
                session["nonce"] = "test-nonce"

            response = client.get(
                "/terms-accept",
                query_string=working_query_params(),
            )

            assert response.status_code == 302
            assert "/invitations" in response.headers["Location"]
            assert "continue_url=" in response.headers["Location"]
    finally:
        invitation_service.has_invitations = original_has_invitations


def test_invitations_feature_flag_disabled(app, feature_flags):
    """Test that invitation endpoints return 501 when the feature flag is disabled."""
    # Explicitly disable the invitations feature flag
    feature_flags.set_flag(auth_app._LOGIN_INVITATIONS_ENABLED, False)

    with app.test_client() as client:
        # Set up a session to avoid authentication errors
        with client.session_transaction() as session:
            session["user_email"] = "<EMAIL>"
            session["idp_user_id"] = "123"

        # Test the /invitations page
        response = client.get("/invitations")
        assert (
            response.status_code == 501
        ), "The /invitations endpoint should return 501 when the feature flag is disabled"
        assert "Invitations feature is not enabled" in str(response.data)

        # Test the /api/invitation/status endpoint
        response = client.get("/api/invitation/status/test-id")
        assert (
            response.status_code == 501
        ), "The /api/invitation/status endpoint should return 501 when the feature flag is disabled"
        assert "Invitations feature is not enabled" in str(response.data)

        # Test the /api/invitation/resolve endpoint
        response = client.post(
            "/api/invitation/resolve", json={"accept_invitation_id": "test-id"}
        )
        assert (
            response.status_code == 501
        ), "The /api/invitation/resolve endpoint should return 501 when the feature flag is disabled"
        assert "Invitations feature is not enabled" in str(response.data)


def test_tos_unauthorized(client):
    """Test that /tos requires authorization."""
    response = client.get("/tos")
    assert response.status_code == 401
    assert "Missing or invalid authorization header" in str(response.data)


def test_tos_invalid_token(client):
    """Test that /tos rejects invalid tokens."""
    response = client.get(
        "/tos",
        headers={"Authorization": "Bearer invalid-token"},
    )
    assert response.status_code == 401
    assert "Invalid token" in str(response.data)


def create_token(app_under_test: AppUnderTest, tenant_id: str, email_address: str):
    """Create a token for the given tenant and email address."""
    user = app_under_test.front_end_token_service.EnsureUserInTenant(
        front_end_token_service_pb2.EnsureUserInTenantRequest(
            email_address=email_address,
            tenant_id=tenant_id,
            mode=front_end_token_service_pb2.TenantEnsureMode.TENANT_ENSURE_MODE_ADD_IF_EMPTY,
        )
    ).user

    code = "_" + secrets.token_hex(nbytes=16)
    app_under_test.front_end_token_service.CreateCode(
        front_end_token_service_pb2.CreateCodeRequest(
            code=code,
            email=email_address,
            idp_user_id="idp_" + email_address,
            augment_user_id=user.id,
            client_id="augment-vscode-extension",
            tenant_id=tenant_id,
            redirect_uri="http://localhost:3000",
            code_challenge="eqEvNh2v4-XpxstYZNyL0t2YwSjxTdRQFObvgMTestU",
        )
    )
    return app_under_test.front_end_token_service.TokenFromCode(
        front_end_token_service_pb2.TokenFromCodeRequest(
            code=code,
            code_verifier="9683e0859a76cde1e20898885833ea4d90f0b48adcec16f751405d59e509f8b0",
            client_id="augment-vscode-extension",
            redirect_uri="http://localhost:3000",
            grant_type="authorization_code",
        )
    ).access_token


def test_tos_enterprise(client_kit):
    """Test that users can retrieve the enterprise TOS."""
    client = client_kit.client

    token = create_token(
        client_kit,
        tenant_id="foo567",
        email_address="<EMAIL>",
    )

    # Test the /tos endpoint with our token
    response = client.get(
        "/tos",
        headers={"Authorization": f"Bearer {token}"},
    )

    assert response.status_code == 200
    data = response.json
    assert data["version"] == "v1.3"
    assert data["is_community"] is False
    assert "tos_text" in data


def test_tos_community(client_kit):
    """Test that users can retrieve the community TOS."""
    client = client_kit.client

    token = create_token(
        client_kit,
        tenant_id="bar567",
        email_address="<EMAIL>",
    )

    # Test the /tos endpoint with our token
    response = client.get(
        "/tos",
        headers={"Authorization": f"Bearer {token}"},
    )

    assert response.status_code == 200
    data = response.json
    assert data["version"] == "v1.3"
    assert data["is_community"] is True
    assert "tos_text" in data


def create_bogus_state_args() -> str:
    """Create bogus state args for testing."""
    return base64.urlsafe_b64encode(
        urlencode(
            [
                ("client_id", "augment-vscode-extension"),
                ("redirect_uri", "http://localhost:3000"),
                ("response_type", "code"),
                ("state", "fake-state"),
                ("code_challenge", "eqEvNh2v4-XpxstYZNyL0t2YwSjxTdRQFObvgMTestU"),
                ("code_challenge_method", "S256"),
            ]
        ).encode()
    ).decode()


def test_authorize_token_throws_exception_that_error_page_returned(app):
    """Test that an exception thrown in authorize_token is caught and an error page is returned."""
    with app.test_client() as client:
        app.config.update(
            augment_override_authorize_access_token=OAuthError(
                error="test_error", description="test_description"
            )
        )

        response = client.get(
            "/oauth2/callback?" + urlencode({"state": create_bogus_state_args()})
        )

        assert response.status_code == 401
        assert "test_error" in response.text
        assert "test_description" in response.text


@pytest.mark.parametrize(
    "library_error, library_description, error, description, expected_message, expected_details",
    [
        (
            "state_mismatch",
            "CSRF token does not match",
            None,
            None,
            "We encountered an unexpected error.",
            "error: state_mismatch.*description: CSRF token does not match",
        ),
        (
            "invalid_request",
            "something went wrong",
            "invalid_request",
            "something went wrong",
            "We encountered an unexpected error.",
            "error: invalid_request.*description: something went wrong",
        ),
        (
            "access_denied",
            "you are sus",
            "access_denied",
            "you are sus",
            "Sign in failed: the system that you used to sign in refused the sign in.",
            "error: access_denied.*description: you are sus",
        ),
        (
            "access_denied",
            "[DENY] vs:foo",
            "access_denied",
            "[DENY] vs:foo",
            "Sign up failed: your login did not pass checks.",
            "",
        ),
        (
            "access_denied",
            "not assigned",
            "access_denied",
            "not assigned",
            "Sign in failed: you have not been assigned access to Augment in your organization's sign in system.",
            "",
        ),
    ],
)
def test_oauth_error_to_user_message(
    app,
    library_error,
    library_description,
    error,
    description,
    expected_message,
    expected_details,
):
    """Test that oauth_error_to_user_message returns the correct message and details."""
    message, details = auth_app.oauth_error_to_user_message(
        library_error=library_error,
        library_description=library_description,
        error=error,
        description=description,
    )
    assert message == expected_message
    assert re.search(expected_details, details) is not None


@pytest.mark.parametrize(
    "is_individual,expected_tenant",
    [(True, "individual-tenant"), (False, "signup-tenant")],
)
def test_get_candidate_signup_tenant_list(
    app, feature_flags, is_individual, expected_tenant
):
    """Test that get_candidate_signup_tenant_list returns the correct tenant list."""
    feature_flags.set_flag(auth_app._INDIVIDUAL_TENANT, "individual-tenant")
    feature_flags.set_flag(auth_app._SIGNUP_TENANT, "signup-tenant")
    app.config.individual_tenant = "backup-individual-tenant-from-config"
    app.config.signup_tenant = "backup-signup-tenant-from-config"
    assert auth_app.get_candidate_signup_tenant_list(
        app.config, is_individual=is_individual
    ) == [expected_tenant], "Feature flag should override config value."


@pytest.mark.parametrize("is_individual", [True, False])
def test_get_candidate_signup_tenant_list_no_tenant(app, feature_flags, is_individual):
    """Test that get_candidate_signup_tenant_list returns an empty list when no tenant is configured."""
    feature_flags.set_flag(auth_app._INDIVIDUAL_TENANT, "")
    feature_flags.set_flag(auth_app._SIGNUP_TENANT, "")
    app.config.individual_tenant = None
    app.config.signup_tenant = None
    tenant_list = auth_app.get_candidate_signup_tenant_list(
        app.config, is_individual=is_individual
    )
    assert tenant_list == [], "Should return empty list if no tenant is configured."


@pytest.mark.parametrize(
    "is_individual,expected_tenant",
    [
        (True, "backup-individual-tenant-from-config"),
        (False, "backup-signup-tenant-from-config"),
    ],
)
def test_get_candidate_signup_tenant_list_fallback(
    app, feature_flags, is_individual, expected_tenant
):
    """Test that get_candidate_signup_tenant_list falls back to config values when feature flags are empty."""
    feature_flags.set_flag(auth_app._INDIVIDUAL_TENANT, "")
    feature_flags.set_flag(auth_app._SIGNUP_TENANT, "")
    app.config.individual_tenant = "backup-individual-tenant-from-config"
    app.config.signup_tenant = "backup-signup-tenant-from-config"
    tenant_list = auth_app.get_candidate_signup_tenant_list(
        app.config, is_individual=is_individual
    )
    assert tenant_list == [
        expected_tenant
    ], "Should fall back to config value when feature flag is empty."


def test_get_signup_tenant_deterministic_selection():
    """Test that get_signup_tenant deterministically selects tenants based on idp_user_id."""
    # Set up multiple tenants in feature flags
    tenant_list = ["tenant1", "tenant2", "tenant3", "tenant4"]

    # Test deterministic selection for tenants
    test_idp_ids = [
        "idp|user1",
        "idp|user2",
        "idp|user3",
        "idp|different_user",
    ]

    # Verify that the same idp_user_id always returns the same tenant
    for idp_user_id in test_idp_ids:
        tenants = [
            auth_app.get_signup_tenant(tenant_list, idp_user_id) for _ in range(3)
        ]
        assert all(
            tenant == tenants[0] for tenant in tenants
        ), f"Same idp_user_id '{idp_user_id}' should return the same tenant"

    # Test that the distribution is reasonably balanced
    # Count occurrences of each tenant with 100 different IDs
    many_idp_ids = [f"idp|balanced{i}" for i in range(100)]
    tenant_counts = {}

    for idp_id in many_idp_ids:
        selected_tenant = auth_app.get_signup_tenant(tenant_list, idp_id)
        tenant_counts[selected_tenant] = tenant_counts.get(selected_tenant, 0) + 1

    # With 100 IDs and 4 tenants, each tenant should get roughly 25 assignments
    # Allow for some variance (15-35 is a reasonable range)
    for tenant, count in tenant_counts.items():
        assert (
            15 <= count <= 35
        ), f"Tenant {tenant} got {count} assignments, expected ~25"


# working_query_params may change in length and may possible make padding disappear.
# Using different lengths of state means at least one test case should fail if we stop
# stripping padding
@pytest.mark.parametrize("extra_state_length", [1, 2])
def test_login_state_parameter_no_equal_sign(client, extra_state_length: int):
    """Test that the state parameter in the login redirect URL doesn't contain an equal sign."""
    response = client.get(
        "/login",
        query_string=working_query_params() | {"state": "x" * extra_state_length},
    )
    location = location_from_login_response(response)

    # Extract the state parameter from the redirect URL
    parsed_url = urlparse(location)
    query_params = parse_qs(parsed_url.query)

    assert "state" in query_params, "state parameter missing in redirect URL"
    state_value = query_params["state"][0]

    # Verify state doesn't contain equal signs
    assert "=" not in state_value, "state parameter contains equal sign"


def test_user_tenant_assignment_precedence(client_kit):
    """Test that tenant assignment takes precedence over email domain matching."""
    # Use the first tenant (which has domain "test.com")
    domain_tenant = TEST_TENANTS[0]  # "foo567" with domain "test.com"
    other_tenant = TEST_TENANTS[1]  # "foo568" with empty domain

    # Create a user with email matching domain_tenant but assign to other_tenant
    email = f"user@{domain_tenant.auth_configuration.domain}"
    user_id = f"idp|{secrets.token_hex(8)}"

    # Create a user and assign them to the other tenant (not matching email domain)
    user = client_kit.front_end_token_service.EnsureUserInTenant(
        front_end_token_service_pb2.EnsureUserInTenantRequest(
            email_address=email,
            tenant_id=other_tenant.id,
            idp_user_id=user_id,
            mode=front_end_token_service_pb2.TenantEnsureMode.TENANT_ENSURE_MODE_OVERWRITE,
        )
    ).user

    # Verify user is in the other tenant
    assert other_tenant.id in user.tenants, "User should be in the other tenant"
    # Verify user has no other tenants
    assert len(user.tenants) == 1, "User should only be in one tenant"

    # Now call the accept endpoint - it should use the tenant assignment (other_tenant)
    # rather than the email domain match (domain_tenant)
    response = client_kit.accept_get_call(
        idp_user_id=user_id, email_address=email, query_params=working_query_params()
    )

    # Verify the response is successful
    assert response.status_code == 200, "Expected successful response"

    # Get the user again to verify they're still in the correct tenant
    updated_user = client_kit.front_end_token_service.GetUser(
        front_end_token_service_pb2.GetUserRequest(
            idp_user_id=user_id,
            email_address=email,
        )
    ).user

    # Verify the user is still in the other tenant and not moved to the domain tenant
    assert len(updated_user.tenants) == 1, "User should only be in one tenant"
    assert (
        other_tenant.id in updated_user.tenants
    ), "User should still be in the other tenant"
    assert (
        domain_tenant.id not in updated_user.tenants
    ), "User should not be in the domain tenant"


def idp_user_id_for_tenant(_tenant):
    return f"idp|{secrets.token_hex(8)}"


def create_user_in_tenant(client_kit, email, tenant):
    idp_user_id = idp_user_id_for_tenant(tenant)
    existing_user = get_user(client_kit, idp_user_id, email)

    # Create a user if one doesn't exist yet and assign them to the other tenant (not matching email domain)
    user = client_kit.front_end_token_service.EnsureUserInTenant(
        front_end_token_service_pb2.EnsureUserInTenantRequest(
            email_address=email,
            tenant_id=tenant.id,
            idp_user_id=idp_user_id,
            augment_user_id=existing_user.id if existing_user else "",
            mode=front_end_token_service_pb2.TenantEnsureMode.TENANT_ENSURE_MODE_OVERWRITE,
        )
    ).user
    return user, idp_user_id


def get_user(client_kit, idp_user_id, email_address):
    return client_kit.front_end_token_service.GetUser(
        front_end_token_service_pb2.GetUserRequest(
            idp_user_id=idp_user_id,
            email_address=email_address,
        )
    ).user


def test_enterprise_domain_wins_over_self_serve(request, client_kit):
    """Test that enterprise domain takes precedence over self-serve tenant."""
    enterprise_tenant = get_enterprise_tenant()
    self_serve_tenant = get_self_serve_tenant()

    email = f"user-{request.node.name}@{enterprise_tenant.auth_configuration.domain}"

    _ = create_user_in_tenant(client_kit, email=email, tenant=self_serve_tenant)

    enterprise_idp_user_id = idp_user_id_for_tenant(enterprise_tenant)
    response = client_kit.accept_get_call(
        idp_user_id=enterprise_idp_user_id,
        email_address=email,
    )

    # Verify the response is successful
    assert response.status_code == 200, "Expected successful response"

    # Get the user again to verify they're in the correct tenant
    updated_user = get_user(
        client_kit, idp_user_id=enterprise_idp_user_id, email_address=email
    )

    assert len(updated_user.tenants) == 1, "User should only be in one tenant"
    assert (
        enterprise_tenant.id in updated_user.tenants
    ), "User should be in the enterprise tenant"


def test_idp_user_id_login_path(request, client_kit):
    """Test that we can login with an idp_user_id that already exists in the system."""
    tenant = get_self_serve_tenant()
    email = f"user-{request.node.name}@gmail.com"

    _, idp_user_id = create_user_in_tenant(client_kit, email=email, tenant=tenant)

    response = client_kit.accept_get_call(
        idp_user_id=idp_user_id,
        email_address="x" + email,
        query_params=working_query_params(),
    )

    # This is fragile - we change the wording and get a false positive on this test
    # But it's better than nothing
    assert "Sign up" not in response.text


def test_email_case_insensitive_lookup(client_kit):
    """Test that user lookup by email is case-insensitive."""
    # Create a user with lowercase email
    lowercase_email = "<EMAIL>"
    uppercase_email = "<EMAIL>"
    mixed_case_email = "<EMAIL>"

    # Create user with lowercase email
    user, _ = create_user_in_tenant(
        client_kit, email=lowercase_email, tenant=get_self_serve_tenant()
    )

    # Test lookup with different email capitalizations
    for test_email in [lowercase_email, uppercase_email, mixed_case_email]:
        # Get user directly from front_end_token_service
        found_user = client_kit.front_end_token_service.GetUser(
            front_end_token_service_pb2.GetUserRequest(
                email_address=test_email,
                # Intentionally omit idp_user_id to test email-only lookup
            )
        ).user

        # Assert user was found
        assert found_user is not None, f"User not found with email {test_email}"
        assert found_user.id == user.id, f"Wrong user found with email {test_email}"
        assert found_user.email == lowercase_email, "Original email should be preserved"
