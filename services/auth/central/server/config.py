"""Configuration for the Auth-Central service."""

import pathlib
from dataclasses import dataclass
from typing import List, Optional

from dataclasses_json import dataclass_json

import services.lib.grpc.tls_config.tls_config as tls_config


@dataclass_json
@dataclass(frozen=True)
class GcpConfig:
    """Bigtable configuration."""

    instance_id: str
    table_name: str
    setup_table: bool = False


@dataclass_json
@dataclass(frozen=True)
class GrpcConfig:
    """gRPC configuration."""

    client_mtls: Optional[tls_config.ClientConfig] = None


@dataclass_json
@dataclass(frozen=True)
class LoginPageConfig:
    """Login page details."""

    auth0_login_url: str


@dataclass_json
@dataclass(frozen=True)
class TenantWatcherConfig:
    """Configuration specific to sharded namespaces."""

    tenant_watcher_endpoint: str
    api_proxy_hostname_domain: str
    """The domain for api-proxy endpoints."""


@dataclass_json
@dataclass(frozen=True)
class AuthConfig:
    """Configuration for authenticating service tokens."""

    token_exchange_endpoint: str


@dataclass_json
@dataclass(frozen=True)
class Auth0Config:
    """Configuration for auth0."""

    credentials_path: str
    server_metadata_url: str


@dataclass_json
@dataclass(frozen=True)
class OAuthClientConfig:
    """Configuration for a client."""

    name: str
    redirect_uris: list[str]
    expiration_time_seconds: int = 0
    instant_redirect: bool = False


@dataclass_json
@dataclass(frozen=True)
class SegmentConfig:
    """Configuration for Segment Analytics."""

    write_key: str
    host: str
    enabled: bool = True


@dataclass_json
@dataclass(frozen=True)
class RecaptchaConfig:
    """Configuration for reCAPTCHA."""

    keys_path: str
    project_id: str


@dataclass_json
@dataclass(frozen=True)
class VerisoulConfig:
    """Configuration for Verisoul."""

    env: str
    project_id: str
    api_key_path: str


@dataclass_json
@dataclass(frozen=True)
class Config:
    """Configuration for the Auth Central service."""

    auth_config: AuthConfig
    backend_port: int
    client_config_map: dict[str, OAuthClientConfig]
    code_ttl_seconds: int
    feature_flags_sdk_key_path: Optional[str]
    dynamic_feature_flags_endpoint: Optional[str]
    gcp: GcpConfig
    grpc: GrpcConfig
    login_page: LoginPageConfig
    tenant_watcher: TenantWatcherConfig
    public_bind_address: str
    prometheus_bind_address: str

    auth_url: str
    secrets_path: str

    recaptcha_config: Optional[RecaptchaConfig] = None

    signup_tenant: Optional[str] = None
    individual_tenant: Optional[str] = None
    login_auth0: Optional[Auth0Config] = None
    signup_auth0: Optional[Auth0Config] = None
    segment: Optional[SegmentConfig] = None

    staging_user_email_regex: Optional[str] = None
    staging_url: Optional[str] = None
    """If set, will redirect users that match staging_user_email_regex to staging_url.

    Should only be set in prod, and the value should be the URL to staging
    auth-central. The URL should have a scheme (https://) set.
    """
    dev_callback_url: Optional[str] = None

    enable_flag_endpoint: bool = False
    request_insight_publisher_config_path: Optional[str] = None

    verisoul: Optional[VerisoulConfig] = None

    customer_ui_url: Optional[str] = None

    allowed_discovery_user_email_regex: Optional[str] = None
    """If set, only emails matching this regex can sign up for discovery/vanguard tenants.

    Should only be set in staging or dev to restrict signup domains. In production, this should be
    null to allow all domains.
    """

    @classmethod
    def load(cls, config_file: pathlib.Path) -> "Config":
        return cls.schema().loads(  # pylint: disable=no-member # type: ignore
            config_file.read_text()
        )
