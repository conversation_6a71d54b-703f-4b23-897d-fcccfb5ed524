syntax = "proto3";
package auth;

import "google/protobuf/timestamp.proto";
import "services/auth/central/server/auth_entities.proto";

// Auth service is responsible for providing read access to authentication
// and authorization related objects such as tokens, users, etc.
service AuthService {
  // 1. Adds user if one does not exist (based on email)
  // 2. Associate user with tenant if needed.
  // Does not fail if user already exists and/or is already associated with the tenant.
  // Fails if tenant does not exist. (Note: some tenant information comes from configuration)
  rpc AddUserToTenant(AddUserToTenantRequest) returns (AddUserToTenantResponse);

  // Get information on user/tenant association.
  rpc GetUserOnTenant(GetUserOnTenantRequest) returns (GetUserOnTenantResponse);

  // Update information on user/tenant association.
  rpc UpdateUserOnTenant(UpdateUserOnTenantRequest) returns (UpdateUserOnTenantResponse);

  // Remove association between user and tenant and all related information.
  rpc RemoveUserFromTenant(RemoveUserFromTenantRequest) returns (RemoveUserFromTenantResponse);

  // Update the user's email.
  rpc UpdateUserEmail(UpdateUserEmailRequest) returns (UpdateUserEmailResponse);

  // List all users in a tenant.
  rpc ListTenantUsers(ListTenantUsersRequest) returns (ListTenantUsersResponse);

  // get information about the user
  rpc GetUser(GetUserRequest) returns (GetUserResponse);

  // Get Users with possible search filter
  rpc GetUsers(GetUsersRequest) returns (GetUsersResponse);

  // RPC for auth_query GetTokenInfo (in the process of migrating to use the global bigtable)
  // Note that auth_query will still be responsible for asking token exchange for a signed JWT.
  rpc GetTokenInfo(GetTokenInfoRequest) returns (GetTokenInfoResponse);

  // Revoke any user cookies used for authentication
  rpc RevokeUserCookies(RevokeUserCookiesRequest) returns (RevokeUserCookiesResponse);

  // Revoke user tokens and cookies used for authentication
  rpc RevokeUser(RevokeUserRequest) returns (RevokeUserResponse);

  // Add a suspension to a user.
  rpc CreateUserSuspension(CreateUserSuspensionRequest) returns (CreateUserSuspensionResponse);

  // Update suspension exemption for a user.
  rpc UpdateSuspensionExemption(UpdateSuspensionExemptionRequest) returns (UpdateSuspensionExemptionResponse);

  // Lift one or more suspensions from one user.
  rpc DeleteUserSuspensions(DeleteUserSuspensionsRequest) returns (DeleteUserSuspensionsResponse);

  // Get the information of a user related to billing.
  rpc GetUserBillingInfo(GetUserBillingInfoRequest) returns (GetUserBillingInfoResponse);

  // Update the information of a user related to billing.
  // TODO (Bin): remove this API once Stripe to Orb migration is done
  rpc UpdateUserBillingInfo(UpdateUserBillingInfoRequest) returns (UpdateUserBillingInfoResponse);

  // Parse through users and delete self-serve accounts for users that are on team / enterprise plans already
  rpc RemoveSelfServeAccountsForTeam(RemoveSelfServeAccountsForTeamRequest) returns (RemoveSelfServeAccountsForTeamResponse);

  // Remove all suspensions of the indicated types. Triggers background processing.
  rpc SuspensionCleanup(SuspensionCleanupRequest) returns (SuspensionCleanupResponse);

  // Parse through all users and remove extra self-serve accounts for users that are only in self-serve tenants.
  rpc RemoveExtraSelfServeTenantsFromUsers(RemoveExtraSelfServeTenantsFromUsersRequest) returns (RemoveExtraSelfServeTenantsFromUsersResponse);

  // Parse through all users and remove deleted tenants
  rpc RemoveDeletedTenantsFromUsers(RemoveDeletedTenantsFromUsersRequest) returns (RemoveDeletedTenantsFromUsersResponse);

  // Parse through all users and remove duplicate users - merging duplicate accounts into a single account
  rpc DeduplicateUsersByEmail(DeduplicateUsersByEmailRequest) returns (DeduplicateUsersByEmailResponse);

  // Parse through all users and set their BillingMethod to ORB if it's not already
  rpc SetUsersBillingMethodToOrb(SetUsersBillingMethodToOrbRequest) returns (SetUsersBillingMethodToOrbResponse);

  // Scan Stripe subscriptions for legacy self-serve team tenants
  rpc ScanLegacySelfServeTeams(ScanLegacySelfServeTeamsRequest) returns (ScanLegacySelfServeTeamsResponse);

  // Delete a single TenantSubscriptionMapping by tenant ID
  rpc DeleteTenantSubscriptionMapping(DeleteTenantSubscriptionMappingRequest) returns (DeleteTenantSubscriptionMappingResponse);

  // Parse through all users and remove duplicate users by idp user id - merging duplicate accounts into a single account
  rpc DeduplicateUsersByIdpUserId(DeduplicateUsersByIdpUserIdRequest) returns (DeduplicateUsersByIdpUserIdResponse);

  // De-duplicate the user's tenant list - does NOT update any mappings
  rpc DeduplicateUserTenantList(DeduplicateUserTenantListRequest) returns (DeduplicateUserTenantListResponse);

  // Sync addresses from Stripe to Orb
  rpc SyncAddresses(SyncAddressesRequest) returns (SyncAddressesResponse);

  // Migrate legacy self-serve teams from Stripe to Orb
  rpc MigrateLegacySelfServeTeams(MigrateLegacySelfServeTeamsRequest) returns (MigrateLegacySelfServeTeamsResponse);

  // Migrate populate IDP user mappings
  rpc MigratePopulateIDPUserMappings(MigratePopulateIDPUserMappingsRequest) returns (MigratePopulateIDPUserMappingsResponse);
}

message GetUserRequest {
  reserved 1;
  string user_id = 2;
  // This is an optional field because we also support
  // verification of the user's token via the user id in the token
  optional string tenant_id = 3;
}

message GetUserResponse {
  auth_entities.User user = 1;
}

message GetUserBillingInfoRequest {
  string user_id = 1;
  string tenant_id = 2;
}

message GetUserBillingInfoResponse {
  string user_id = 1;
  string email = 2;

  // Whether this user is on a self-serve team
  bool is_self_serve_team = 3;

  // If the user is on a self-serve team, this is the Orb customer id of the team
  // Otherwise, this is the Orb customer id of the user
  string orb_customer_id = 4;
}

message UpdateUserBillingInfoRequest {
  string user_id = 1;
  string tenant_id = 2;

  // The fields below will be updated only if they are sepecified.
  optional auth_entities.BillingMethod billing_method = 3;
  optional string orb_customer_id = 4;
  optional string orb_subscription_id = 5;
  optional string stripe_customer_id = 6;
}

message UpdateUserBillingInfoResponse {}

message AddUserToTenantRequest {
  reserved 1;
  string email = 2;
  string tenant_id = 3;
}

message AddUserToTenantResponse {
  auth_entities.User user = 1;
}

// Updates information regarding user/tenant association.
message UpdateUserOnTenantRequest {
  // The tenant ID and user ID mapping (as compound key) to update.
  string tenant_id = 1;
  string user_id = 2;

  // List of rules to set (replace) on the user.
  repeated auth_entities.CustomerUiRole customer_ui_roles = 3;
}

// Returns the updated information on user-tenant association.
message UpdateUserOnTenantResponse {
  // The customer-ui roles on the user post update.
  repeated auth_entities.CustomerUiRole customer_ui_roles = 1;
}

// Use this message to get information
// on user/tenant association.
message GetUserOnTenantRequest {
  // The tenant ID and user ID mapping (as compound key) to retrieve.
  string tenant_id = 1;
  string user_id = 2;
}

// Returns information on user/tenant association.
message GetUserOnTenantResponse {
  // The customer-ui roles on the user.
  repeated auth_entities.CustomerUiRole customer_ui_roles = 1;
}

message RemoveUserFromTenantRequest {
  reserved 1;
  string user_id = 2;
  string tenant_id = 3;
}

// Dedicated empty type is gRPC best practice
message RemoveUserFromTenantResponse {}

message UpdateUserEmailRequest {
  string user_id = 1;
  // Current email, used for verification
  string current_email = 2;
  // New email to set
  string new_email = 3;
}

message UpdateUserEmailResponse {}

message ListTenantUsersRequest {
  reserved 1;
  string tenant_id = 2;
}

message ListTenantUsersResponse {
  repeated auth_entities.User users = 1;
}

message GetTokenInfoRequest {
  // The token to return information
  string token = 1 [debug_redact = true];

  // A string identifying who is requesting the information.
  // Number of requestors should be low.
  string requestor = 2;
}
message GetTokenInfoResponse {
  // The user ID associated with the token.
  // NB: This is the deprecated notion of a "user id", which is either an email or an API token id.
  // We are migrating to using `augment_user_id` and `user_email` instead.
  string user_id = 1;

  // The tenant ID associated with the token.
  string tenant_id = 2;

  // The tenant name associated with the token.
  //
  // In single tenant namespaces, the tenant name will be the same as the
  // namespace.
  string tenant_name = 3;

  // The UUID from the auth database.
  string augment_user_id = 4;

  // The email of the user.
  string user_email = 5 [debug_redact = true];

  // Subscription information
  // Keep all of these in sync with subscription in services/auth/query/auth_query.proto

  oneof subscription {
    EnterpriseSubscription enterprise = 6;
    ActiveSubscription active_subscription = 7;
    Trial trial = 8;
    InactiveSubscription inactive_subscription = 9;
  }

  // Current active suspensions
  repeated auth_entities.UserSuspension suspensions = 10;
}

// Enterprise subscription type
message EnterpriseSubscription {
  // Add any enterprise-specific fields here
}

// Active subscription type
message ActiveSubscription {
  // The end date of the subscription - could be a trial end date when using Orb or a paid subscription end date
  google.protobuf.Timestamp end_date = 1;

  // Whether the user is out of credits
  bool usage_balance_depleted = 2;

  // The user's billing method
  auth_entities.BillingMethod billing_method = 3;
}

// Trial subscription type
// This will be deprecated soon, this is only being used for Stripe customers
message Trial {
  // The trial end date
  google.protobuf.Timestamp trial_end = 1;

  // The user's billing method
  auth_entities.BillingMethod billing_method = 2;
}

// Inactive subscription type
message InactiveSubscription {
  // Add any inactive subscription-specific fields here

  // The user's billing method
  auth_entities.BillingMethod billing_method = 1;
}

message RevokeUserCookiesRequest {
  string user_id = 1;
  string tenant_id = 2;
}

// Dedicated empty type is gRPC best practice
message RevokeUserCookiesResponse {}

message RevokeUserRequest {
  string email = 1;
  string tenant_id = 2;
}

message RevokeUserResponse {
  int32 tokens_deleted = 1;
}

message RemoveSelfServeAccountsForTeamRequest {
  bool dry_run = 1;

  // If specified, only remove self-serve accounts for users in this tenant
  // If not specified, remove self-serve accounts for all users
  optional string tenant_id = 2;

  // If specified, don't process any users that are in any of these tenants
  repeated string tenant_ids_to_ignore = 3;
}

message RemoveSelfServeAccountsForTeamResponse {
  message TenantInfo {
    string tenant_id = 1;
    string tenant_name = 2;
  }
  message UserRemovals {
    string user_id = 1;
    repeated TenantInfo primary_tenants = 2;
    repeated TenantInfo removed_tenants = 3;
  }

  repeated UserRemovals removed_users = 1;
  repeated string failed_users = 2;
}

message RemoveExtraSelfServeTenantsFromUsersRequest {
  bool make_changes = 1;

  // If specified, only remove self-serve accounts for users in these namespaces
  // If not specified, remove self-serve accounts for all users
  repeated string namespace_ids = 2;
}

message RemoveExtraSelfServeTenantsFromUsersResponse {
  message TenantInfo {
    string tenant_id = 1;
    string tenant_name = 2;
  }
  message UserRemovals {
    string user_id = 1;
    TenantInfo primary_tenant = 2;
    repeated TenantInfo removed_tenants = 3;
  }

  repeated UserRemovals removed_users = 1;
  repeated string failed_users = 2;
}

message RemoveDeletedTenantsFromUsersRequest {
  bool make_changes = 1;
  // map from tenant ID to tenant name
  map<string, string> tenant_mappings_for_deleted_tenants = 2;
}

message RemoveDeletedTenantsFromUsersResponse {
  message TenantInfo {
    string tenant_id = 1;
    string tenant_name = 2;
  }
  message UserRemovals {
    string user_id = 1;
    repeated TenantInfo removed_tenants = 2;
  }

  repeated UserRemovals removed_users = 1;
  repeated string failed_users = 2;
}

message DeduplicateUsersByEmailRequest {
  bool make_changes = 1;
}

message DeduplicateUsersByEmailResponse {
  message UserMerge {
    // The email that had duplicates
    string email = 1;
    // The user id to use going forward for this user - this should be the lowest user ID
    string primary_user_id = 2;
    // The user IDs that were merged and should no longer be used
    repeated string merged_user_ids = 3;
    // The user ID whose subscription information was copied to the primary user
    string subscription_source_user_id = 4;

    // Tenant information - maps of tenant_id -> tenant_name
    // The tenant that the user will be in going forward - this may have been copied from subscription_source_user_id
    map<string, string> primary_user_tenant = 5;
    // The tenants that were merged and should no longer be used
    map<string, string> merged_user_tenants = 6;

    // Subscription information
    // The customer ID that was preserved and associated with the "most active" subscription
    string preserved_orb_customer_id = 7;
    // The orb subscription ID that was preserved and is the "most active"
    string preserved_orb_subscription_id = 8;
    // The stripe customer ID that was preserved and associated with the "most active" subscription
    string preserved_stripe_customer_id = 9;
    // The orb subscription IDs that will no longer be used
    repeated string merged_user_subscription_ids = 10;
    // The orb subscription IDs that were previously active but were canceled
    repeated string canceled_subscription_ids = 11;

    // Merged IDP user IDs that should now all be associated with the primary user
    repeated string idp_user_ids = 12;
  }

  repeated UserMerge user_merges = 1;
  repeated string failed_users = 2;
}

message DeduplicateUsersByIdpUserIdRequest {
  bool make_changes = 1;
}

message DeduplicateUsersByIdpUserIdResponse {
  repeated DeduplicateUsersByEmailResponse.UserMerge user_merges = 1;
  repeated string failed_users = 2;
}

message ScanLegacySelfServeTeamsRequest {}

message ScanLegacySelfServeTeamsResponse {
  message TeamSubscription {
    string tenant_id = 1;
    string tenant_name = 2;
    int32 user_count = 3;
    string admin_user_id = 4;
    string stripe_customer_id = 5;
    string stripe_subscription_id = 6;
    string stripe_subscription_status = 7;
    string stripe_subscription_price_id = 8;
    string stripe_subscription_price_key = 9;
    int64 stripe_subscription_quantity = 10;
    google.protobuf.Timestamp stripe_subscription_current_period_start = 11;
    google.protobuf.Timestamp stripe_subscription_current_period_end = 12;
    string find_stripe_subscription_error = 13;
  }

  repeated TeamSubscription team_subscriptions = 1;
  repeated string failed_tenants = 2;
}

message DeleteTenantSubscriptionMappingRequest {
  // The tenant ID for which to delete the subscription mapping
  string tenant_id = 1;
}

message DeleteTenantSubscriptionMappingResponse {}

message DeduplicateUserTenantListRequest {
  bool make_changes = 1;
  string user_id = 2;
}

message DeduplicateUserTenantListResponse {
  repeated string previous_tenants = 1;
  string new_tenant = 2;
}

message SyncAddressesRequest {
  bool make_changes = 1; // If false, just report what would be changed without making changes
  repeated string ids_to_sync = 2; // If specified, only sync these users. If not specified, sync all users
}

message SyncAddressesResponse {
  repeated string users_updated = 1;
  repeated string users_failed = 2;
  repeated string users_not_attempted = 3; // they have no address/card in Stripe
  repeated string users_multiple_payment_methods = 4; // they have multiple payment methods in Stripe, not sure what to do
}

message MigrateLegacySelfServeTeamsRequest {
  message LegacyTeamInfo {
    string tenant_id = 1 [deprecated = true];
    string tenant_name = 2;
    string admin_user_id = 3;
    string stripe_customer_id = 4 [deprecated = true];
    string stripe_subscription_id = 5;
    google.protobuf.Timestamp stripe_current_period_start = 6;
    google.protobuf.Timestamp stripe_current_period_end = 7;
    string orb_external_plan_id = 8;
    int64 orb_plan_version_number = 9;
    int32 seats = 10;
    bool apply_free_seat_adjustment = 11;

    // The following fields are only used for a small amount of teams that needs non-dev plans
    string seats_price_id = 12;
    string messages_price_id = 13;
    float price_per_seat = 14;
    float messages_per_seat = 15;
  }

  repeated LegacyTeamInfo teams = 1;
}

message MigrateLegacySelfServeTeamsResponse {
  message MigrationResult {
    string tenant_id = 1 [deprecated = true];
    bool success = 2;
    string error_message = 3;
    string tenant_name = 4;
  }

  repeated MigrationResult results = 1;
  int32 successful_migrations = 2;
  int32 failed_migrations = 3;
}

message MigratePopulateIDPUserMappingsRequest {}

message MigratePopulateIDPUserMappingsResponse {}

// Separate team management service to help distinguish true auth operations from user-driven team
// management. This is purely organizational; AuthService and TeamManagementService share a database
// and are intended to run within the same server.
service TeamManagementService {
  // Create a new tenant for a team, with the caller as the tenant's sole member and admin. This is
  // an async operation. Callers are expected to poll GetCreateTenantForTeamStatus to learn when the
  // operation has completed.
  // This is intended to be a temporary endpoint, until we're ready to create a tenant for every new
  // user.
  rpc CreateTenantForTeam(CreateTenantForTeamRequest) returns (CreateTenantForTeamResponse);
  rpc GetCreateTenantForTeamStatus(GetCreateTenantForTeamStatusRequest) returns (GetCreateTenantForTeamStatusResponse);

  // Invite users to a tenant. This will give users the option of joining the tenant (called a
  // "team" in the UI) when they log in. This will fail if any of the following are true:
  // - The caller is not an admin for the tenant.
  // - The tenant does not have sufficient seats available.
  rpc InviteUsersToTenant(InviteUsersToTenantRequest) returns (InviteUsersToTenantResponse);

  // Get a list of all the pending invitations for a tenant. This could be extended someday to allow
  // filtering by invitation status (so that we can show declined invitations as well).
  rpc GetTenantInvitations(GetTenantInvitationsRequest) returns (GetTenantInvitationsResponse);

  // Get a list of all the pending invitations for a user, by email. This is intended to be called
  // as part of the login flow.
  // Note(jacqueline): I suspect that this will eventually be deprecated in favor of emailed
  // invitation tokens.
  rpc GetUserInvitations(GetUserInvitationsRequest) returns (GetUserInvitationsResponse);

  // Accept or decline invitations. Note that this endpoint relies on the frontend's view of the
  // open invitations, and does not check for additional invitations on the backend. This is
  // intentional, to avoid a race where a user never sees an invitation sent between when the
  // frontend loads the open invitations and when they accept/decline them. Having additional
  // pending invitations is harmless; the user will see them the next time they log in.
  // This endpoint is async. Callers should use GetResolveInvitationsStatus to learn the outcome
  // of the operation.
  rpc ResolveInvitations(ResolveInvitationsRequest) returns (ResolveInvitationsResponse);
  rpc GetResolveInvitationsStatus(GetResolveInvitationsStatusRequest) returns (GetResolveInvitationsStatusResponse);

  // Delete an invitation. The intended usage is to delete a pending invitation (which will free
  // up a seat in the team's subscription).
  rpc DeleteInvitation(DeleteInvitationRequest) returns (DeleteInvitationResponse);

  // Get Orb subscription information (e.g., seat count).
  rpc GetSubscription(GetSubscriptionRequest) returns (GetSubscriptionResponse);

  // List subscriptions in the system.
  rpc ListSubscriptions(ListSubscriptionsRequest) returns (ListSubscriptionsResponse);

  // List tenant subscription mappings in the system.
  rpc ListTenantSubscriptionMappings(ListTenantSubscriptionMappingsRequest) returns (ListTenantSubscriptionMappingsResponse);

  // List user tenant mappings in the system.
  rpc ListUserTenantMappings(ListUserTenantMappingsRequest) returns (ListUserTenantMappingsResponse);

  // Update a subscription (currently just the number of seats). This will fail if any of the
  // following are true:
  // - The caller is not an admin for the tenant.
  // - The update would cause the subscription to have fewer seats than the current number of
  //   users + invitations in the tenant.
  // - The given subscription is associated with a user rather than a tenant. Only team
  //   subscriptions have seats.
  // This operation is async. Callers should poll GetSubscription to learn when their change has
  // been persisted to Orb.
  rpc UpdateSubscription(UpdateSubscriptionRequest) returns (UpdateSubscriptionResponse);

  // Purchase additional Orb credits
  rpc PurchaseCredits(PurchaseCreditsRequest) returns (PurchaseCreditsResponse);

  // Cancel Orb subscription at end of current billing period
  rpc CancelSubscription(CancelSubscriptionRequest) returns (CancelSubscriptionResponse);

  // Get the information of a user in Orb
  rpc GetUserOrbInfo(GetUserOrbInfoRequest) returns (GetUserOrbInfoResponse);

  // Get the plan information of a user in Orb
  rpc GetUserOrbPlanInfo(GetUserOrbPlanInfoRequest) returns (GetUserOrbPlanInfoResponse);

  // Get all Orb plans
  rpc GetAllOrbPlans(GetAllOrbPlansRequest) returns (GetAllOrbPlansResponse);

  // UnschedulePendingSubscriptionCancellation reverts a pending cancellation for a subscription.
  // This is only allowed if the subscription has been scheduled to cancel at
  // the end of the billing period and is still active.
  // If the subscription is for a team, the caller must be an admin.
  //
  // If the subscription is already canceled or is not scheduled to be cancelled, will return an error.
  rpc UnschedulePendingSubscriptionCancellation(UnschedulePendingSubscriptionCancellationRequest) returns (UnschedulePendingSubscriptionCancellationResponse);

  // UnschedulePlanChanges reverts a pending plan change for a subscription.
  // This is only allowed if the subscription has a scheduled plan change.
  // If the subscription is for a team, the caller must be an admin.
  //
  // If the subscription has no scheduled plan change, will return an error.
  rpc UnschedulePlanChanges(UnschedulePlanChangesRequest) returns (UnschedulePlanChangesResponse);

  // This is an operation that will coalesce the user to a given plan
  // The user is not required to be on a plan or have a subscription or be in the correct type of tenant
  //
  // This operation is conditionally asynchronous, if a tier change is required then it will be async, and
  // callers should poll on GetUser which will include the relevant pending state
  rpc PutUserOnPlan(PutUserOnPlanRequest) returns (PutUserOnPlanResponse);

  // Get information about the user's credit status in Orb
  rpc GetUserOrbCreditsInfo(GetUserOrbCreditsInfoRequest) returns (GetUserOrbCreditsInfoResponse);

  // Get information about the user's current payment methods and status
  rpc GetUserOrbPaymentInfo(GetUserOrbPaymentInfoRequest) returns (GetUserOrbPaymentInfoResponse);

  // Get information about the user's subscription from Orb
  rpc GetUserOrbSubscriptionInfo(GetUserOrbSubscriptionInfoRequest) returns (GetUserOrbSubscriptionInfoResponse);

  // TODO: rpc that gets very basic info for page load

  // Get the status of a tenant's plan, specifically if a plan change is pending.
  rpc GetTenantPlanStatus(GetTenantPlanStatusRequest) returns (GetTenantPlanStatusResponse);
}

message CreateTenantForTeamRequest {
  // The user ID of the user creating the team. It is an error if this does not match the user ID
  // in the auth token used to call this endpoint.
  string admin_user_id = 1;
}

message CreateTenantForTeamResponse {
  // Id that can be passed to GetCreateTenantForTeamStatus to poll for the status of the tenant
  // creation.
  string tenant_creation_id = 1;
}

message GetCreateTenantForTeamStatusRequest {
  // Should come from a previous call to CreateTenantForTeam.
  string tenant_creation_id = 1;
}

message GetCreateTenantForTeamStatusResponse {
  auth_entities.TenantCreation tenant_creation = 1;
}

message InviteUsersToTenantRequest {
  // The tenant to invite users to.
  string tenant_id = 1;

  // The emails of the users being invited.
  repeated string invitee_emails = 2 [debug_redact = true];
}

message InviteUsersToTenantResponse {
  // Wrapper around status information for a single invitation creation. Having this wrapper gives
  // us the option of displaying detailed information for each failure, if that's something we
  // decide we want to do in the future.
  message InvitationCreationStatus {
    enum Status {
      // Unused default.
      UNKNOWN = 0;

      // Invitation was successfully created.
      SUCCESS = 1;

      // Generic error status. Some reasons this could happen include:
      // - The invitee has an email domain associated with an enterprise tenant.
      ERROR = 2;
    }

    string email = 1 [debug_redact = true];
    Status status = 2;

    // Only populated if status is SUCCESS.
    string invitation_id = 3;
  }

  // For every email in the request, return the status of the creation.
  repeated InvitationCreationStatus invitation_statuses = 1;
}

message GetTenantInvitationsRequest {
  string tenant_id = 1;
}

message GetTenantInvitationsResponse {
  // Note that this has all invitations, including accepted and declined ones. We'll probably want
  // filtering at some point.
  repeated auth_entities.TenantInvitation invitations = 1;
}

message GetUserInvitationsRequest {
  // The email of the user to find invitations for. This email will be trimmed and lowercased for
  // lookup, but we do NOT normalize "+" or "." out of emails, to allow users to create multiple
  // accounts that way if they wish.
  string email = 1 [debug_redact = true];
}

message GetUserInvitationsResponse {
  // All the pending invitations for the user.
  repeated auth_entities.TenantInvitation invitations = 1;
}

message ResolveInvitationsRequest {
  // The invitation id to accept. Can be left unset if the user doesn't want to accept anything.
  optional string accept_invitation_id = 1;

  // The invitation ids to decline.
  repeated string decline_invitation_ids = 2;
}

// Empty response on success.
message ResolveInvitationsResponse {
  // Id that can be passed to GetResolveInvitationsStatus to poll for the status of the invitation
  // resolution.
  string invitation_resolution_id = 1;
}

message GetResolveInvitationsStatusRequest {
  // Should come from a previous call to ResolveInvitations.
  string invitation_resolution_id = 1;
}

message GetResolveInvitationsStatusResponse {
  auth_entities.InvitationResolution invitation_resolution = 1;
}

message DeleteInvitationRequest {
  string tenant_id = 1;
  string invitation_id = 2;
}

message DeleteInvitationResponse {}

message GetSubscriptionRequest {
  oneof lookup_id {
    // Look up the subscription by subscription ID. An error will be returned if the caller doesn't
    // have access to this subscription.
    string subscription_id = 1;

    // Look up the subscription by tenant ID. An error will be returned if this doesn't match the
    // tenant ID in the caller's auth token.
    string tenant_id = 2;
  }
}

message GetSubscriptionResponse {
  auth_entities.Subscription subscription = 1;
}

message UpdateSubscriptionRequest {
  // The id of the subscription to update. The caller's auth token must match the tenant ID for this
  // subscription.
  string subscription_id = 1;

  // The number of seats to update the subscription to.
  int32 seats = 2;
}

// Empty response on success.
message UpdateSubscriptionResponse {}

message CreateUserSuspensionRequest {
  string user_id = 1;
  string tenant_id = 2;
  auth_entities.UserSuspensionType suspension_type = 3;
  // Detailed message regarding why this uses needs to be suspended, for audit log.
  string evidence = 4 [debug_redact = true];
}

message CreateUserSuspensionResponse {
  string suspension_id = 1;
  int32 tokens_deleted = 2;
}

message DeleteUserSuspensionsRequest {
  string user_id = 1;
  string tenant_id = 2;
  repeated string suspension_ids = 3;
}

message DeleteUserSuspensionsResponse {
  int32 suspensions_deleted = 1;
}

message UpdateSuspensionExemptionRequest {
  string user_id = 1;
  string tenant_id = 2;
  // When true, prevents new suspensions from being applied.
  bool exempt = 3;
}

message UpdateSuspensionExemptionResponse {
  string user_id = 1;
  string tenant_id = 2;
  bool exempt = 3;
}

message PurchaseCreditsRequest {
  // The requester's Augment user ID and tenant ID
  string user_id = 1;
  string tenant_id = 2;

  // Number of credits to purchase. Must be between 10 and 100, inclusive
  float credits = 3;

  // Optional idempotency key. Can represent a checkout session or be used to make sure
  // that the same purchase is not made twice.
  optional string idempotency_key = 4;
}

message PurchaseCreditsResponse {}

message CancelSubscriptionRequest {
  string user_id = 1;
  string tenant_id = 2;
  bool cancel_immediately = 3;
}

message CancelSubscriptionResponse {}

message ListSubscriptionsRequest {
  // Maximum number of subscriptions to return in a single response.
  // If not specified, a default limit will be used.
  uint32 page_size = 1;

  // A token from a previous response that can be used to continue listing.
  // Leave empty for the first request.
  string page_token = 2;
}

message ListSubscriptionsResponse {
  // The list of subscriptions for the current page
  repeated auth_entities.Subscription subscriptions = 1;

  // Token to retrieve the next page of results, or empty if there are no more results.
  string next_page_token = 2;
}

message ListTenantSubscriptionMappingsRequest {
  // Maximum number of tenant subscription mappings to return in a single response.
  // If not specified, a default limit will be used.
  uint32 page_size = 1;

  // A token from a previous response that can be used to continue listing.
  // Leave empty for the first request.
  string page_token = 2;
}

message ListTenantSubscriptionMappingsResponse {
  // The list of tenant subscription mappings for the current page
  repeated auth_entities.TenantSubscriptionMapping tenant_subscription_mappings = 1;

  // Token to retrieve the next page of results, or empty if there are no more results.
  string next_page_token = 2;
}

message ListUserTenantMappingsRequest {
  // Maximum number of user tenant mappings to return in a single response.
  // If not specified, a default limit will be used.
  uint32 page_size = 1;

  // A token from a previous response that can be used to continue listing.
  // Leave empty for the first request.
  string page_token = 2;
}

message ListUserTenantMappingsResponse {
  // The list of user tenant mappings for the current page
  repeated auth_entities.UserTenantMapping user_tenant_mappings = 1;

  // Token to retrieve the next page of results, or empty if there are no more results.
  string next_page_token = 2;
}

message GetUserOrbInfoRequest {
  string user_id = 1; //the user's Augment user ID
}

message FailedPayment {
  string amount = 1;
  string date = 2;
}

// Times are in RFC3339 format
message GetUserOrbInfoResponse {
  string orb_customer_id = 1;
  string orb_subscription_id = 2;
  string portal_url = 3; //Orb portal URL
  string external_plan_id = 4;
  string billing_period_end = 5;
  bool has_payment_method = 6;
  int32 usage_units_available = 7;
  int32 usage_units_used_this_billing_cycle = 8;
  int32 usage_units_pending = 9;
  int32 usage_units_renewing_each_billing_cycle = 10;
  int32 number_of_seats_this_billing_cycle = 11;
  int32 number_of_seats_next_billing_cycle = 12;
  optional string trial_period_end = 13; //can be null
  optional string subscription_end_date = 14; //can be null
  string next_billing_cycle_amount = 15;
  string usage_unit_name = 16;
  // If the user has a failed payment, information about it to display
  optional FailedPayment failed_payment = 17; //can be null
  enum SubscriptionStatus {
    UNKNOWN = 0;
    UPCOMING = 1;
    ACTIVE = 2;
    ENDED = 3;
  }
  SubscriptionStatus subscription_status = 18;
}

message GetUserOrbPlanInfoRequest {
  string user_id = 1; //the user's Augment user ID
}

message OrbPlanInfo {
  string external_plan_id = 1;
  string formatted_plan_name = 2;
  string price_per_seat = 3;
  string additional_usage_unit_cost = 4;
  bool add_usage_available = 5;
  float usage_units_per_seat = 6;
  bool training_allowed = 7;
  bool teams_allowed = 8;
  int32 max_num_seats = 9;
  string usage_unit_name = 10;
}

message GetUserOrbPlanInfoResponse {
  OrbPlanInfo orb_plan_info = 1;
}

message GetAllOrbPlansRequest {}

message GetAllOrbPlansResponse {
  repeated OrbPlanInfo orb_plans = 1;
}

message UnschedulePendingSubscriptionCancellationRequest {
  string user_id = 1; //augment user ID
}

message UnschedulePendingSubscriptionCancellationResponse {}

message UnschedulePlanChangesRequest {
  string user_id = 1; //augment user ID
}

message UnschedulePlanChangesResponse {}

message PutUserOnPlanRequest {
  // The user ID to put on a plan
  string user_id = 1;

  // TODO: deprecate the plan enum when plan_id is used
  enum Plan {
    UNKNOWN_PLAN = 0;
    COMMUNITY = 1;
    DEVELOPER = 2;
  }
  // The plan to put the user on
  Plan plan = 2;

  // The plan ID in Orb to associate with the user
  string plan_id = 3;
}

// Empty response on success as this is an async operation
message PutUserOnPlanResponse {}

message GetUsersRequest {
  // String to search for in the user's record. If blank, matches all users
  string search_string = 1 [debug_redact = true];

  // Maximum number of users to return in a single response.
  // If not specified, a default limit will be used.
  uint32 page_size = 2;

  // A token from a previous response that can be used to continue listing.
  // Leave empty for the first request.
  string page_token = 3;
}

message GetUsersResponse {
  // The users that match the search criteria
  repeated auth_entities.User users = 1;

  // Token to retrieve the next page of results, or empty if there are no more results.
  string next_page_token = 2;
}

message MigrateUserBlocksToSuspensionsRequest {}

message MigrateUserBlocksToSuspensionsResponse {
  int32 users_migrated = 1;
  int32 users_failed = 2;
}

message GetUserOrbCreditsInfoRequest {
  string user_id = 1;
}

message GetUserOrbCreditsInfoResponse {
  int32 usage_units_available = 1;
  int32 usage_units_used_this_billing_cycle = 2;
  int32 usage_units_pending = 3;
}

message GetUserOrbPaymentInfoRequest {
  string user_id = 1;
}

message GetUserOrbPaymentInfoResponse {
  bool has_payment_method = 1; //whether the user has a payment method saved
  optional FailedPayment failed_payment = 2; //can be null. change to list??
}

message GetUserOrbSubscriptionInfoRequest {
  string user_id = 1;
}

message OrbSubscriptionInfo {
  string orb_customer_id = 1;
  string orb_subscription_id = 2;
  string portal_url = 3; // orb portal URL for showing additional invoices, etc
  string external_plan_id = 4;
  enum SubscriptionStatus {
    UNKNOWN = 0;
    UPCOMING = 1;
    ACTIVE = 2;
    ENDED = 3;
  }
  SubscriptionStatus subscription_status = 5;

  int32 usage_units_renewing_each_billing_cycle = 6; // usage units renewing each billing cycle. 0 if scheduled to end at billing cycle end
  int32 usage_units_included_this_billing_cycle = 7; // number of usage units on the current plan
  int32 seats = 8;

  string monthly_total_cost = 9; // how much they are paying currently
  string next_billing_cycle_amount = 10; // how much they will be charged at the next billing cycle

  string billing_period_end_date_iso = 11;
  optional string trial_period_end_date_iso = 12; // null if not on trial
  optional string subscription_end_date_iso = 13; // null if no end date
  bool subscription_end_at_billing_cycle_end = 14; //true if subscription will end at current billing cycle end

  optional string scheduled_target_plan_id = 15; // null if no scheduled plan change
  bool cancelled_due_to_payment_failure = 16; // true if subscription was cancelled due to payment failure
}

message PendingOrbSubscription {}

message NoSubscription {}

message GetUserOrbSubscriptionInfoResponse {
  oneof orb_subscription_info {
    OrbSubscriptionInfo subscription = 1;
    PendingOrbSubscription pending_subscription = 2;
    NoSubscription nonexistent_subscription = 3;
  }
}

message SuspensionCleanupRequest {
  // Remove all suspensions of the indicated types
  repeated auth_entities.UserSuspensionType remove_suspension_types = 1;
  // Dedup suspensions of the indicated types, keeping only the oldest suspension
  repeated auth_entities.UserSuspensionType dedup_suspension_types = 2;
}

message SuspensionCleanupResponse {
  // Check logs for results. Job may run longer than RPC timeout.
  int32 suspensions_removed = 1;
  int32 suspensions_deduped = 2;
  int32 updates_failed = 3;
}

message GetTenantPlanStatusRequest {
  string tenant_id = 1;
}

message GetTenantPlanStatusResponse {
  bool is_pending = 1;
  // The target Orb plan ID if a change is pending
  optional string pending_target_plan_id = 2;
  // Timestamp of when the plan change was initiated, if pending
  optional google.protobuf.Timestamp plan_change_created_at = 3;
}

message SetUsersBillingMethodToOrbRequest {
  bool make_changes = 1; // If false, just report what would be changed without making changes
}

message SetUsersBillingMethodToOrbResponse {
  repeated string updated_user_ids = 1; // User IDs whose billing method was updated or would be updated
  repeated string failed_user_ids = 2; // User IDs that failed to update
}
