package main

import (
	"context"
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"time"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging/audit"
	"github.com/augmentcode/augment/services/integrations/orb"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	ripb "github.com/augmentcode/augment/services/request_insight/proto"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	tw_pb "github.com/augmentcode/augment/services/tenant_watcher/proto"
	tenantutil "github.com/augmentcode/augment/services/tenant_watcher/util"
	tokenexchangepb "github.com/augmentcode/augment/services/token_exchange/proto"
	"github.com/google/uuid"
	"github.com/rs/zerolog/log"
	"golang.org/x/sync/errgroup"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	authpb "github.com/augmentcode/augment/services/auth/central/server/auth"
	"github.com/augmentcode/augment/services/auth/central/server/auth_entities"
	"github.com/augmentcode/augment/services/auth/central/server/auth_internal"
)

// RandomSelector interface for dependency injection of random number generation
type RandomSelector interface {
	Intn(n int) int
}

// DefaultRandomSelector implements RandomSelector using math/rand
type DefaultRandomSelector struct{}

func (d *DefaultRandomSelector) Intn(n int) int {
	return rand.Intn(n)
}

// If false, disable all team mangement endpoints. This is intended to be a quick off switch for
// emergencies; feature rollout should be handled by frontend flags.
var teamManagementEnabled = featureflags.NewBoolFlag("auth_central_team_management_enabled", true)

// If false, InviteUsersToTenant requests will be rejected if any email contains a +.
var inviteUsersToTenantPlusAllowed = featureflags.NewBoolFlag("auth_central_invite_users_to_tenant_plus_allowed", false)

type TeamManagementServer struct {
	daoFactory              *DAOFactory
	tenantMap               *TenantMap
	auditLogger             *audit.AuditLogger
	requestInsightPublisher ripublisher.RequestInsightPublisher
	featureFlagHandle       featureflags.FeatureFlagHandle
	asyncOpsPublisher       AsyncOpsPublisher
	stripeClient            StripeClient
	orbConfig               *OrbConfig
	orbClient               orb.OrbClient
	randomSelector          RandomSelector
}

func NewTeamManagementServer(
	featureFlagHandle featureflags.FeatureFlagHandle,
	daoFactory *DAOFactory,
	tenantMap *TenantMap,
	auditLogger *audit.AuditLogger,
	requestInsightPublisher ripublisher.RequestInsightPublisher,
	asyncOpsPublisher AsyncOpsPublisher,
	stripeClient StripeClient,
	orbConfig *OrbConfig,
	orbClient orb.OrbClient,
	randomSelector RandomSelector,
) *TeamManagementServer {
	// Use default random selector if none provided
	if randomSelector == nil {
		randomSelector = &DefaultRandomSelector{}
	}

	return &TeamManagementServer{
		daoFactory:              daoFactory,
		tenantMap:               tenantMap,
		auditLogger:             auditLogger,
		requestInsightPublisher: requestInsightPublisher,
		featureFlagHandle:       featureFlagHandle,
		asyncOpsPublisher:       asyncOpsPublisher,
		stripeClient:            stripeClient,
		orbConfig:               orbConfig,
		orbClient:               orbClient,
		randomSelector:          randomSelector,
	}
}

// Returns an error that can be returned directly to the caller if provided gRPC context does not
// contain auth claims that give access to the provided fields. Leave tenantID nil for requests that
// don't require tenant validation and userID nil for requests that don't require user validation.
func (s *TeamManagementServer) teamManagementAuthCheck(
	ctx context.Context,
	tenantID *string,
	userID *string,
	requiredScope tokenexchangepb.Scope,
	adminOnly bool,
	apiName string,
) error {
	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		log.Error().Msgf("Failed to get auth claims from context")
		return status.Error(codes.PermissionDenied, "Invalid context")
	}

	if !authClaims.HasScope(requiredScope) {
		log.Error().Msgf("Auth claims do not give have scope %s", requiredScope)
		return status.Error(codes.PermissionDenied, "Access denied")
	}

	// Internal IAP users can access the APIs for testing purposes
	iapEmail, isIapUser := authClaims.GetIapEmail()
	if isIapUser {
		s.auditLogger.WriteAuditLog(
			iapEmail,
			authClaims.OpaqueUserIDType,
			authClaims.TenantName,
			fmt.Sprintf("IAP user %s accessing team management API %s in tenant %s", iapEmail, apiName, authClaims.TenantName),
		)
	}

	if tenantID != nil {
		// Validate that the tenant from the request matches the tenant from the auth claims.
		if *tenantID == "" {
			if !authClaims.AllowsAllTenants() {
				log.Error().Msgf("Auth claims give permission for tenant %s, but request requires access to all tenants", authClaims.TenantID)
				return status.Error(codes.PermissionDenied, "Access denied")
			}
		} else if !authClaims.IsTenantAllowed(*tenantID) {
			log.Error().Msgf("Auth claims give permission for tenant %s, but request has tenant %s", authClaims.TenantID, *tenantID)
			return status.Error(codes.PermissionDenied, "Access denied")
		}

		// Legacy self-serve teams are not supported
		tenant, err := s.tenantMap.GetTenantByID(*tenantID)
		if err != nil {
			log.Error().Err(err).Msgf("Failed to get tenant %s", *tenantID)
			return status.Error(codes.Internal, "Failed to get tenant")
		}
		if tenant != nil && tenantutil.IsLegacySelfServeTeamTenant(tenant) {
			log.Error().Msgf("Tenant %s is a legacy self-serve team", *tenantID)
			return status.Error(codes.PermissionDenied, "Access denied")
		}
	}

	if userID != nil {
		if isIapUser {
			log.Info().Msgf("Skipping user validation for IAP user %s", iapEmail)
		} else {
			// Validate that the user from the request matches the user from the auth claims.
			claimsAugmentUserID := augmentUserID(authClaims)
			if claimsAugmentUserID == "" {
				log.Error().Msgf("Auth claims do not have an AUGMENT user ID")
				return status.Error(codes.PermissionDenied, "Access denied")
			}

			if *userID == "" {
				log.Error().Msgf("Invalid (empty) user ID")
				return status.Error(codes.InvalidArgument, "Access denied")
			} else if *userID != claimsAugmentUserID {
				log.Error().Msgf(
					"Auth claims user ID %s does not match requested user ID %s",
					claimsAugmentUserID, *userID)
				return status.Error(codes.PermissionDenied, "Access denied")
			}
		}
	}

	if adminOnly {
		if isIapUser {
			log.Info().Msgf("Skipping admin validation for IAP user %s", iapEmail)
		} else {
			augmentUserID := augmentUserID(authClaims)
			if augmentUserID == "" {
				log.Error().Msgf("Auth claims do not have an AUGMENT user ID")
				return status.Error(codes.PermissionDenied, "Access denied")
			}
			if authClaims.TenantName == "" {
				log.Error().Msgf("Auth claims do not have a tenant name")
				return status.Error(codes.PermissionDenied, "Access denied")
			}
			tenantMappingDAO := s.daoFactory.GetUserTenantMappingDAO(authClaims.TenantName)
			mapping, err := tenantMappingDAO.GetByUser(ctx, augmentUserID)
			if err != nil {
				log.Error().Err(err).Msgf("Failed to get user mapping")
				return status.Error(codes.Internal, "Failed to get user mapping")
			} else if mapping == nil {
				log.Error().Msgf("User %s is not in tenant %s", augmentUserID, authClaims.TenantID)
				return status.Error(codes.PermissionDenied, "Access denied")
			}

			isAdmin := false
			roles := mapping.CustomerUiRoles
			for _, role := range roles {
				if role == auth_entities.CustomerUiRole_ADMIN {
					isAdmin = true
					break
				}
			}
			if !isAdmin {
				log.Error().Msgf("User %s is not an admin", augmentUserID)
				return status.Error(codes.PermissionDenied, "Access denied")
			}
		}
	}

	return nil
}

// teamManagementWildcardAuthCheck performs auth checks for endpoints that require wildcard tenant access.
// This is a specialized version of teamManagementAuthCheck for operations that need to access all tenants.
func (s *TeamManagementServer) teamManagementWildcardAuthCheck(
	ctx context.Context,
	requiredScope tokenexchangepb.Scope,
	apiName string,
) error {
	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		log.Error().Msgf("Failed to get auth claims from context")
		return status.Error(codes.PermissionDenied, "Invalid context")
	}

	// Check that the token has the required scope
	if !authClaims.HasScope(requiredScope) {
		log.Error().Msgf("Auth claims do not have required scope %s for %s", requiredScope, apiName)
		return status.Error(codes.PermissionDenied, "Access denied")
	}

	// Check that the token allows access to all tenants
	if !authClaims.AllowsAllTenants() {
		log.Error().Msgf("Auth claims give permission for tenant %s, but %s requires access to all tenants", authClaims.TenantID, apiName)
		return status.Error(codes.PermissionDenied, "Access denied")
	}

	return nil
}

// Get the AUGMENT user ID from auth claims, if any. Returns the empty string if the given claims
// don't have an AUGMENT user ID.
func augmentUserID(authClaims *auth.AugmentClaims) string {
	claimsOpaqueUserID := authClaims.GetOpaqueUserID()
	if claimsOpaqueUserID == nil {
		return ""
	} else if claimsOpaqueUserID.UserIdType != auth_entities.UserId_AUGMENT {
		return ""
	} else {
		return claimsOpaqueUserID.UserId
	}
}

func (s *TeamManagementServer) teamManagementEnabled() bool {
	enabled, err := teamManagementEnabled.Get(s.featureFlagHandle)
	if err != nil {
		log.Warn().Err(err).Msgf("Failed to get teamManagementEnabled feature flag, defaulting to true")
		enabled = true
	}
	if !enabled {
		log.Warn().Msgf("Team management is disabled")
	}
	return enabled
}

func (s *TeamManagementServer) WriteTeamsAuditLog(ctx context.Context, message string) {
	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		log.Error().Msg("Failed to get auth claims")
		return
	}
	iapEmail, ok := authInfo.GetIapEmail()
	if ok {
		s.auditLogger.WriteAuditLog(
			iapEmail,
			authInfo.OpaqueUserIDType,
			authInfo.TenantName,
			fmt.Sprintf("IAP user %s %s", iapEmail, message),
		)
	} else {
		s.auditLogger.WriteAuditLog(
			authInfo.UserID,
			authInfo.OpaqueUserIDType,
			authInfo.TenantName,
			fmt.Sprintf("User %s %s", authInfo.UserID, message),
		)
	}
}

func isAllowedToCreateTeamTenant(user *auth_entities.User, tenantMap *TenantMap, targetTenantID string) error {
	// Check if user has a tier change in progress
	if user.TierChange != nil {
		log.Error().Msgf("User %s has a tier change in progress", user.Id)
		return status.Error(codes.FailedPrecondition, "User has a tier change in progress")
	}

	// Check if user belongs to at least one tenant
	if len(user.Tenants) == 0 {
		log.Error().Msgf("User %s does not belong to any tenant", user.Id)
		return status.Error(codes.FailedPrecondition, "User does not belong to any tenant")
	}

	// Check if user belongs to any enterprise, community, or self-serve team tenants
	for _, tenantId := range user.Tenants {
		if tenantId == targetTenantID {
			// The target tenant is the one we're creating and adding the user to, so skip it
			// This can happen due to retrying the request after failures
			log.Info().Msgf("Skip checking target tenant %s in user's tenants list", targetTenantID)
			continue
		}
		tenant, err := tenantMap.GetTenantByID(tenantId)
		if err != nil || tenant == nil {
			log.Error().Err(err).Msgf("Failed to get tenant by id %s", tenantId)
			return status.Error(codes.Internal, "Failed to get tenant by id")
		}
		if tenantutil.IsEnterpriseTenant(tenant) || tenantutil.IsCommunityTenant(tenant) {
			log.Error().Msgf("User %s belongs to a %s tenant: %s", user.Id, tenant.Tier, tenant.Name)
			return status.Error(codes.FailedPrecondition, "User belongs to an enterprise tenant")
		}
		if tenantutil.IsSelfServeTeamTenant(tenant) {
			log.Error().Msgf("User %s belongs to a self-serve team tenant: %s", user.Id, tenant.Name)
			if tenant.Config != nil && tenant.Config.Configs != nil {
				log.Debug().Msgf("Tenant Configs: %v", tenant.Config.Configs)
			}
			return status.Error(codes.FailedPrecondition, "User belongs to a self-serve team tenant")
		}
	}

	return nil
}

func (s *TeamManagementServer) CreateTenantForTeam(
	ctx context.Context, req *authpb.CreateTenantForTeamRequest,
) (*authpb.CreateTenantForTeamResponse, error) {
	if !s.teamManagementEnabled() {
		log.Warn().Msgf("Team management is disabled for CreateTenantForTeam")
		return nil, status.Error(codes.Internal, "Disabled")
	}

	err := s.teamManagementAuthCheck(ctx, nil, &req.AdminUserId, tokenexchangepb.Scope_AUTH_RW, false, "CreateTenantForTeam")
	if err != nil {
		return nil, err
	}

	// Get the user to check if they're eligible for team creation
	userDAO := s.daoFactory.GetUserDAO()
	user, err := userDAO.Get(ctx, req.AdminUserId)
	if err != nil {
		log.Error().Err(err).Msgf("Failed to get user %s", req.AdminUserId)
		return nil, status.Error(codes.Internal, "Failed to get user information")
	}
	if user == nil {
		log.Error().Msgf("User %s not found", req.AdminUserId)
		return nil, status.Error(codes.NotFound, "User not found")
	}

	err = isAllowedToCreateTeamTenant(user, s.tenantMap, "")
	if err != nil {
		return nil, err
	}

	// Make sure this user has an orb subscription. Their subscription will be moved to be associated
	// with the team tenant.
	if user.OrbSubscriptionId == "" {
		log.Error().Msgf("CreateTenantForTeam: User %s does not have an Orb subscription", user.Id)
		return nil, status.Error(codes.FailedPrecondition, "User does not have an Orb subscription")
	}
	subscriptionDAO := s.daoFactory.GetSubscriptionDAO()
	subscription, err := subscriptionDAO.Get(ctx, user.OrbSubscriptionId)
	if err != nil {
		log.Error().Err(err).Msgf(
			"CreateTenantForTeam: Failed to get Orb subscription %s", user.OrbSubscriptionId)
		return nil, status.Error(codes.Internal, "Failed to get subscription")
	} else if subscription == nil {
		log.Error().Msgf("CreateTenantForTeam: Orb subscription %s not found", user.OrbSubscriptionId)
		return nil, status.Error(codes.FailedPrecondition, "Subscription not found")
	}

	// Generate a unique tenant creation ID
	tenantCreationID := uuid.New().String()
	log.Info().Msgf("Creating tenant creation request %s for user %s", tenantCreationID, req.AdminUserId)

	// Audit Log
	s.WriteTeamsAuditLog(ctx, fmt.Sprintf("creating team tenant for admin user %s", req.AdminUserId))

	// Create and publish the tenant creation message for async processing
	tenantCreationMsg := &auth_internal.CreateTenantForTeamMessage{
		TenantCreationId: tenantCreationID,
		TenantCreationRequest: &authpb.CreateTenantForTeamRequest{
			AdminUserId: req.AdminUserId,
		},
		PublishTime: timestamppb.Now(),
	}

	err = s.asyncOpsPublisher.PublishTenantCreation(ctx, tenantCreationMsg)
	if err != nil {
		log.Error().Err(err).Msgf("Failed to publish tenant creation message for id %s", tenantCreationID)
		return nil, status.Error(codes.Internal, "Failed to publish tenant creation message")
	}

	// Create and save a new tenant creation record to the internal database
	tenantCreationDAO := s.daoFactory.GetTenantCreationDAO()
	tenantCreation := &auth_entities.TenantCreation{
		Id:        tenantCreationID,
		CreatedAt: timestamppb.Now(),
		Status:    auth_entities.TenantCreation_PENDING,
	}

	_, err = tenantCreationDAO.Create(ctx, tenantCreation)
	if err != nil {
		log.Error().Err(err).Msgf("Failed to create tenant creation record %s", tenantCreationID)
		return nil, status.Error(codes.Internal, "Failed to create tenant creation record")
	}

	log.Info().Msgf("Created tenant creation request %s for user %s", tenantCreationID, req.AdminUserId)

	return &authpb.CreateTenantForTeamResponse{
		TenantCreationId: tenantCreationID,
	}, nil
}

func (s *TeamManagementServer) GetCreateTenantForTeamStatus(
	ctx context.Context, req *authpb.GetCreateTenantForTeamStatusRequest,
) (*authpb.GetCreateTenantForTeamStatusResponse, error) {
	if !s.teamManagementEnabled() {
		log.Warn().Msgf("Team management is disabled for GetCreateTenantForTeamStatus")
		return nil, status.Error(codes.Internal, "Disabled")
	}

	err := s.teamManagementAuthCheck(ctx, nil, nil, tokenexchangepb.Scope_AUTH_R, false, "GetCreateTenantForTeamStatus")
	if err != nil {
		return nil, err
	}

	log.Info().Msgf("Getting status for tenant creation request %s", req.TenantCreationId)

	tenantCreationDAO := s.daoFactory.GetTenantCreationDAO()
	tenantCreation, err := tenantCreationDAO.Get(ctx, req.TenantCreationId)
	if err != nil {
		log.Error().Err(err).Msgf("Failed to get tenant creation record %s", req.TenantCreationId)
		return nil, status.Error(codes.Internal, "Failed to get tenant creation record")
	}
	if tenantCreation == nil {
		log.Error().Msgf("Tenant creation record %s not found", req.TenantCreationId)
		return nil, status.Error(codes.NotFound, "Tenant creation record not found")
	}

	log.Info().Msgf("Got status for tenant creation request %s: %v", req.TenantCreationId, tenantCreation)

	return &authpb.GetCreateTenantForTeamStatusResponse{
		TenantCreation: tenantCreation,
	}, nil
}

func (s *TeamManagementServer) InviteUsersToTenant(
	ctx context.Context, req *authpb.InviteUsersToTenantRequest,
) (*authpb.InviteUsersToTenantResponse, error) {
	if !s.teamManagementEnabled() {
		return nil, status.Error(codes.Internal, "Disabled")
	}

	err := s.teamManagementAuthCheck(ctx, &req.TenantId, nil, tokenexchangepb.Scope_AUTH_RW, false, "InviteUsersToTenant")
	if err != nil {
		return nil, err
	}

	// Audit Log
	s.WriteTeamsAuditLog(ctx, fmt.Sprintf("inviting users %v to tenant %s", req.InviteeEmails, req.TenantId))

	results := make(
		[]*authpb.InviteUsersToTenantResponse_InvitationCreationStatus, 0, len(req.InviteeEmails),
	)

	// Restrict this endpoint to self-serve team tenants.
	tenant, err := s.tenantMap.GetTenantByID(req.TenantId)
	if err != nil {
		log.Error().Err(err).Msgf("Failed to get tenant %s", req.TenantId)
		return nil, status.Error(codes.Internal, "Failed to get tenant")
	} else if tenant == nil {
		log.Error().Msgf("Tenant %s not found", req.TenantId)
		return nil, status.Error(codes.NotFound, "Tenant not found")
	} else if !tenantutil.IsSelfServeTeamTenant(tenant) {
		log.Error().Msgf("Tenant %s is not a self-serve team tenant. Cannot invite users", req.TenantId)
		return nil, status.Error(codes.FailedPrecondition, "Invitations not permitted to this tenant")
	}

	// Auth claims have already been checked above, so we know this parsing will succeed.
	authInfo, _ := auth.GetAugmentClaims(ctx)
	inviterUserID := authInfo.GetOpaqueUserID().GetUserId()
	inviterEmail := authInfo.UserEmail
	if inviterEmail == "" {
		log.Error().Msg("Failed to get inviter email from auth claims")
		return nil, status.Error(codes.FailedPrecondition, "Failed to get inviter email")
	}

	// TEMPORARY HACK: Disallow inviting emails with +.
	// TODO(jacqueline): This should really be checked at signup but currently we aren't doing the
	// similar email checks in the invitation flow.
	plusAllowed, _ := inviteUsersToTenantPlusAllowed.Get(s.featureFlagHandle)
	if !plusAllowed {
		for _, email := range req.InviteeEmails {
			if strings.Contains(email, "+") {
				log.Error().Msgf("Disallowing invite for email %s to tenant %s due to +", email)
				return nil, status.Error(codes.InvalidArgument, "Email contains +")
			}
		}
	}

	// Deduplicate and normalize emails from the request.
	deduplicatedEmails := make(map[string]struct{})
	for _, email := range req.InviteeEmails {
		normalizedEmail, err := normalizeEmail(email)
		if err != nil {
			log.Error().Err(err).Msgf("Failed to normalize email %s", email)
			results = append(
				results,
				&authpb.InviteUsersToTenantResponse_InvitationCreationStatus{
					Email:  email,
					Status: authpb.InviteUsersToTenantResponse_InvitationCreationStatus_ERROR,
				},
			)
			continue
		}
		deduplicatedEmails[normalizedEmail] = struct{}{}
	}

	// Get the list of all emails with pending invitations for this tenant, to avoid creating
	// duplicate invitations. Note that there's a race here where it's still possible to end up with
	// duplicates. Jacqueline is choosing to ignore this for now because the duplicates should be
	// harmless and this will be easy to solve once we've migrated to a relational database.
	pendingInvitations, err := GetInvitationsForTenant(
		ctx, s.daoFactory, req.TenantId, auth_entities.TenantInvitation_PENDING,
	)
	if err != nil {
		log.Error().Err(err).Msg("Failed to fetch invitations")
		return nil, status.Error(codes.Internal, "Failed to fetch invitations")
	}
	pendingInvitationsByEmail := make(map[string]*auth_entities.TenantInvitation)
	for _, invitation := range pendingInvitations {
		pendingInvitationsByEmail[invitation.InviteeEmail] = invitation
	}

	// Check if the tenant has sufficient seats available for non-admin callers.
	// For admin callers we will add seats in parallel and have decided that the (low) risk of
	// over-inviting is acceptable.
	isAdmin := false
	augmentUserID := augmentUserID(authInfo)
	if augmentUserID != "" {
		tenantMappingDAO := s.daoFactory.GetUserTenantMappingDAO(tenant.Name)
		mapping, err := tenantMappingDAO.GetByUser(ctx, augmentUserID)
		if err != nil {
			log.Error().Err(err).Msgf("Failed to get user mapping")
			return nil, status.Error(codes.Internal, "Failed to get user mapping")
		} else if mapping != nil {
			for _, role := range mapping.CustomerUiRoles {
				if role == auth_entities.CustomerUiRole_ADMIN {
					isAdmin = true
					break
				}
			}
		}
	}

	// Skip seat validation for admin users since we don't want to add friction for adding users.
	// Only enforce seat limits for non-admin users to prevent them from exceeding the team's paid capacity.
	if !isAdmin {
		// Count new invitations (excluding ones that already exist)
		newInvitationCount := 0
		for email := range deduplicatedEmails {
			if _, exists := pendingInvitationsByEmail[email]; !exists {
				newInvitationCount++
			}
		}

		// Get the subscription for the tenant
		tenantSubscriptionMappingDAO := s.daoFactory.GetTenantSubscriptionMappingDAO()
		subscriptionMapping, err := tenantSubscriptionMappingDAO.Get(ctx, req.TenantId)
		if err != nil {
			log.Error().Err(err).Msg("Failed to fetch subscription mapping")
			return nil, status.Error(codes.Internal, "Failed to fetch subscription mapping")
		}

		if subscriptionMapping == nil {
			log.Error().Str("tenant_id", req.TenantId).Msg("Subscription not found for tenant")
			return nil, status.Error(codes.NotFound, "Subscription not found for tenant")
		}

		// Get the subscription to check its seat limit
		subscriptionDAO := s.daoFactory.GetSubscriptionDAO()
		subscription, err := subscriptionDAO.Get(ctx, subscriptionMapping.OrbSubscriptionId)
		if err != nil {
			log.Error().Err(err).Msg("Failed to fetch subscription")
			return nil, status.Error(codes.Internal, "Failed to fetch subscription")
		}

		if subscription == nil {
			log.Error().Str("subscription_id", subscriptionMapping.OrbSubscriptionId).Msg("Subscription not found")
			return nil, status.Error(codes.NotFound, "Subscription not found")
		}

		// Validate that there are enough seats available
		err = s.ValidateSubscriptionSeats(ctx, req.TenantId, subscription.Seats, newInvitationCount)
		if err != nil {
			return nil, err
		}
	}

	tenantInvitationDAO := s.daoFactory.GetTenantInvitationDAO(req.TenantId)
	for inviteeEmail := range deduplicatedEmails {
		// If the email is already invited just respond with "success".
		if existingInvitation, ok := pendingInvitationsByEmail[inviteeEmail]; ok {
			results = append(
				results,
				&authpb.InviteUsersToTenantResponse_InvitationCreationStatus{
					Email:        inviteeEmail,
					Status:       authpb.InviteUsersToTenantResponse_InvitationCreationStatus_SUCCESS,
					InvitationId: existingInvitation.Id,
				},
			)
			continue
		}

		// Check if the inviteeEmail is associated with an enterprise tenant.
		tenant, err := s.tenantMap.GetTenantForEmailDomain(inviteeEmail)
		if err != nil {
			log.Error().Err(err).Msgf("Failed to check if email is associated with enterprise tenant")
			results = append(
				results,
				&authpb.InviteUsersToTenantResponse_InvitationCreationStatus{
					Email:  inviteeEmail,
					Status: authpb.InviteUsersToTenantResponse_InvitationCreationStatus_ERROR,
				},
			)
			continue
		}

		// If the email is associated with an enterprise tenant, we can't invite them
		if tenant != nil && tenantutil.IsEnterpriseTenant(tenant) {
			log.Error().Msgf("Email %s is associated with enterprise tenant %s", inviteeEmail, tenant.Name)
			results = append(
				results,
				&authpb.InviteUsersToTenantResponse_InvitationCreationStatus{
					Email:  inviteeEmail,
					Status: authpb.InviteUsersToTenantResponse_InvitationCreationStatus_ERROR,
				},
			)
			continue
		}

		// Create the invitation.
		invitation := &auth_entities.TenantInvitation{
			Id:            uuid.New().String(),
			CreatedAt:     timestamppb.Now(),
			InviteeEmail:  inviteeEmail,
			TenantId:      req.TenantId,
			InviterUserId: inviterUserID,
			InviterEmail:  inviterEmail,
			Status:        auth_entities.TenantInvitation_PENDING,
		}
		_, err = tenantInvitationDAO.Create(ctx, invitation)
		var status authpb.InviteUsersToTenantResponse_InvitationCreationStatus_Status
		var invitationID string
		if err != nil {
			log.Error().Err(err).Msgf("Failed to invite %s to %s", inviteeEmail, req.TenantId)
			status = authpb.InviteUsersToTenantResponse_InvitationCreationStatus_ERROR
		} else {
			log.Info().Msgf("Invited %s to %s", inviteeEmail, req.TenantId)
			status = authpb.InviteUsersToTenantResponse_InvitationCreationStatus_SUCCESS
			invitationID = invitation.Id
		}
		results = append(
			results,
			&authpb.InviteUsersToTenantResponse_InvitationCreationStatus{
				Email:        inviteeEmail,
				Status:       status,
				InvitationId: invitationID,
			},
		)

		// Pulish this invitation to request insight.
		event := ripublisher.NewTenantEvent()
		event.Event = &ripb.TenantEvent_InviteUserToTenant{
			InviteUserToTenant: &ripb.InviteUserToTenant{
				Invitation: invitation,
			},
		}
		riErr := s.requestInsightPublisher.PublishTenantEvent(ctx, &ripb.TenantInfo{
			TenantId:   req.TenantId,
			TenantName: authInfo.TenantName,
		}, event)
		if riErr != nil {
			log.Warn().Err(riErr).Msg("Failed to publish InviteUserToTenant event")
		}
	}

	emailsInvited := []string{}
	for _, result := range results {
		if result.Status == authpb.InviteUsersToTenantResponse_InvitationCreationStatus_SUCCESS {
			emailsInvited = append(emailsInvited, result.Email)

			// Publish an async message for sending theinvitation email
			s.asyncOpsPublisher.PublishSendInvitationEmail(ctx, &auth_internal.SendInvitationEmailMessage{
				TenantId:     req.TenantId,
				InvitationId: result.InvitationId,
				InviteeEmail: result.Email,
				PublishTime:  timestamppb.Now(),
			})
		}
	}
	s.WriteTeamsAuditLog(ctx, fmt.Sprintf("successfully invited users %v to tenant %s", emailsInvited, req.TenantId))

	return &authpb.InviteUsersToTenantResponse{
		InvitationStatuses: results,
	}, nil
}

func (s *TeamManagementServer) GetTenantInvitations(
	ctx context.Context, req *authpb.GetTenantInvitationsRequest,
) (*authpb.GetTenantInvitationsResponse, error) {
	if !s.teamManagementEnabled() {
		return nil, status.Error(codes.Internal, "Disabled")
	}

	err := s.teamManagementAuthCheck(ctx, &req.TenantId, nil, tokenexchangepb.Scope_AUTH_R, false, "GetTenantInvitations")
	if err != nil {
		return nil, err
	}

	invitations, err := GetInvitationsForTenant(
		ctx, s.daoFactory, req.TenantId, auth_entities.TenantInvitation_PENDING,
	)
	if err != nil {
		log.Error().Err(err).Msg("Failed to fetch invitations")
		return nil, status.Error(codes.Internal, "Failed to fetch invitations")
	}

	return &authpb.GetTenantInvitationsResponse{
		Invitations: invitations,
	}, nil
}

func (s *TeamManagementServer) GetUserInvitations(
	ctx context.Context, req *authpb.GetUserInvitationsRequest,
) (*authpb.GetUserInvitationsResponse, error) {
	// At the point in the login flow when this endpoint is called we don't have a user id. Instead of
	// validating a user id we will filter for the correct email in application logic.
	err := s.teamManagementAuthCheck(ctx, nil, nil, tokenexchangepb.Scope_AUTH_R, false, "GetUserInvitations")
	if err != nil {
		return nil, err
	}
	return s.getUserInvitations(ctx, req)
}

// Application logic for the GetUserInvitations endpoint, pulled out so that it can be used by
// both TeamManagementService and FrontEndTokenService. This does NOT perform any authorization
// checks; callers must do that themselves.
func (s *TeamManagementServer) getUserInvitations(
	ctx context.Context, req *authpb.GetUserInvitationsRequest,
) (*authpb.GetUserInvitationsResponse, error) {
	if !s.teamManagementEnabled() {
		return nil, status.Error(codes.Internal, "Disabled")
	}

	normalizedEmail, err := normalizeEmail(req.Email)
	if err != nil {
		log.Error().Err(err).Msgf("Failed to normalize email %s", req.Email)
		return nil, status.Error(codes.InvalidArgument, "Failed to normalize email")
	}

	// Listing all the invitations for an email requires a table scan.
	tenantInvitationDAO := s.daoFactory.GetTenantInvitationDAO("")

	// Look for PENDING invitations with a matching normalized email.
	invitations := make([]*auth_entities.TenantInvitation, 0)

	err = tenantInvitationDAO.ListInvitationsForAllTenants(ctx, func(invitation *auth_entities.TenantInvitation) bool {
		normalizedInvitationEmail, err := normalizeEmail(invitation.InviteeEmail)
		if err != nil {
			log.Error().Err(err).Msgf(
				"Failed to normalize email %s for invitation %s", invitation.InviteeEmail, invitation.Id)
			return true
		}

		if invitation.Status == auth_entities.TenantInvitation_PENDING &&
			normalizedInvitationEmail == normalizedEmail {
			invitations = append(invitations, invitation)
		}
		return true
	})
	if err != nil {
		log.Error().Err(err).Msg("Failed to fetch invitations")
		return nil, status.Error(codes.Internal, "Failed to fetch invitations")
	}

	log.Info().Msgf("Found %d matching invitations for email %s", len(invitations), req.Email)
	return &authpb.GetUserInvitationsResponse{
		Invitations: invitations,
	}, nil
}

func (s *TeamManagementServer) ResolveInvitations(
	ctx context.Context, req *authpb.ResolveInvitationsRequest,
) (*authpb.ResolveInvitationsResponse, error) {
	err := s.teamManagementAuthCheck(ctx, nil, nil, tokenexchangepb.Scope_AUTH_RW, false, "ResolveInvitations")
	if err != nil {
		return nil, err
	}
	return s.resolveInvitations(ctx, req)
}

// Application logic for the ResolveInvitations endpoint, pulled out so that it can be used by
// both TeamManagementService and FrontEndTokenService. This does NOT perform any authorization
// checks; callers must do that themselves.
func (s *TeamManagementServer) resolveInvitations(
	ctx context.Context, req *authpb.ResolveInvitationsRequest,
) (*authpb.ResolveInvitationsResponse, error) {
	if !s.teamManagementEnabled() {
		return nil, status.Error(codes.Internal, "Disabled")
	}

	var acceptId string
	if req.AcceptInvitationId != nil {
		acceptId = *req.AcceptInvitationId
	}
	s.WriteTeamsAuditLog(ctx, fmt.Sprintf("accepting invitation %s and declining invitations %v", acceptId, req.DeclineInvitationIds))

	// TODO(jacqueline): Think through races (e.g., invitation resolutions for the same email and
	// enterprise tenants associated with this email created after the invitation creation). I think
	// it's largely safe to ignore races here, but it requires more thought.

	// Publish the operation before writing to the database to avoid orphaned invitation resolution
	// operations.
	invitationResolutionID := uuid.New().String()
	s.asyncOpsPublisher.PublishInvitationResolution(ctx, &auth_internal.ResolveInvitationsMessage{
		InvitationResolutionId:    invitationResolutionID,
		ResolveInvitationsRequest: req,
		PublishTime:               timestamppb.Now(),
	})

	invitationResolutionDAO := s.daoFactory.GetInvitationResolutionDAO()
	invitationResolution := &auth_entities.InvitationResolution{
		Id:        invitationResolutionID,
		CreatedAt: timestamppb.Now(),
		Status:    auth_entities.InvitationResolution_PENDING,
	}
	_, err := invitationResolutionDAO.Create(ctx, invitationResolution)
	if err != nil {
		log.Error().Err(err).Msg("Failed to create invitation resolution record")
		return nil, status.Error(codes.Internal, "Failed to create invitation resolution record")
	}

	return &authpb.ResolveInvitationsResponse{
		InvitationResolutionId: invitationResolutionID,
	}, nil
}

func (s *TeamManagementServer) GetResolveInvitationsStatus(
	ctx context.Context, req *authpb.GetResolveInvitationsStatusRequest,
) (*authpb.GetResolveInvitationsStatusResponse, error) {
	err := s.teamManagementAuthCheck(ctx, nil, nil, tokenexchangepb.Scope_AUTH_R, false, "GetResolveInvitationsStatus")
	if err != nil {
		return nil, err
	}
	return s.getResolveInvitationsStatus(ctx, req)
}

// Application logic for the GetResolveInvitationsStatus endpoint, pulled out so that it can be used
// by both TeamManagementService and FrontEndTokenService. This does NOT perform any authorization
// checks; callers must do that themselves.
func (s *TeamManagementServer) getResolveInvitationsStatus(
	ctx context.Context, req *authpb.GetResolveInvitationsStatusRequest,
) (*authpb.GetResolveInvitationsStatusResponse, error) {
	if !s.teamManagementEnabled() {
		return nil, status.Error(codes.Internal, "Disabled")
	}

	invitationResolutionDAO := s.daoFactory.GetInvitationResolutionDAO()
	invitationResolution, err := invitationResolutionDAO.Get(ctx, req.InvitationResolutionId)
	if err != nil {
		log.Error().Err(err).Msg("Failed to fetch invitation resolution")
		return nil, status.Error(codes.Internal, "Failed to fetch invitation resolution")
	}

	if invitationResolution == nil {
		log.Error().Msg("Invitation resolution not found")
		return nil, status.Error(codes.NotFound, "Invitation resolution not found")
	}

	return &authpb.GetResolveInvitationsStatusResponse{
		InvitationResolution: invitationResolution,
	}, nil
}

func (s *TeamManagementServer) GetSubscription(
	ctx context.Context, req *authpb.GetSubscriptionRequest,
) (*authpb.GetSubscriptionResponse, error) {
	// Validate the request
	var subscriptionId string
	var userId, tenantId *string

	switch id := req.LookupId.(type) {
	case *authpb.GetSubscriptionRequest_SubscriptionId:
		// We'll need to get the user/tenant ID from the subscription to check permissions
		subscriptionId = id.SubscriptionId
		subscriptionDAO := s.daoFactory.GetSubscriptionDAO()
		subscription, err := subscriptionDAO.Get(ctx, subscriptionId)
		if err != nil {
			log.Error().Err(err).Msg("Failed to fetch subscription")
			return nil, status.Error(codes.Internal, "Subscription not found or you don't have permission to access it")
		}

		if subscription == nil {
			log.Error().Msg("Subscription not found")
			return nil, status.Error(codes.Internal, "Subscription not found or you don't have permission to access it")
		}

		// Get the tenant ID from the subscription
		switch owner := subscription.Owner.(type) {
		case *auth_entities.Subscription_TenantId:
			// This use case is currently not supported in FE
			tenantId = &owner.TenantId
		case *auth_entities.Subscription_UserId:
			// For individual subscriptions, we need to check if the caller is the owner
			userId = &owner.UserId
		default:
			log.Error().Str("subscription_id", id.SubscriptionId).Msg("Subscription exists but has no valid owner type")
			return nil, status.Error(codes.Internal, "Subscription not found or you don't have permission to access it")
		}

	case *authpb.GetSubscriptionRequest_TenantId:
		tenantId = &id.TenantId
	default:
		return nil, status.Error(codes.InvalidArgument, "Either subscription_id or tenant_id must be provided")
	}

	// Check permissions given the userID or tenantID
	err := s.teamManagementAuthCheck(ctx, tenantId, userId, tokenexchangepb.Scope_AUTH_R, false, "GetSubscription")
	if err != nil {
		return nil, err
	}

	// Get the subscription for the tenant
	if tenantId != nil {
		// Get the subscription ID from the tenant
		tenantSubscriptionMappingDAO := s.daoFactory.GetTenantSubscriptionMappingDAO()
		subscriptionMapping, err := tenantSubscriptionMappingDAO.Get(ctx, *tenantId)
		if err != nil {
			log.Error().Err(err).Msg("Failed to fetch subscription mapping")
			return nil, status.Error(codes.Internal, "Failed to fetch subscription mapping")
		}

		if subscriptionMapping == nil {
			log.Error().Str("tenant_id", *tenantId).Msg("Subscription not found for tenant")
			return nil, status.Error(codes.NotFound, "Subscription not found for tenant")
		}

		subscriptionId = subscriptionMapping.OrbSubscriptionId
	}

	subscriptionDAO := s.daoFactory.GetSubscriptionDAO()
	subscription, err := subscriptionDAO.Get(ctx, subscriptionId)
	if err != nil {
		log.Error().Err(err).Msg("Failed to fetch subscription")
		return nil, status.Error(codes.Internal, "Failed to fetch subscription")
	}

	if subscription == nil {
		log.Error().Str("subscription_id", subscriptionId).Msg("Subscription not found")
		return nil, status.Error(codes.NotFound, "Subscription not found")
	}

	return &authpb.GetSubscriptionResponse{
		Subscription: subscription,
	}, nil
}

// paginatedListHelper handles common pagination logic for list endpoints
// This is a generic helper function that can be used with different types
func paginatedListHelper[T any](
	ctx context.Context,
	pageSize uint32,
	pageToken string,
	defaultPageSize uint32,
	fetchFunc func(ctx context.Context, startKey string, pageSize uint32) ([]T, string, error),
	errorMsg string,
) ([]T, string, error) {
	// Set default page size if not specified
	if pageSize == 0 {
		pageSize = defaultPageSize
	}

	// Parse the page token to get the starting key
	startKey := ""
	if pageToken != "" {
		// The page token is the row key of the last item from the previous page
		startKey = pageToken
	}

	// Fetch data using the provided function
	items, nextStartKey, err := fetchFunc(ctx, startKey, pageSize)
	if err != nil {
		log.Error().Err(err).Msg(errorMsg)
		return nil, "", status.Error(codes.Internal, errorMsg)
	}

	return items, nextStartKey, nil
}

func (s *TeamManagementServer) ListSubscriptions(
	ctx context.Context, req *authpb.ListSubscriptionsRequest,
) (*authpb.ListSubscriptionsResponse, error) {
	// Check permissions - this endpoint requires wildcard tenant access
	err := s.teamManagementWildcardAuthCheck(ctx, tokenexchangepb.Scope_AUTH_R, "ListSubscriptions")
	if err != nil {
		return nil, err
	}

	// Get the subscriptions with pagination
	subscriptionDAO := s.daoFactory.GetSubscriptionDAO()

	subscriptions, nextPageToken, err := paginatedListHelper(
		ctx,
		req.PageSize,
		req.PageToken,
		100, // Default page size
		subscriptionDAO.FindAllPaginated,
		"Failed to fetch subscriptions",
	)

	return &authpb.ListSubscriptionsResponse{
		Subscriptions: subscriptions,
		NextPageToken: nextPageToken,
	}, err
}

func (s *TeamManagementServer) ListTenantSubscriptionMappings(
	ctx context.Context, req *authpb.ListTenantSubscriptionMappingsRequest,
) (*authpb.ListTenantSubscriptionMappingsResponse, error) {
	// Check permissions - this endpoint requires wildcard tenant access
	err := s.teamManagementWildcardAuthCheck(ctx, tokenexchangepb.Scope_AUTH_R, "ListTenantSubscriptionMappings")
	if err != nil {
		return nil, err
	}

	// Get the tenant subscription mappings with pagination
	tenantSubscriptionMappingDAO := s.daoFactory.GetTenantSubscriptionMappingDAO()

	tenantSubscriptionMappings, nextPageToken, err := paginatedListHelper(
		ctx,
		req.PageSize,
		req.PageToken,
		100, // Default page size
		tenantSubscriptionMappingDAO.FindAllPaginated,
		"Failed to fetch tenant subscription mappings",
	)

	return &authpb.ListTenantSubscriptionMappingsResponse{
		TenantSubscriptionMappings: tenantSubscriptionMappings,
		NextPageToken:              nextPageToken,
	}, err
}

func (s *TeamManagementServer) ListUserTenantMappings(
	ctx context.Context, req *authpb.ListUserTenantMappingsRequest,
) (*authpb.ListUserTenantMappingsResponse, error) {
	// Check permissions - this endpoint requires wildcard tenant access
	err := s.teamManagementWildcardAuthCheck(ctx, tokenexchangepb.Scope_AUTH_R, "ListUserTenantMappings")
	if err != nil {
		return nil, err
	}

	// Get the user tenant mappings with pagination
	userTenantMappingDAO := s.daoFactory.GetUserTenantMappingDAO("")

	userTenantMappings, nextPageToken, err := paginatedListHelper(
		ctx,
		req.PageSize,
		req.PageToken,
		100, // Default page size
		userTenantMappingDAO.FindAllPaginated,
		"Failed to fetch user tenant mappings",
	)

	return &authpb.ListUserTenantMappingsResponse{
		UserTenantMappings: userTenantMappings,
		NextPageToken:      nextPageToken,
	}, err
}

func (s *TeamManagementServer) DeleteInvitation(
	ctx context.Context, req *authpb.DeleteInvitationRequest,
) (*authpb.DeleteInvitationResponse, error) {
	if !s.teamManagementEnabled() {
		return nil, status.Error(codes.Internal, "Disabled")
	}

	err := s.teamManagementAuthCheck(
		ctx, &req.TenantId, nil, tokenexchangepb.Scope_AUTH_RW, false, "DeleteInvitation")
	if err != nil {
		return nil, err
	}
	// We know this will succeed because the auth check passed.
	authInfo, _ := auth.GetAugmentClaims(ctx)

	// Audit Log
	s.WriteTeamsAuditLog(ctx, fmt.Sprintf("deleting invitation %s for tenant %s", req.InvitationId, req.TenantId))

	invitationDAO := s.daoFactory.GetTenantInvitationDAO(req.TenantId)
	err = invitationDAO.Delete(ctx, req.InvitationId)
	if err != nil {
		log.Error().Err(err).Msg("Failed to delete invitation")
		return nil, status.Error(codes.Internal, "Failed to delete invitation")
	}

	// Record invitation deletion in request insight.
	event := ripublisher.NewTenantEvent()
	event.Event = &ripb.TenantEvent_DeleteInvitation{
		DeleteInvitation: &ripb.DeleteInvitation{
			InvitationId: req.InvitationId,
		},
	}
	riErr := s.requestInsightPublisher.PublishTenantEvent(ctx, &ripb.TenantInfo{
		TenantId:   req.TenantId,
		TenantName: authInfo.TenantName,
	}, event)
	if riErr != nil {
		log.Warn().Err(riErr).Msg("Failed to publish DeleteInvitation event")
	}

	log.Info().Msgf("successfully deleted invitation %s for tenant %s", req.InvitationId, req.TenantId)
	s.WriteTeamsAuditLog(ctx, fmt.Sprintf("successfully deleted invitation %s for tenant %s", req.InvitationId, req.TenantId))
	return &authpb.DeleteInvitationResponse{}, nil
}

func (s *TeamManagementServer) UpdateSubscription(
	ctx context.Context, req *authpb.UpdateSubscriptionRequest,
) (*authpb.UpdateSubscriptionResponse, error) {
	if !s.teamManagementEnabled() {
		return nil, status.Error(codes.Internal, "Disabled")
	}

	// Validate the request
	if req.SubscriptionId == "" {
		return nil, status.Error(codes.InvalidArgument, "Subscription ID is required")
	}

	if req.Seats <= 0 {
		return nil, status.Error(codes.InvalidArgument, "Seats must be greater than 0")
	}

	// Get the subscription to check permissions
	subscriptionDAO := s.daoFactory.GetSubscriptionDAO()
	subscription, err := subscriptionDAO.Get(ctx, req.SubscriptionId)
	if err != nil {
		log.Error().Str("subscription_id", req.SubscriptionId).Msg("Failed to fetch subscription")
		return nil, status.Error(codes.Internal, "Failed to fetch subscription")
	}

	if subscription == nil {
		return nil, status.Error(codes.NotFound, "Subscription not found")
	}

	// Check if there is already a subscription change in progress
	if subscription.SubscriptionChangeId != nil {
		log.Error().Str("subscription_id", req.SubscriptionId).Msg("Subscription change already in progress")
		return nil, status.Error(codes.FailedPrecondition, "Subscription change already in progress")
	}

	// Seats must be less than the configured max seats for the plan
	planInfo := s.orbConfig.GetPlanFeatures(subscription.ExternalPlanId)
	if planInfo == nil {
		log.Error().Str("plan_id", subscription.ExternalPlanId).Msg("Unknown plan ID")
		return nil, status.Error(codes.Internal, "Unknown plan ID")
	}
	if int(req.Seats) > planInfo.Features.MaxSeats {
		return nil, status.Error(codes.InvalidArgument, fmt.Sprintf("Seats must be less than %d", planInfo.Features.MaxSeats))
	}

	// Check permissions based on the subscription owner
	var tenantId *string

	switch owner := subscription.Owner.(type) {
	case *auth_entities.Subscription_TenantId:
		tenantId = &owner.TenantId
	case *auth_entities.Subscription_UserId:
		log.Error().Str("subscription_id", req.SubscriptionId).Msg("Individual subscriptions cannot be updated with this endpoint")
		return nil, status.Error(codes.Internal, "Individual subscriptions cannot be updated with this endpoint")
	default:
		log.Error().Msg("Subscription exists but has no valid owner type")
		return nil, status.Error(codes.Internal, "Subscription has no valid owner")
	}

	// Check permissions given the tenantID
	err = s.teamManagementAuthCheck(ctx, tenantId, nil, tokenexchangepb.Scope_AUTH_RW, true, "UpdateSubscription")
	if err != nil {
		log.Error().Err(err).Msg("Failed to check permissions")
		return nil, err
	}

	// If they are not on the trial, check that they have a payment method
	if subscription.ExternalPlanId != s.orbConfig.getTrialPlan().ID {
		// Get Stripe Customer ID from the tenant Subscription Mapping
		// We can use tenant subscription mapping because we know this is a team
		tenantSubscriptionMappingDAO := s.daoFactory.GetTenantSubscriptionMappingDAO()
		tenantSubscriptionMapping, err := tenantSubscriptionMappingDAO.Get(ctx, *tenantId)
		if err != nil {
			log.Error().Err(err).Str("tenant_id", *tenantId).Msg("Failed to fetch tenant subscription mapping")
			return nil, status.Error(codes.Internal, "Failed to fetch tenant subscription mapping")
		}
		if tenantSubscriptionMapping == nil {
			log.Error().Str("tenant_id", *tenantId).Msg("Tenant subscription mapping not found")
			return nil, status.Error(codes.NotFound, "Tenant subscription mapping not found")
		}

		hasPaymentMethod, err := s.stripeClient.HasPaymentMethod(tenantSubscriptionMapping.StripeCustomerId)
		if err != nil {
			log.Error().Err(err).Str("stripe_customer_id", tenantSubscriptionMapping.StripeCustomerId).Msg("Failed to check payment method status")
			return nil, status.Error(codes.Internal, "Failed to check payment method status")
		}
		if !hasPaymentMethod {
			log.Error().Str("stripe_customer_id", tenantSubscriptionMapping.StripeCustomerId).Msg("User does not have a payment method")
			return nil, status.Error(codes.FailedPrecondition, "User does not have a payment method")
		}
	}

	// Audit Log
	s.WriteTeamsAuditLog(ctx, fmt.Sprintf("updating subscription %s for tenant %s to %d seats", req.SubscriptionId, *tenantId, req.Seats))

	// Validate that the requested seat count is sufficient
	err = s.ValidateSubscriptionSeats(ctx, *tenantId, req.Seats, 0)
	if err != nil {
		return nil, err
	}

	// Publish seat update to pubsub queue and update subscription change info in database
	updateSubscriptionID := uuid.New().String()
	updateMsg := &auth_internal.UpdateSubscriptionMessage{
		Id:             updateSubscriptionID,
		SubscriptionId: req.SubscriptionId,
		NumSeats:       req.Seats,
		PublishTime:    timestamppb.Now(),
	}
	err = s.asyncOpsPublisher.PublishUpdateSubscription(ctx, updateMsg)
	if err != nil {
		log.Error().Err(err).Msg("Failed to publish update subscription message")
		return nil, status.Error(codes.Internal, "Failed to publish update subscription message")
	}

	// Update subscription DAO with update ID
	_, err = subscriptionDAO.TryUpdate(ctx, req.SubscriptionId, func(sub *auth_entities.Subscription) bool {
		sub.SubscriptionChangeId = &updateSubscriptionID
		return true
	}, DefaultRetry)
	if err != nil {
		log.Error().Err(err).Msg("Failed to update subscription with change info")
		return nil, status.Error(codes.Internal, "Failed to update subscription with change info")
	}

	return &authpb.UpdateSubscriptionResponse{}, nil
}

// PutUserOnPlan associates a user with a plan
func (s *TeamManagementServer) PutUserOnPlan(
	ctx context.Context, req *authpb.PutUserOnPlanRequest,
) (*authpb.PutUserOnPlanResponse, error) {
	if req.UserId == "" {
		return nil, status.Error(codes.InvalidArgument, "User ID is required")
	}
	if req.PlanId == "" {
		// TODO: For now this is hardcoded for backwards compatibility. In the future we should
		// deprecate the plan enum and remove this code.
		switch req.Plan {
		case authpb.PutUserOnPlanRequest_COMMUNITY:
			req.PlanId = "orb_community_plan"
		case authpb.PutUserOnPlanRequest_DEVELOPER:
			req.PlanId = "orb_developer_plan"
		default:
			return nil, status.Error(codes.InvalidArgument, "Plan ID is required")
		}
	}

	// Get the user to check if they exist
	userDAO := s.daoFactory.GetUserDAO()
	user, err := userDAO.Get(ctx, req.UserId)
	if err != nil {
		log.Error().Err(err).Msgf("Failed to get user %s", req.UserId)
		return nil, status.Error(codes.Internal, "Failed to get user information")
	}
	if user == nil {
		log.Error().Msgf("User %s not found", req.UserId)
		return nil, status.Error(codes.NotFound, "User not found")
	}

	if user.BillingMethod != auth_entities.BillingMethod_BILLING_METHOD_ORB {
		log.Error().Str("user_id", req.UserId).Msg("User is not billing through orb")
		return nil, status.Error(codes.FailedPrecondition, "User is not billing through orb")
	}

	if user.StripeCustomerId == "" {
		log.Error().Str("user_id", req.UserId).Msg("User does not have a stripe customer ID")
		return nil, status.Error(codes.FailedPrecondition, "User does not have a stripe customer ID")
	}

	if user.OrbCustomerId == "" {
		log.Error().Str("user_id", req.UserId).Msg("User does not have an orb customer ID")
		return nil, status.Error(codes.FailedPrecondition, "User does not have an orb customer ID")
	}

	if len(user.Tenants) != 1 {
		log.Error().Str("user_id", req.UserId).Msg("User does not belong to exactly one tenant")
		return nil, status.Error(codes.FailedPrecondition, "User does not belong to exactly one tenant")
	}

	// Get the current tenant
	currentTenantID := user.Tenants[0]
	currentTenant, err := s.tenantMap.GetTenantByID(currentTenantID)
	if err != nil {
		log.Error().Err(err).Str("tenant_id", currentTenantID).Msg("Failed to get tenant")
		return nil, status.Error(codes.Internal, "Failed to get tenant information")
	}
	if currentTenant == nil {
		log.Error().Str("tenant_id", currentTenantID).Msg("Tenant not found")
		return nil, status.Error(codes.NotFound, "Tenant not found")
	}

	// Check if this is a self-serve team tenant
	isSelfServeTeam := tenantutil.IsSelfServeTeamTenant(currentTenant)

	// Handle teams and individual users differently
	if isSelfServeTeam {
		return s.putTeamOnPlan(ctx, req, user, currentTenant)
	} else {
		return s.putIndividualUserOnPlan(ctx, req, user, currentTenant)
	}
}

// putTeamOnPlan handles putting a team on a plan
func (s *TeamManagementServer) putTeamOnPlan(
	ctx context.Context,
	req *authpb.PutUserOnPlanRequest,
	user *auth_entities.User,
	currentTenant *tw_pb.Tenant,
) (*authpb.PutUserOnPlanResponse, error) {
	// For team tenants, the user must be an admin
	err := s.teamManagementAuthCheck(ctx, &currentTenant.Id, &req.UserId, tokenexchangepb.Scope_AUTH_RW, true, "PutTeamOnPlan")
	if err != nil {
		return nil, err
	}

	s.WriteTeamsAuditLog(ctx, fmt.Sprintf("putting team with tenant ID %s on plan %s", currentTenant.Id, req.PlanId))

	// Use the plan ID directly
	targetOrbPlanID := req.PlanId

	// Validate that the plan exists in our configuration
	plan := s.orbConfig.findPlan(targetOrbPlanID)
	if plan == nil {
		log.Error().Str("plan_id", targetOrbPlanID).Msg("Plan not found in configuration")
		return nil, status.Error(codes.InvalidArgument, "Plan not found in configuration")
	}

	// Check if teams can be on this plan (community plans are not allowed for teams)
	switch plan.Features.PlanType {
	case PlanTypeCommunity:
		log.Error().Str("tenant_id", currentTenant.Id).Str("plan_id", targetOrbPlanID).Msg("Teams cannot be put on the community plan")
		return nil, status.Error(codes.InvalidArgument, "Teams cannot be put on the community plan")
	case PlanTypePaid:
		// Teams can be on paid plans - continue
	default:
		log.Error().Str("tenant_id", currentTenant.Id).Str("plan_id", targetOrbPlanID).Str("plan_type", string(plan.Features.PlanType)).Msg("Invalid plan type for teams")
		return nil, status.Error(codes.InvalidArgument, "Unknown plan type")
	}

	// Get the tenant subscription mapping to retrieve Orb subscription ID
	tenantSubscriptionMappingDAO := s.daoFactory.GetTenantSubscriptionMappingDAO()
	subscriptionMapping, err := tenantSubscriptionMappingDAO.Get(ctx, currentTenant.Id)
	if err != nil {
		log.Error().Err(err).Str("tenant_id", currentTenant.Id).Msg("Failed to get tenant subscription mapping")
		return nil, status.Error(codes.Internal, "Failed to get tenant subscription mapping")
	}
	if subscriptionMapping == nil {
		log.Error().Str("tenant_id", currentTenant.Id).Msg("Tenant subscription mapping not found")
		return nil, status.Error(codes.NotFound, "Tenant subscription mapping not found")
	}

	// Ensure that the team has a payment method
	// They will need one for any plan they go to, as they can't go to community
	hasPaymentMethod, err := s.stripeClient.HasPaymentMethod(subscriptionMapping.StripeCustomerId)
	if err != nil {
		log.Error().Err(err).Str("stripe_customer_id", subscriptionMapping.StripeCustomerId).Msg("Failed to check payment method status")
		return nil, status.Error(codes.Internal, "Failed to check payment method status")
	}
	if !hasPaymentMethod {
		log.Error().Str("stripe_customer_id", subscriptionMapping.StripeCustomerId).Str("tenant_id", currentTenant.Id).Msg("Team does not have a payment method")
		return nil, status.Error(codes.FailedPrecondition, "Team does not have a payment method")
	}

	// Get current Orb subscription information to check the current plan
	orbSubscriptionID := subscriptionMapping.OrbSubscriptionId
	orbSubInfo, err := s.orbClient.GetUserSubscription(ctx, orbSubscriptionID, &orb.ItemIds{
		SeatsID:            s.orbConfig.SeatsItemID,
		IncludedMessagesID: s.orbConfig.IncludedMessagesItemID,
	})
	if err != nil {
		log.Error().Err(err).Str("orb_subscription_id", orbSubscriptionID).Msg("Failed to get Orb subscription info")
		return nil, status.Error(codes.Internal, "Failed to get Orb subscription info")
	}
	if orbSubInfo == nil {
		log.Error().Str("orb_subscription_id", orbSubscriptionID).Msg("Orb subscription info not found")
		return nil, status.Error(codes.NotFound, "Orb subscription info not found")
	}

	// If the team is already on the requested Orb plan and the plan is active, no need to change
	if orbSubInfo.ExternalPlanID == targetOrbPlanID && orbSubInfo.OrbStatus == "active" {
		log.Info().
			Str("tenant_id", currentTenant.Id).
			Str("orb_plan_id", targetOrbPlanID).
			Msg("Team is already on the requested Orb plan and the plan is active, no change needed")
		return &authpb.PutUserOnPlanResponse{}, nil
	}

	// Generate a unique plan change ID
	planChangeID := uuid.New().String()

	// Construct the TeamPlanChangeMessage for async processing
	planChangeMsg := &auth_internal.TeamPlanChangeMessage{
		TeamTenantId:      currentTenant.Id,
		TargetOrbPlanId:   targetOrbPlanID,
		PlanChangeId:      planChangeID,
		PublishTime:       timestamppb.Now(),
		InitiatedByUserId: user.Id,
	}

	// Publish the plan change message
	if err := s.asyncOpsPublisher.PublishTeamPlanChange(ctx, planChangeMsg); err != nil {
		log.Error().
			Err(err).
			Str("tenant_id", currentTenant.Id).
			Str("plan_change_id", planChangeID).
			Str("target_orb_plan_id", targetOrbPlanID).
			Msg("Failed to publish team Orb plan change message")
		return nil, status.Errorf(codes.Internal,
			"Failed to publish team Orb plan change: tenant_id=%s, plan_change_id=%s: %v",
			currentTenant.Id, planChangeID, err)
	}

	// Update the tenant subscription mapping with the plan change info
	newMapping, err := tenantSubscriptionMappingDAO.TryUpdate(ctx, currentTenant.Id, func(mapping *auth_entities.TenantSubscriptionMapping) bool {
		if mapping.PlanChange != nil {
			log.Warn().
				Str("tenant_id", currentTenant.Id).
				Str("plan_change_id", planChangeID).
				Msg("Plan change already in progress, skipping update")
			return false
		}

		mapping.PlanChange = &auth_entities.TenantSubscriptionMapping_PlanChangeInfo{
			Id:              planChangeID,
			TargetOrbPlanId: targetOrbPlanID,
			CreatedAt:       timestamppb.Now(),
			UpdatedAt:       timestamppb.Now(),
		}
		return true
	}, DefaultRetry)
	if err != nil {
		log.Error().
			Err(err).
			Str("tenant_id", currentTenant.Id).
			Str("plan_change_id", planChangeID).
			Str("target_orb_plan_id", targetOrbPlanID).
			Msg("Failed to update tenant subscription mapping with plan change info")
		return nil, status.Errorf(codes.Internal,
			"Failed to update tenant subscription mapping with plan change info: tenant_id=%s, plan_change_id=%s: %v",
			currentTenant.Id, planChangeID, err)
	}

	if newMapping.PlanChange == nil {
		log.Error().
			Str("tenant_id", currentTenant.Id).
			Str("plan_change_id", planChangeID).
			Str("target_orb_plan_id", targetOrbPlanID).
			Msg("Failed to update tenant subscription mapping with plan change info")
		return nil, status.Errorf(codes.Internal,
			"Failed to update tenant subscription mapping with plan change info: tenant_id=%s, plan_change_id=%s",
			currentTenant.Id, planChangeID)
	}

	log.Info().
		Str("tenant_id", currentTenant.Id).
		Str("plan_change_id", planChangeID).
		Str("current_orb_plan_id", orbSubInfo.ExternalPlanID).
		Str("target_orb_plan_id", targetOrbPlanID).
		Msg("Team Orb plan change successfully initiated")

	s.WriteTeamsAuditLog(ctx, fmt.Sprintf("successfully initiated Orb plan change for team with tenant ID %s from plan %s to plan %s",
		currentTenant.Id, orbSubInfo.ExternalPlanID, targetOrbPlanID))

	return &authpb.PutUserOnPlanResponse{}, nil
}

// putIndividualUserOnPlan handles putting an individual user on a plan
func (s *TeamManagementServer) putIndividualUserOnPlan(
	ctx context.Context,
	req *authpb.PutUserOnPlanRequest,
	user *auth_entities.User,
	currentTenant *tw_pb.Tenant,
) (*authpb.PutUserOnPlanResponse, error) {
	// For individual users, they can only change their own subscription
	err := s.teamManagementAuthCheck(ctx, nil, &req.UserId, tokenexchangepb.Scope_AUTH_RW, false, "PutUserOnPlan")
	if err != nil {
		return nil, err
	}

	// If not going to the community plan, ensure that the user has a payment method
	if req.PlanId != s.orbConfig.getCommunityPlan().ID {
		hasPaymentMethod, err := s.stripeClient.HasPaymentMethod(user.StripeCustomerId)
		if err != nil {
			log.Error().Err(err).Str("user_id", req.UserId).Str("stripe_customer_id", user.StripeCustomerId).Msg("Failed to check payment method status")
			return nil, status.Error(codes.Internal, "Failed to check payment method status")
		}
		if !hasPaymentMethod {
			log.Error().Str("user_id", req.UserId).Str("stripe_customer_id", user.StripeCustomerId).Msg("User does not have a payment method")
			return nil, status.Error(codes.FailedPrecondition, "User does not have a payment method")
		}
	}

	s.WriteTeamsAuditLog(ctx, fmt.Sprintf("putting individual user %s on plan %s", req.UserId, req.PlanId))

	// Check if a tier change is already in progress
	if user.TierChange != nil {
		log.Info().
			Str("user_id", req.UserId).
			Str("tier_change_id", user.TierChange.Id).
			Str("current_target_tier", user.TierChange.TargetTier.String()).
			Msg("User already has a tier change in progress")
		return nil, status.Errorf(codes.FailedPrecondition,
			"User already has a tier change in progress: user_id=%s, tier_change_id=%s, current_target_tier=%s",
			user.Id, user.TierChange.Id, user.TierChange.TargetTier)
	}

	if currentTenant.Tier != tw_pb.TenantTier_COMMUNITY && currentTenant.Tier != tw_pb.TenantTier_PROFESSIONAL {
		log.Error().Str("tenant_name", currentTenant.Name).Str("tenant_tier", currentTenant.Tier.String()).Msg("Invalid tenant tier")
		return nil, status.Error(codes.InvalidArgument, "Current tenant must be either professional or community tier")
	}

	// Validate that the plan exists in our configuration
	plan := s.orbConfig.findPlan(req.PlanId)
	if plan == nil {
		log.Error().Str("plan_id", req.PlanId).Msg("Plan not found in configuration")
		return nil, status.Error(codes.InvalidArgument, "Plan not found in configuration")
	}

	// Set the tier based on the plan type
	var newTier auth_entities.UserTier
	var newTierAsTenantTier tw_pb.TenantTier
	switch plan.Features.PlanType {
	case PlanTypeCommunity:
		newTier = auth_entities.UserTier_COMMUNITY
		newTierAsTenantTier = tw_pb.TenantTier_COMMUNITY
	case PlanTypePaid:
		newTier = auth_entities.UserTier_PROFESSIONAL
		newTierAsTenantTier = tw_pb.TenantTier_PROFESSIONAL
	default:
		log.Error().Str("plan_id", req.PlanId).Str("plan_type", string(plan.Features.PlanType)).Msg("Invalid plan type")
		return nil, status.Error(codes.InvalidArgument, "Invalid plan type")
	}

	newTenant := currentTenant
	if currentTenant.Tier != newTierAsTenantTier {
		var newTenantName string
		var tenantNamesStr string
		switch newTier {
		case auth_entities.UserTier_COMMUNITY:
			tenantNamesStr, err = s.featureFlagHandle.GetString("auth_central_signup_tenant", "")
		case auth_entities.UserTier_PROFESSIONAL:
			tenantNamesStr, err = s.featureFlagHandle.GetString("auth_central_individual_tenant", "")
		}
		if err != nil {
			log.Error().Err(err).Str("user_id", req.UserId).Str("plan_id", req.PlanId).Msg("PutUserOnPlan failed: error getting tenant name from feature flag")
			return nil, status.Error(codes.Internal, fmt.Sprintf("Failed to get new tenant name for plan %s", req.PlanId))
		}

		if tenantNamesStr == "" {
			log.Error().Str("user_id", req.UserId).Str("plan_id", req.PlanId).Msg("PutUserOnPlan failed: tenant name not configured")
			return nil, status.Error(codes.Internal, fmt.Sprintf("Default tenant not configured for plan %s", req.PlanId))
		}

		// Split comma-separated list of tenant names
		tenantNames := strings.Split(tenantNamesStr, ",")

		// Choose a tenant randomly
		if len(tenantNames) > 0 {
			newTenantName = tenantNames[s.randomSelector.Intn(len(tenantNames))]
			log.Info().
				Str("selected_tenant", newTenantName).
				Strs("available_tenants", tenantNames).
				Msg("Selected tenant for user plan change")
		}

		// Get new tenant
		newTenant, err = s.tenantMap.GetTenant(newTenantName)
		if err != nil {
			log.Error().Err(err).Str("user_id", req.UserId).Str("tenant_name", newTenantName).Msg("PutUserOnPlan failed: error fetching new tenant")
			return nil, status.Error(codes.Internal, "Failed to get new tenant")
		}
		if newTenant == nil {
			log.Error().Str("user_id", req.UserId).Str("tenant_name", newTenantName).Msg("PutUserOnPlan failed: new tenant not found")
			return nil, status.Error(codes.Internal, "New tenant not found")
		}
	}

	// Generate a unique tier change ID
	tierChangeID := uuid.New().String()

	// Construct the UserTierChangeMessage
	tierChangeMsg := &auth_internal.UserTierChangeMessage{
		User:          user,
		CurrentTenant: currentTenant,
		NewTier:       newTier,
		NewTenant:     newTenant,
		TierChangeId:  tierChangeID,
		PublishTime:   timestamppb.Now(),
		NewPlanId:     req.PlanId,
	}

	// Publish the tier change message
	if err := s.asyncOpsPublisher.PublishUserTierChange(ctx, tierChangeMsg); err != nil {
		log.Error().
			Err(err).
			Str("user_id", user.Id).
			Str("tier_change_id", tierChangeID).
			Str("plan_id", req.PlanId).
			Msg("Failed to publish tier change message")
		return nil, status.Errorf(codes.Internal,
			"Failed to publish user tier change: user_id=%s, tier_change_id=%s: %v",
			user.Id, tierChangeID, err)
	}

	// Update the user with the tier change info
	updateUser := func(u *auth_entities.User) bool {
		// If the user already has a tier change in progress, don't update
		if u.TierChange != nil {
			log.Warn().
				Str("user_id", user.Id).
				Str("existing_tier_change_id", u.TierChange.Id).
				Str("new_tier_change_id", tierChangeID).
				Msg("User already has a tier change in progress during update")
			// Return false to indicate no changes were made
			return false
		}

		// Only set the tier change if it wasn't already set
		u.TierChange = &auth_entities.User_TierChangeInfo{
			Id:         tierChangeID,
			TargetTier: newTier,
			CreatedAt:  timestamppb.Now(),
			UpdatedAt:  timestamppb.Now(),
		}
		return true
	}

	newUser, err := s.daoFactory.GetUserDAO().TryUpdate(ctx, req.UserId, updateUser, DefaultRetry)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_id", user.Id).
			Str("tier_change_id", tierChangeID).
			Msg("Failed to update user with tier change info")
		return nil, status.Errorf(codes.Internal,
			"Failed to update user with tier change info: user_id=%s, tier_change_id=%s: %v",
			user.Id, tierChangeID, err)
	}

	if newUser.TierChange == nil {
		log.Error().
			Str("user_id", user.Id).
			Str("tier_change_id", tierChangeID).
			Msg("Failed to update user with tier change info")
		return nil, status.Errorf(codes.Internal,
			"Failed to update user with tier change info: user_id=%s, tier_change_id=%s",
			user.Id, tierChangeID)
	}

	log.Info().
		Str("user_id", user.Id).
		Str("tier_change_id", tierChangeID).
		Str("plan_id", req.PlanId).
		Msg("Individual user plan change successfully initiated")

	s.WriteTeamsAuditLog(ctx, fmt.Sprintf("successfully initiated plan change for individual user %s to plan %s", user.Id, req.PlanId))

	return &authpb.PutUserOnPlanResponse{}, nil
}

// ValidateSubscriptionSeats checks if a subscription has enough seats for the current team members,
// pending invitations, and optionally additional new invitations.
func (s *TeamManagementServer) ValidateSubscriptionSeats(
	ctx context.Context,
	tenantID string,
	seatLimit int32,
	newInvitationCount int,
) error {
	// 1. Count active team members in the tenant
	activeTeamMemberCount, err := GetActiveTeamMemberCount(ctx, s.daoFactory, s.tenantMap, tenantID)
	if err != nil {
		log.Error().Err(err).Str("tenant_id", tenantID).Msg("Failed to fetch team members")
		return status.Error(codes.Internal, "Failed to fetch team members")
	}

	// 2. Count pending invitations for the tenant
	pendingInvitations, err := GetInvitationsForTenant(
		ctx, s.daoFactory, tenantID, auth_entities.TenantInvitation_PENDING,
	)
	if err != nil {
		log.Error().Err(err).Str("tenant_id", tenantID).Msg("Failed to fetch pending invitations")
		return status.Error(codes.Internal, "Failed to fetch pending invitations")
	}
	pendingInvitationCount := len(pendingInvitations)

	// 3. Calculate total required seats
	totalRequired := activeTeamMemberCount + pendingInvitationCount + newInvitationCount

	// 4. Validate that available seats are sufficient
	if totalRequired > int(seatLimit) {
		log.Error().Int("seat_limit", int(seatLimit)).Int("active_members", activeTeamMemberCount).Int("pending_invitations", pendingInvitationCount).Int("new_invitations", newInvitationCount).Msg("Not enough seats available")
		return status.Error(
			codes.InvalidArgument,
			fmt.Sprintf("Not enough seats available. You need %d seats but only have %d. Please upgrade your subscription to add more team members.", totalRequired, seatLimit),
		)
	}

	return nil
}

// Purchase additional Orb usage units
// We use "Credits" as the input here, but "Credits" is actually the number of user messages.
func (s *TeamManagementServer) PurchaseCredits(
	ctx context.Context, req *authpb.PurchaseCreditsRequest,
) (*authpb.PurchaseCreditsResponse, error) {
	// Ensure Orb is enabled
	if s.orbClient == nil || !s.orbConfig.Enabled {
		log.Error().Str("augment_user_id", req.UserId).Str("tenant_id", req.TenantId).Float32("num_purchasing", req.Credits).Msg("Orb is not enabled")
		return nil, status.Error(codes.Internal, "Orb is not enabled")
	}

	// Check the requests inputs
	if req.UserId == "" {
		log.Error().Str("tenant_id", req.TenantId).Float32("num_purchasing", req.Credits).Msg("Invalid (empty) user ID")
		return nil, status.Error(codes.InvalidArgument, "Invalid (empty) user ID")
	}
	if req.TenantId == "" {
		log.Error().Str("augment_user_id", req.UserId).Float32("num_purchasing", req.Credits).Msg("Invalid (empty) tenant ID")
		return nil, status.Error(codes.InvalidArgument, "Invalid (empty) tenant ID")
	}

	// Get Orb Customer ID from User
	billingInfo, err := GetUserBillingInfo(ctx, req.UserId, req.TenantId, s.daoFactory, s.tenantMap)
	if err != nil {
		log.Error().Err(err).Str("augment_user_id", req.UserId).Str("tenant_id", req.TenantId).Float32("num_purchasing", req.Credits).Msg("Failed to get user billing info")
		return nil, status.Error(codes.Internal, "Failed to get user billing info")
	}

	// Perform auth check
	adminNecessary := billingInfo.IsSelfServeTeam // If the user is on a team, they must be an admin to purchase credits
	err = s.teamManagementAuthCheck(ctx, nil, &req.UserId, tokenexchangepb.Scope_AUTH_RW, adminNecessary, "PurchaseCredits")
	if err != nil {
		return nil, err
	}
	// Auth claims have already been checked above, so we know this parsing will succeed.
	authInfo, _ := auth.GetAugmentClaims(ctx)

	// Ensure the user has a payment method saved
	hasPaymentMethod, err := s.stripeClient.HasPaymentMethod(billingInfo.StripeCustomerID)
	log.Info().Bool("has_payment_method", hasPaymentMethod).Str("augment_user_id", req.UserId).Str("stripe_customer_id", billingInfo.StripeCustomerID).Msg("Checked payment method status")
	if err != nil {
		log.Error().Err(err).Str("augment_user_id", req.UserId).Str("stripe_customer_id", billingInfo.StripeCustomerID).Float32("num_purchasing", req.Credits).Msg("Failed to check payment method status")
		return nil, status.Error(codes.Internal, "Failed to check payment method status")
	}
	if !hasPaymentMethod {
		log.Error().Str("augment_user_id", req.UserId).Str("stripe_customer_id", billingInfo.StripeCustomerID).Float32("num_purchasing", req.Credits).Msg("User does not have a payment method")
		return nil, status.Error(codes.FailedPrecondition, "User does not have a payment method")
	}

	// Audit Log
	// TODO(sophie): if we add "reason" or flexible cost basis to purchase credits, add that in audit log
	s.WriteTeamsAuditLog(ctx, fmt.Sprintf("purchasing %f requests for Orb customer %s", req.Credits, billingInfo.OrbCustomerID))

	// Get the user's Orb subscription
	orbSubscription, err := s.orbClient.GetUserSubscription(ctx, billingInfo.OrbSubscriptionID, nil)
	if err != nil {
		log.Error().Err(err).Str("augment_user_id", req.UserId).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to get user subscription")
		return nil, status.Error(codes.Internal, "Failed to get user subscription")
	}

	plan := s.orbConfig.findPlan(orbSubscription.ExternalPlanID)
	if plan == nil {
		log.Error().Str("orb_subscription_id", orbSubscription.OrbSubscriptionID).Str("plan_id", orbSubscription.ExternalPlanID).Msg("Failed to find plan")
		return nil, status.Error(codes.Internal, "Failed to find plan")
	}

	// Ensure the user is not on the trial plan
	if plan.Features.PlanType == PlanTypePaidTrial {
		log.Error().Str("augment_user_id", req.UserId).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("plan_id", orbSubscription.ExternalPlanID).Msg("Cannot purchase credits on a trial plan")
		return nil, status.Error(codes.InvalidArgument, "Cannot purchase credits on a trial plan")
	}

	// Get the cost per message and total cost of the purchase
	costPerMessage := s.orbConfig.CostPerMessage
	totalCost := costPerMessage * float64(req.Credits)

	// Confirm that the spend is within bounds given the message cost and min/max purchase amounts
	if totalCost > s.orbConfig.MaxAddonPurchase {
		log.Error().Str("augment_user_id", req.UserId).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Float32("credits", req.Credits).Msgf("Cannot purchase more than $%f worth of messages at a time", s.orbConfig.MaxAddonPurchase)
		return nil, status.Error(codes.InvalidArgument, fmt.Sprintf("Cannot purchase more than $%.2f worth of messages at a time", s.orbConfig.MaxAddonPurchase))
	}
	if totalCost < s.orbConfig.MinAddonPurchase {
		log.Error().Str("augment_user_id", req.UserId).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Float32("credits", req.Credits).Msgf("Cannot purchase less than $%f worth of messages at a time", s.orbConfig.MinAddonPurchase)
		return nil, status.Error(codes.InvalidArgument, fmt.Sprintf("Cannot purchase less than $%.2f worth of messages at a time", s.orbConfig.MinAddonPurchase))
	}

	// Create an Orb event for the credit purchase
	description := fmt.Sprintf("Purchased messages from Manage Subscription Page on %s by %s", time.Now().Format(time.DateOnly), billingInfo.Email)
	// The credit block should start at the start date of the current billing period per https://docs.withorb.com/self-serve/product-access#unblocking-product-access
	// We need to round the current billing period start date to midnight for the API to accept it
	roundedStartDate := time.Date(orbSubscription.CurrentBillingPeriodStartDate.Year(), orbSubscription.CurrentBillingPeriodStartDate.Month(), orbSubscription.CurrentBillingPeriodStartDate.Day(), 0, 0, 0, 0, orbSubscription.CurrentBillingPeriodStartDate.Location())
	err = SafePurchaseCredits(ctx, s.orbClient, s.daoFactory.GetUserDAO(), req.UserId, orb.OrbCreditPurchase{
		CustomerOrbID: billingInfo.OrbCustomerID,
		NumberCredits: float64(req.Credits),
		Description:   description,
		CostBasis:     costPerMessage,              // cost basis = cost per message
		ExpiryDate:    time.Now().AddDate(1, 0, 0), // these expire in one year
		Currency:      s.orbConfig.PricingUnit,     // ex: "usermessages", "credits" -- the Orb currency
		StartDate:     &roundedStartDate,
	}, req.IdempotencyKey)
	if err != nil {
		log.Error().Err(err).Str("augment_user_id", req.UserId).Str("orb_customer_id", billingInfo.OrbCustomerID).Msg("Failed to purchase credits")
		return nil, status.Error(codes.Internal, "Failed to purchase credits")
	}
	log.Info().Str("augment_user_id", req.UserId).Str("tenant_id", req.TenantId).Str("orb_customer_id", billingInfo.OrbCustomerID).Float32("num_purchasing", req.Credits).Float64("total_cost", totalCost).Msg("Successfully purchased credits")

	s.WriteTeamsAuditLog(ctx, fmt.Sprintf("successfully purchased %f requests for Orb customer %s, at a cost basis of $%.2f per request for a total cost of $%.2f", req.Credits, billingInfo.OrbCustomerID, costPerMessage, totalCost))

	// Record this puchase in Request Insight.
	requestContext, riErr := requestcontext.FromGrpcContext(ctx)
	if riErr != nil {
		log.Warn().Err(riErr).Msg("Failed to get request context")
	} else {
		event := ripublisher.NewSessionEvent()
		event.Event = &ripb.SessionEvent_PurchaseCredits{
			PurchaseCredits: &ripb.PurchaseCredits{
				OrbSubscriptionId: billingInfo.OrbSubscriptionID,
				Credits:           req.Credits,
			},
		}
		riErr := s.requestInsightPublisher.PublishSessionEvent(
			ctx,
			requestContext.RequestSessionId.String(),
			authInfo.GetOpaqueUserID(),
			&ripb.TenantInfo{
				TenantId:   authInfo.TenantID,
				TenantName: authInfo.TenantName,
			}, event)
		if riErr != nil {
			log.Warn().Err(riErr).Msg("Failed to publish PurchaseCredits event")
		}
	}

	return &authpb.PurchaseCreditsResponse{}, nil
}

func (s *TeamManagementServer) CancelSubscription(
	ctx context.Context, req *authpb.CancelSubscriptionRequest,
) (*authpb.CancelSubscriptionResponse, error) {
	// Ensure Orb is enabled
	if s.orbClient == nil || !s.orbConfig.Enabled {
		log.Error().Str("augment_user_id", req.UserId).Str("tenant_id", req.TenantId).Msg("Orb is not enabled")
		return nil, status.Error(codes.Internal, "Orb is not enabled")
	}

	// Check the requests inputs
	if req.UserId == "" {
		log.Error().Str("tenant_id", req.TenantId).Msg("Invalid (empty) user ID")
		return nil, status.Error(codes.InvalidArgument, "Invalid (empty) user ID")
	}
	if req.TenantId == "" {
		log.Error().Str("augment_user_id", req.UserId).Msg("Invalid (empty) tenant ID")
		return nil, status.Error(codes.InvalidArgument, "Invalid (empty) tenant ID")
	}

	// Get Orb Customer and Subscription ID from User's Billing Info
	billingInfo, err := GetUserBillingInfo(ctx, req.UserId, req.TenantId, s.daoFactory, s.tenantMap)
	if err != nil {
		log.Error().Err(err).Str("augment_user_id", req.UserId).Str("tenant_id", req.TenantId).Msg("Failed to get user billing info")
		return nil, status.Error(codes.Internal, "Failed to get user billing info")
	}
	if billingInfo.OrbSubscriptionID == "" {
		log.Error().Str("augment_user_id", req.UserId).Msg("User does not have an Orb subscription ID")
		return nil, status.Error(codes.Internal, "User does not have an active subscription")
	}

	// Perform auth check
	adminNecessary := billingInfo.IsSelfServeTeam // If the user is on a team, they must be an admin to cancel the subscription
	err = s.teamManagementAuthCheck(ctx, nil, &req.UserId, tokenexchangepb.Scope_AUTH_RW, adminNecessary, "CancelSubscription")
	if err != nil {
		log.Error().Err(err).Str("augment_user_id", req.UserId).Str("tenant_id", req.TenantId).Bool("admin_necessary", adminNecessary).Msg("Failed to check permissions")
		return nil, err
	}
	// We know this will succeed because the auth check passed.
	authInfo, _ := auth.GetAugmentClaims(ctx)

	// Audit Log
	s.WriteTeamsAuditLog(ctx, fmt.Sprintf("cancelling subscription %s for user %s", billingInfo.OrbSubscriptionID, req.UserId))

	// Determine cancellation parameters
	cancelTime := orb.PlanChangeEndOfTerm
	if req.CancelImmediately {
		cancelTime = orb.PlanChangeImmediate
	}

	// Cancel the Orb subscription - unschedule any existing cancellation first
	err = SafeCancelOrbSubscription(ctx, s.orbClient, s.daoFactory.GetUserDAO(), req.UserId, billingInfo.OrbSubscriptionID, cancelTime, nil, nil, true)
	if err != nil {
		log.Error().Err(err).
			Str("augment_user_id", req.UserId).
			Str("orb_customer_id", billingInfo.OrbCustomerID).
			Str("orb_subscription_id", billingInfo.OrbSubscriptionID).
			Msg("Failed to cancel Orb subscription")
		return nil, status.Error(codes.Internal, "Failed to cancel Orb subscription")
	}
	log.Info().
		Str("augment_user_id", req.UserId).
		Str("tenant_id", req.TenantId).
		Str("orb_customer_id", billingInfo.OrbCustomerID).
		Str("orb_subscription_id", billingInfo.OrbSubscriptionID).
		Msg("Successfully canceled Orb subscription")

	s.WriteTeamsAuditLog(ctx, fmt.Sprintf("successfully cancelled subscription %s for user %s", billingInfo.OrbSubscriptionID, req.UserId))

	// Record this cancellation in Request Insight.
	requestContext, riErr := requestcontext.FromGrpcContext(ctx)
	if riErr != nil {
		log.Warn().Err(riErr).Msg("Failed to get request context")
	} else {
		event := ripublisher.NewSessionEvent()
		event.Event = &ripb.SessionEvent_CancelSubscription{
			CancelSubscription: &ripb.CancelSubscription{
				OrbSubscriptionId: billingInfo.OrbSubscriptionID,
			},
		}
		riErr := s.requestInsightPublisher.PublishSessionEvent(
			ctx,
			requestContext.RequestSessionId.String(),
			authInfo.GetOpaqueUserID(),
			&ripb.TenantInfo{
				TenantId:   authInfo.TenantID,
				TenantName: authInfo.TenantName,
			}, event)
		if riErr != nil {
			log.Warn().Err(riErr).Msg("Failed to publish CancelSubscription event")
		}
	}

	return &authpb.CancelSubscriptionResponse{}, nil
}

// Checks inputs and gets the user's billing info
func (s *TeamManagementServer) validateUserAndGetBillingInfo(
	ctx context.Context, userID string, scope tokenexchangepb.Scope, adminNecessary bool, apiName string,
) (*UserBillingInfo, error) {
	// Ensure Orb is enabled
	if s.orbClient == nil || !s.orbConfig.Enabled {
		log.Error().Str("augment_user_id", userID).Msg("Orb is not enabled")
		return nil, status.Error(codes.Internal, "Orb is not enabled")
	}

	// Check the requests inputs
	if userID == "" {
		log.Error().Msg("Invalid (empty) user ID")
		return nil, status.Error(codes.InvalidArgument, "Invalid (empty) user ID")
	}

	// Get the tenant ID from the user DAO
	userDAO := s.daoFactory.GetUserDAO()
	user, err := userDAO.Get(ctx, userID)
	if err != nil {
		log.Error().Err(err).Str("augment_user_id", userID).Msg("Failed to get user")
		return nil, status.Error(codes.Internal, "Failed to get user")
	}
	if user == nil {
		log.Error().Str("augment_user_id", userID).Msg("User not found")
		return nil, status.Error(codes.NotFound, "User not found")
	}
	userTenants := user.Tenants
	if len(userTenants) > 1 {
		log.Error().Str("augment_user_id", userID).Msg("User is in multiple tenants")
		return nil, status.Error(codes.Internal, "User is in multiple tenants")
	}
	if len(userTenants) == 0 {
		log.Error().Str("augment_user_id", userID).Msg("User is in 0 tenants")
		return nil, status.Error(codes.Internal, "User is in 0 tenants")
	}

	userTenantID := userTenants[0]
	// Get the tenant to check if it's a legacy self-serve team
	userTenant, err := s.tenantMap.GetTenantByID(userTenantID)
	if err != nil {
		log.Error().Err(err).Str("tenant_id", userTenantID).Msg("Failed to get tenant")
		return nil, status.Error(codes.Internal, "Failed to get tenant")
	}
	if userTenant != nil && tenantutil.IsLegacySelfServeTeamTenant(userTenant) {
		log.Error().Str("augment_user_id", userID).Msg("User is in a legacy self-serve team tenant")
		return nil, status.Error(codes.Internal, "User is in a legacy self-serve team tenant")
	}

	// Get Orb Customer ID from User
	billingInfo, err := GetUserBillingInfo(ctx, userID, userTenants[0], s.daoFactory, s.tenantMap)
	if err != nil {
		log.Error().Err(err).Str("augment_user_id", userID).Msg("Failed to get user billing info")
		return nil, status.Error(codes.Internal, "Failed to get user billing info")
	}

	// Auth check
	// If the action must be performed by an admin, and the user is on a team, then they must be an admin of that team
	err = s.teamManagementAuthCheck(ctx, nil, &userID, scope, (adminNecessary && billingInfo.IsSelfServeTeam), apiName)
	if err != nil {
		return nil, err
	}
	return billingInfo, nil
}

func (s *TeamManagementServer) GetUserOrbInfo(
	ctx context.Context, req *authpb.GetUserOrbInfoRequest,
) (*authpb.GetUserOrbInfoResponse, error) {
	// Get the user's billing info
	billingInfo, err := s.validateUserAndGetBillingInfo(ctx, req.UserId, tokenexchangepb.Scope_AUTH_R, false, "GetUserOrbInfo")
	if err != nil {
		return nil, err
	}

	// Get Orb Subscription Info
	if s.orbClient == nil {
		log.Error().Str("augment_user_id", req.UserId).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Orb client is nil, cannot get subscription info")
		return nil, status.Error(codes.Internal, "Orb client is nil, cannot get subscription info")
	}
	orbSubscriptionInfo, err := s.orbClient.GetUserSubscription(ctx, billingInfo.OrbSubscriptionID, &orb.ItemIds{
		SeatsID:            s.orbConfig.SeatsItemID,
		IncludedMessagesID: s.orbConfig.IncludedMessagesItemID,
	})
	if err != nil {
		log.Error().Err(err).Str("augment_user_id", req.UserId).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to get Orb subscription info")
		return nil, status.Error(codes.Internal, "Failed to get Orb subscription info")
	}

	// Get Orb Credit Info
	creditInfo, err := s.orbClient.GetCustomerCreditInfo(ctx, billingInfo.OrbCustomerID, orbSubscriptionInfo.CurrentBillingPeriodStartDate, s.orbConfig.PricingUnit)
	if err != nil {
		log.Error().Err(err).Str("augment_user_id", req.UserId).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to get Orb credit info")
		return nil, status.Error(codes.Internal, "Failed to get Orb credit info")
	}

	// Get payment failed info
	failedPaymentInfo, err := s.orbClient.GetFailedPaymentInfo(ctx, billingInfo.OrbSubscriptionID)
	if err != nil {
		// Don't fail here, just warn and show that there have been no failed payments
		log.Warn().Err(err).Str("augment_user_id", req.UserId).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to get failed payment info")
	}

	// Check stripe directly for payment method status to avoid staleness due to webhook delays
	hasPaymentMethod, err := s.stripeClient.HasPaymentMethod(billingInfo.StripeCustomerID)
	if err != nil {
		log.Error().Err(err).Str("augment_user_id", req.UserId).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to get payment method status from Stripe")
		return nil, status.Error(codes.Internal, "Failed to get payment method status from Stripe and internal entity")
	}

	// Exclude billing portal url from response for non-admin users on a self-serve team
	portalUrl := orbSubscriptionInfo.PortalUrl
	if billingInfo.IsSelfServeTeam && !billingInfo.IsAdmin {
		portalUrl = ""
	}

	resp := &authpb.GetUserOrbInfoResponse{
		OrbCustomerId:                  billingInfo.OrbCustomerID,
		OrbSubscriptionId:              billingInfo.OrbSubscriptionID,
		PortalUrl:                      portalUrl,
		ExternalPlanId:                 orbSubscriptionInfo.ExternalPlanID,
		BillingPeriodEnd:               orbSubscriptionInfo.CurrentBillingPeriodEndDate.Format(time.RFC3339),
		HasPaymentMethod:               hasPaymentMethod,
		UsageUnitsAvailable:            int32(creditInfo.ActiveCredits),
		UsageUnitsUsedThisBillingCycle: int32(creditInfo.CreditsUsedThisBillingCycle),
		UsageUnitsPending:              int32(creditInfo.PendingCredits),
		NumberOfSeatsThisBillingCycle:  int32(orbSubscriptionInfo.CurrentFixedQuantities.Seats),
		NextBillingCycleAmount:         orbSubscriptionInfo.NextBillingCycleAmount,
		UsageUnitName:                  "user messages",
		SubscriptionStatus:             authpb.GetUserOrbInfoResponse_SubscriptionStatus(authpb.GetUserOrbInfoResponse_SubscriptionStatus_value[strings.ToUpper(orbSubscriptionInfo.OrbStatus)]),
	}

	plan := s.orbConfig.findPlan(orbSubscriptionInfo.ExternalPlanID)
	if plan == nil {
		log.Error().Str("orb_subscription_id", orbSubscriptionInfo.OrbSubscriptionID).Str("plan_id", orbSubscriptionInfo.ExternalPlanID).Msg("Failed to find plan")
		return nil, status.Error(codes.Internal, "Failed to find plan")
	}

	if orbSubscriptionInfo.FutureFixedQuantities != nil {
		if orbSubscriptionInfo.FutureFixedQuantities.Seats == 0 {
			resp.NumberOfSeatsNextBillingCycle = int32(orbSubscriptionInfo.CurrentFixedQuantities.Seats)
		} else {
			resp.NumberOfSeatsNextBillingCycle = int32(orbSubscriptionInfo.FutureFixedQuantities.Seats)
		}
		if orbSubscriptionInfo.FutureFixedQuantities.IncludedMessages == 0 {
			resp.UsageUnitsRenewingEachBillingCycle = int32(orbSubscriptionInfo.CurrentFixedQuantities.IncludedMessages)
		} else {
			resp.UsageUnitsRenewingEachBillingCycle = int32(orbSubscriptionInfo.FutureFixedQuantities.IncludedMessages)
		}
	} else if orbSubscriptionInfo.EndDate != orbSubscriptionInfo.CurrentBillingPeriodEndDate || plan.Features.PlanType == PlanTypePaidTrial {
		// If there is no future fixed quantities, but the subscription will continue next month, then the next billing cycle will be the same as the current billing cycle
		// If we are on the trial period, there is no "next billing cycle", but we still want to show the current seats and credits as the future amount
		resp.NumberOfSeatsNextBillingCycle = int32(orbSubscriptionInfo.CurrentFixedQuantities.Seats)
		resp.UsageUnitsRenewingEachBillingCycle = int32(orbSubscriptionInfo.CurrentFixedQuantities.IncludedMessages)
	}

	if plan.Features.PlanType == PlanTypePaidTrial {
		trialEnd := orbSubscriptionInfo.CurrentBillingPeriodEndDate.Format(time.RFC3339)
		resp.TrialPeriodEnd = &trialEnd
	} else {
		resp.TrialPeriodEnd = nil
	}

	if !orbSubscriptionInfo.EndDate.IsZero() {
		endDate := orbSubscriptionInfo.EndDate.Format(time.RFC3339)
		resp.SubscriptionEndDate = &endDate
	} else {
		resp.SubscriptionEndDate = nil
	}

	if failedPaymentInfo != nil {
		resp.FailedPayment = &authpb.FailedPayment{
			Amount: failedPaymentInfo.Amount,
			Date:   failedPaymentInfo.Date.Format(time.RFC3339),
		}
	}

	return resp, nil
}

func (s *TeamManagementServer) GetUserOrbPlanInfo(
	ctx context.Context, req *authpb.GetUserOrbPlanInfoRequest,
) (*authpb.GetUserOrbPlanInfoResponse, error) {
	billingInfo, err := s.validateUserAndGetBillingInfo(ctx, req.UserId, tokenexchangepb.Scope_AUTH_R, false, "GetUserOrbPlanInfo")
	if err != nil {
		return nil, err
	}

	// Get Orb Info
	if s.orbClient == nil {
		log.Error().Str("augment_user_id", req.UserId).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Orb client is nil, cannot get plan information")
		return nil, status.Error(codes.Internal, "Orb client is nil, cannot get plan information")
	}
	orbPlanInfo, err := s.orbClient.GetPlanInformation(ctx, orb.ItemIds{IncludedMessagesID: s.orbConfig.IncludedMessagesItemID, SeatsID: s.orbConfig.SeatsItemID}, &billingInfo.OrbSubscriptionID, nil)
	if err != nil {
		log.Error().Err(err).Str("augment_user_id", req.UserId).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to get Orb plan info")
		return nil, status.Error(codes.Internal, "Failed to get Orb plan info")
	}

	resp, err := FormatPlan(orbPlanInfo, s.orbConfig)
	if err != nil {
		return nil, err
	}

	return &authpb.GetUserOrbPlanInfoResponse{OrbPlanInfo: resp}, nil
}

// Format into auth format
func FormatPlan(orbPlanInfo *orb.OrbPlanInfo, orbConfig *OrbConfig) (*authpb.OrbPlanInfo, error) {
	planFeatures := orbConfig.GetPlanFeatures(orbPlanInfo.ExternalPlanID)
	if planFeatures == nil {
		log.Error().Str("plan_id", orbPlanInfo.ExternalPlanID).Msg("Unknown plan ID")
		return nil, fmt.Errorf("unknown plan ID: %s", orbPlanInfo.ExternalPlanID)
	}
	planInfo := &authpb.OrbPlanInfo{
		ExternalPlanId:          orbPlanInfo.ExternalPlanID,
		FormattedPlanName:       orbPlanInfo.Name,
		AdditionalUsageUnitCost: fmt.Sprintf("%.2f", orbConfig.CostPerMessage),
		AddUsageAvailable:       planFeatures.Features.AddCreditsAvailable,
		UsageUnitsPerSeat:       float32(orbPlanInfo.MessagesPerSeat),
		TrainingAllowed:         planFeatures.Features.TrainingAllowed,
		TeamsAllowed:            planFeatures.Features.TeamsAllowed,
		MaxNumSeats:             int32(planFeatures.Features.MaxSeats),
		UsageUnitName:           "user messages",
		PricePerSeat:            orbPlanInfo.PricePerSeat,
	}
	return planInfo, nil
}

func (s *TeamManagementServer) GetAllOrbPlans(
	ctx context.Context, req *authpb.GetAllOrbPlansRequest,
) (*authpb.GetAllOrbPlansResponse, error) {
	// Check the requests inputs
	authClaims, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		return nil, status.Error(codes.PermissionDenied, "Invalid context")
	}
	if !authClaims.HasScope(tokenexchangepb.Scope_AUTH_R) {
		log.Error().Msgf("Auth claims do not give have scope %s", tokenexchangepb.Scope_AUTH_R)
		return nil, status.Error(codes.PermissionDenied, "Access denied")
	}

	// Need audit logs if IAP user, even for a read-only API
	iapEmail, isIapUser := authClaims.GetIapEmail()
	if isIapUser {
		s.auditLogger.WriteAuditLog(
			iapEmail,
			authClaims.OpaqueUserIDType,
			authClaims.TenantName,
			fmt.Sprintf("IAP user %s accessing team management API GetAllOrbPlans in tenant %s", iapEmail, authClaims.TenantName),
		)
	}

	// Get all the plans we need to get generic info for
	allResp := &authpb.GetAllOrbPlansResponse{}
	var filteredPlans []string
	for _, plan := range s.orbConfig.Plans {
		// plan.ID is the external_plan_id in Orb
		filteredPlans = append(filteredPlans, plan.ID)
	}

	for _, planID := range filteredPlans {
		orbPlanInfo, err := s.orbClient.GetPlanInformation(ctx, orb.ItemIds{IncludedMessagesID: s.orbConfig.IncludedMessagesItemID, SeatsID: s.orbConfig.SeatsItemID}, nil, &planID)
		if err != nil {
			log.Error().Err(err).Str("plan_id", planID).Msg("Failed to get Orb plan info")
			return nil, status.Error(codes.Internal, "Failed to get Orb plan info")
		}

		orbPlan, err := FormatPlan(orbPlanInfo, s.orbConfig)
		if err != nil {
			return nil, err
		}
		allResp.OrbPlans = append(allResp.OrbPlans, orbPlan)
	}

	return allResp, nil
}

func (s *TeamManagementServer) UnschedulePendingSubscriptionCancellation(
	ctx context.Context, req *authpb.UnschedulePendingSubscriptionCancellationRequest,
) (*authpb.UnschedulePendingSubscriptionCancellationResponse, error) {
	billingInfo, err := s.validateUserAndGetBillingInfo(ctx, req.UserId, tokenexchangepb.Scope_AUTH_RW, true, "UnschedulePendingSubscriptionCancellation")
	if err != nil {
		return nil, err
	}
	// We know this will succeed because the auth check passed inside validateUserAndGetBillingInfo.
	authInfo, _ := auth.GetAugmentClaims(ctx)

	// Get the user's Orb subscription
	orbSubscription, err := s.orbClient.GetUserSubscription(ctx, billingInfo.OrbSubscriptionID, nil)
	if err != nil {
		log.Error().Err(err).Str("augment_user_id", req.UserId).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to get user subscription")
		return nil, status.Error(codes.Internal, "Failed to get user subscription")
	}

	// Audit log
	s.WriteTeamsAuditLog(ctx, fmt.Sprintf("unscheduling pending subscription cancellation for user %s, subscription ID %s", req.UserId, billingInfo.OrbSubscriptionID))

	plan := s.orbConfig.findPlan(orbSubscription.ExternalPlanID)
	if plan == nil {
		log.Error().Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Str("plan_id", orbSubscription.ExternalPlanID).Msg("Failed to find plan")
		return nil, status.Error(codes.Internal, "Failed to find plan")
	}

	// Make sure we're not on the trial plan
	// You shouldn't be able to unschedule cancellation for the trial plan (makes no sense), but guard against this as this would give them free forever
	if plan.Features.PlanType == PlanTypePaidTrial {
		log.Error().Str("augment_user_id", req.UserId).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Cannot unschedule cancellation on a trial plan")
		return nil, status.Error(codes.Internal, "Cannot unschedule cancellation on a trial plan")
	}

	// Check to make sure subscription is scheduled to be cancelled
	if orbSubscription.EndDate.IsZero() {
		log.Error().Str("augment_user_id", req.UserId).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Subscription is not scheduled to be cancelled")
		return nil, status.Error(codes.Internal, "Subscription is not scheduled to be cancelled")
	}

	// Check to make sure subscription is not already cancelled
	if orbSubscription.EndDate.Before(time.Now()) {
		log.Error().Str("augment_user_id", req.UserId).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Subscription is already cancelled")
		return nil, status.Error(codes.Internal, "Subscription is already cancelled")
	}

	// Unschedule the cancellation
	err = SafeUnschedulePendingSubscriptionCancellation(ctx, s.orbClient, s.daoFactory.GetUserDAO(), req.UserId, billingInfo.OrbSubscriptionID, nil)
	if err != nil {
		log.Error().Err(err).Str("augment_user_id", req.UserId).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to unschedule subscription cancellation")
		return nil, status.Error(codes.Internal, "Failed to unschedule subscription cancellation")
	}

	// Audit log on success
	s.WriteTeamsAuditLog(ctx, fmt.Sprintf("successfully unscheduled pending subscription cancellation for user %s, subscription ID %s", req.UserId, billingInfo.OrbSubscriptionID))

	// Record this unschedule cancellation in request insight.
	requestContext, riErr := requestcontext.FromGrpcContext(ctx)
	if riErr != nil {
		log.Warn().Err(riErr).Msg("Failed to get request context")
	} else {
		event := ripublisher.NewSessionEvent()
		event.Event = &ripb.SessionEvent_UnschedulePendingSubscriptionCancellation{
			UnschedulePendingSubscriptionCancellation: &ripb.UnschedulePendingSubscriptionCancellation{
				OrbSubscriptionId: billingInfo.OrbSubscriptionID,
			},
		}
		riErr := s.requestInsightPublisher.PublishSessionEvent(
			ctx,
			requestContext.RequestSessionId.String(),
			authInfo.GetOpaqueUserID(),
			&ripb.TenantInfo{
				TenantId:   authInfo.TenantID,
				TenantName: authInfo.TenantName,
			}, event)
		if riErr != nil {
			log.Warn().Err(riErr).Msg("Failed to publish UnschedulePendingSubscriptionCancellation event")
		}
	}

	return &authpb.UnschedulePendingSubscriptionCancellationResponse{}, nil
}

func (s *TeamManagementServer) UnschedulePlanChanges(
	ctx context.Context, req *authpb.UnschedulePlanChangesRequest,
) (*authpb.UnschedulePlanChangesResponse, error) {
	billingInfo, err := s.validateUserAndGetBillingInfo(ctx, req.UserId, tokenexchangepb.Scope_AUTH_RW, true, "UnschedulePlanChanges")
	if err != nil {
		return nil, err
	}

	// Check if the subscription has scheduled plan changes
	scheduledTargetPlanID, err := s.orbClient.GetScheduledPlanChanges(ctx, billingInfo.OrbSubscriptionID, billingInfo.OrbCustomerID)
	if err != nil {
		log.Error().Err(err).Str("augment_user_id", req.UserId).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to get scheduled plan changes")
		return nil, status.Error(codes.Internal, "Failed to get scheduled plan changes")
	}

	if scheduledTargetPlanID == nil {
		log.Warn().Str("augment_user_id", req.UserId).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("No scheduled plan changes found")
		return nil, status.Error(codes.InvalidArgument, "No scheduled plan changes found")
	}

	// Get auth info for audit logging
	authInfo, _ := auth.GetAugmentClaims(ctx)

	// Unschedule the plan changes
	err = SafeUnschedulePlanChanges(ctx, s.orbClient, s.daoFactory.GetUserDAO(), req.UserId, billingInfo.OrbSubscriptionID, nil)
	if err != nil {
		log.Error().Err(err).Str("augment_user_id", req.UserId).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to unschedule pending plan changes")
		return nil, status.Error(codes.Internal, "Failed to unschedule pending plan changes")
	}

	// Audit log on success
	s.WriteTeamsAuditLog(ctx, fmt.Sprintf("successfully unscheduled pending plan changes for user %s, subscription ID %s", req.UserId, billingInfo.OrbSubscriptionID))

	// Record this unschedule plan changes in request insight.
	requestContext, riErr := requestcontext.FromGrpcContext(ctx)
	if riErr != nil {
		log.Warn().Err(riErr).Msg("Failed to get request context")
	} else {
		event := ripublisher.NewSessionEvent()
		event.Event = &ripb.SessionEvent_UnschedulePlanChanges{
			UnschedulePlanChanges: &ripb.UnschedulePlanChanges{
				OrbSubscriptionId: billingInfo.OrbSubscriptionID,
			},
		}
		riErr := s.requestInsightPublisher.PublishSessionEvent(
			ctx,
			requestContext.RequestSessionId.String(),
			authInfo.GetOpaqueUserID(),
			&ripb.TenantInfo{
				TenantId:   authInfo.TenantID,
				TenantName: authInfo.TenantName,
			}, event)
		if riErr != nil {
			log.Warn().Err(riErr).Msg("Failed to publish UnschedulePlanChanges event")
		}
	}

	return &authpb.UnschedulePlanChangesResponse{}, nil
}

func (s *TeamManagementServer) GetUserOrbCreditsInfo(
	ctx context.Context, req *authpb.GetUserOrbCreditsInfoRequest,
) (*authpb.GetUserOrbCreditsInfoResponse, error) {
	billingInfo, err := s.validateUserAndGetBillingInfo(ctx, req.UserId, tokenexchangepb.Scope_AUTH_R, false, "GetUserOrbCreditsInfo")
	if err != nil {
		return nil, err
	}

	// Get Orb Subscription Info. We need the current billing period start date in order to get how many credits have been used this billing period
	orbSubscription, err := s.orbClient.GetUserSubscription(ctx, billingInfo.OrbSubscriptionID, nil)
	if err != nil {
		log.Error().Err(err).Str("augment_user_id", req.UserId).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to get user subscription")
		return nil, status.Error(codes.Internal, "Failed to get user subscription")
	}

	// Get Orb Credit Info
	creditInfo, err := s.orbClient.GetCustomerCreditInfo(ctx, billingInfo.OrbCustomerID, orbSubscription.CurrentBillingPeriodStartDate, s.orbConfig.PricingUnit)
	if err != nil {
		log.Error().Err(err).Str("augment_user_id", req.UserId).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to get Orb credit info")
		return nil, status.Error(codes.Internal, "Failed to get Orb credit info")
	}

	return &authpb.GetUserOrbCreditsInfoResponse{
		UsageUnitsAvailable:            int32(creditInfo.ActiveCredits),
		UsageUnitsUsedThisBillingCycle: int32(creditInfo.CreditsUsedThisBillingCycle),
		UsageUnitsPending:              int32(creditInfo.PendingCredits),
	}, nil
}

func (s *TeamManagementServer) GetUserOrbPaymentInfo(
	ctx context.Context, req *authpb.GetUserOrbPaymentInfoRequest,
) (*authpb.GetUserOrbPaymentInfoResponse, error) {
	billingInfo, err := s.validateUserAndGetBillingInfo(ctx, req.UserId, tokenexchangepb.Scope_AUTH_R, false, "GetUserOrbPaymentInfo")
	if err != nil {
		return nil, err
	}

	var (
		hasPaymentMethod  bool
		failedPaymentInfo *orb.FailedPaymentInfo
		stripeErr         error
	)

	// Create an error group with the request context
	g, gctx := errgroup.WithContext(ctx)

	// Run Stripe API call to determine if we have a payment method
	g.Go(func() error {
		var err error
		hasPaymentMethod, err = s.stripeClient.HasPaymentMethod(billingInfo.StripeCustomerID)
		if err != nil {
			stripeErr = err
			log.Error().Err(err).Str("augment_user_id", req.UserId).Str("stripe_customer_id", billingInfo.StripeCustomerID).Msg("Failed to get payment method status from Stripe")
			return err
		}
		return nil
	})

	// Run Orb API call to determine if we have any failed payments
	g.Go(func() error {
		var err error
		failedPaymentInfo, err = s.orbClient.GetFailedPaymentInfo(gctx, billingInfo.OrbSubscriptionID)
		if err != nil {
			log.Warn().Err(err).Str("augment_user_id", req.UserId).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to get failed payment info")
			// Don't propagate this error since we don't want it to fail the entire request
			return nil
		}
		return nil
	})

	// Wait for all goroutines to complete
	_ = g.Wait()

	// Handle Stripe error
	if stripeErr != nil {
		return nil, status.Error(codes.Internal, "Failed to get payment method status from Stripe")
	}

	resp := &authpb.GetUserOrbPaymentInfoResponse{
		HasPaymentMethod: hasPaymentMethod,
	}
	if failedPaymentInfo != nil {
		resp.FailedPayment = &authpb.FailedPayment{
			Amount: failedPaymentInfo.Amount,
			Date:   failedPaymentInfo.Date.Format(time.RFC3339),
		}
	}
	return resp, nil
}

func (s *TeamManagementServer) GetUserOrbSubscriptionInfo(
	ctx context.Context, req *authpb.GetUserOrbSubscriptionInfoRequest,
) (*authpb.GetUserOrbSubscriptionInfoResponse, error) {
	billingInfo, err := s.validateUserAndGetBillingInfo(ctx, req.UserId, tokenexchangepb.Scope_AUTH_R, false, "GetUserOrbSubscriptionInfo")
	if err != nil {
		return nil, err
	}

	// If the user has a pending subscription, return pending
	if billingInfo.SubscriptionCreationInfo != nil && billingInfo.SubscriptionCreationInfo.Status == auth_entities.User_SubscriptionCreationInfo_PENDING {
		return &authpb.GetUserOrbSubscriptionInfoResponse{
			OrbSubscriptionInfo: &authpb.GetUserOrbSubscriptionInfoResponse_PendingSubscription{
				PendingSubscription: &authpb.PendingOrbSubscription{},
			},
		}, nil
	}

	// No subscription ID, but subscription is not pending. Return nonexistent subscription.
	if billingInfo.OrbSubscriptionID == "" {
		return &authpb.GetUserOrbSubscriptionInfoResponse{
			OrbSubscriptionInfo: &authpb.GetUserOrbSubscriptionInfoResponse_NonexistentSubscription{
				NonexistentSubscription: &authpb.NoSubscription{},
			},
		}, nil
	}

	var (
		orbSubscription       *orb.OrbSubscriptionInfo
		orbPlanInfo           *orb.OrbPlanInfo
		scheduledTargetPlanID *string
		subscriptionErr       error
		planInfoErr           error
		scheduledPlanErr      error
	)
	g, gctx := errgroup.WithContext(ctx)

	// Get Orb Subscription Info
	g.Go(func() error {
		var err error
		orbSubscription, err = s.orbClient.GetUserSubscription(gctx, billingInfo.OrbSubscriptionID, &orb.ItemIds{
			SeatsID:            s.orbConfig.SeatsItemID,
			IncludedMessagesID: s.orbConfig.IncludedMessagesItemID,
		})
		if err != nil {
			subscriptionErr = err
			return err
		}
		return nil
	})

	// Get Orb Plan Info
	g.Go(func() error {
		var err error
		orbPlanInfo, err = s.orbClient.GetPlanInformation(gctx, orb.ItemIds{
			IncludedMessagesID: s.orbConfig.IncludedMessagesItemID,
			SeatsID:            s.orbConfig.SeatsItemID,
		}, &billingInfo.OrbSubscriptionID, nil)
		if err != nil {
			planInfoErr = err
			return err
		}
		return nil
	})

	// Get Scheduled Plan Changes
	g.Go(func() error {
		var err error
		scheduledTargetPlanID, err = s.orbClient.GetScheduledPlanChanges(gctx, billingInfo.OrbSubscriptionID, billingInfo.OrbCustomerID)
		if err != nil {
			scheduledPlanErr = err
			return err
		}
		return nil
	})

	// Wait for all goroutines to complete
	if err := g.Wait(); err != nil {
		// Return the specific error message based on which call failed
		if subscriptionErr != nil {
			log.Error().Err(err).Str("augment_user_id", req.UserId).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to get user subscription")
			return nil, status.Error(codes.Unknown, "Failed to get user subscription")
		}
		if planInfoErr != nil {
			log.Error().Err(err).Str("augment_user_id", req.UserId).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to get Orb plan info")
			return nil, status.Error(codes.Unknown, "Failed to get plan info")
		}
		if scheduledPlanErr != nil {
			log.Error().Err(err).Str("augment_user_id", req.UserId).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to get scheduled plan changes")
			return nil, status.Error(codes.Unknown, "Failed to get scheduled plan changes")
		}
		return nil, status.Error(codes.Unknown, "Failed to get information")
	}

	// If no Orb subscription, return null
	if orbSubscription == nil {
		return &authpb.GetUserOrbSubscriptionInfoResponse{
			OrbSubscriptionInfo: &authpb.GetUserOrbSubscriptionInfoResponse_NonexistentSubscription{
				NonexistentSubscription: &authpb.NoSubscription{},
			},
		}, nil
	}

	// Exclude billing portal url from response for non-admin users on a self-serve team
	portalUrl := orbSubscription.PortalUrl
	if billingInfo.IsSelfServeTeam && !billingInfo.IsAdmin {
		portalUrl = ""
	}

	// Format the response
	subscription := &authpb.OrbSubscriptionInfo{
		OrbCustomerId:           billingInfo.OrbCustomerID,
		OrbSubscriptionId:       billingInfo.OrbSubscriptionID,
		PortalUrl:               portalUrl,
		ExternalPlanId:          orbSubscription.ExternalPlanID,
		NextBillingCycleAmount:  orbSubscription.NextBillingCycleAmount,
		BillingPeriodEndDateIso: orbSubscription.CurrentBillingPeriodEndDate.Format(time.RFC3339),
		SubscriptionStatus:      authpb.OrbSubscriptionInfo_SubscriptionStatus(authpb.OrbSubscriptionInfo_SubscriptionStatus_value[strings.ToUpper(orbSubscription.OrbStatus)]),
	}
	endDate := orbSubscription.EndDate.Format(time.RFC3339)
	if orbSubscription.ExternalPlanID == s.orbConfig.getTrialPlan().ID {
		subscription.TrialPeriodEndDateIso = &endDate
	}
	if !orbSubscription.EndDate.IsZero() {
		subscription.SubscriptionEndDateIso = &endDate
		subscription.SubscriptionEndAtBillingCycleEnd = orbSubscription.EndDate.Equal(orbSubscription.CurrentBillingPeriodEndDate)
	}

	// Set the number of seats and monthly cost
	pricePerSeat, err := strconv.ParseFloat(orbPlanInfo.PricePerSeat, 64)
	if err != nil {
		log.Error().Err(err).Str("augment_user_id", req.UserId).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to parse price per seat")
		return nil, status.Error(codes.Internal, "Failed to parse price per seat")
	}
	if orbSubscription.FutureFixedQuantities != nil && orbSubscription.FutureFixedQuantities.Seats > 0 {
		// If we have a future number of seats set that is nonzero and different from current seats, use it
		subscription.Seats = int32(orbSubscription.FutureFixedQuantities.Seats)
		subscription.MonthlyTotalCost = fmt.Sprintf("%.2f", float64(orbSubscription.FutureFixedQuantities.Seats)*pricePerSeat)
	} else {
		// Otherwise, the current number of seats will continue
		subscription.Seats = int32(orbSubscription.CurrentFixedQuantities.Seats)
		subscription.MonthlyTotalCost = fmt.Sprintf("%.2f", float64(orbSubscription.CurrentFixedQuantities.Seats)*pricePerSeat)
	}

	// Set the renewing number of usage units
	if orbSubscription.FutureFixedQuantities != nil && orbSubscription.FutureFixedQuantities.IncludedMessages > 0 {
		// If we have a future number of included messages set that is nonzero, set it
		subscription.UsageUnitsRenewingEachBillingCycle = int32(orbSubscription.FutureFixedQuantities.IncludedMessages)
	} else if !subscription.SubscriptionEndAtBillingCycleEnd {
		// If the subscription is not ending at billing cycle end, then the current number of included messages will continue
		subscription.UsageUnitsRenewingEachBillingCycle = int32(orbSubscription.CurrentFixedQuantities.IncludedMessages)
	} else {
		subscription.UsageUnitsRenewingEachBillingCycle = 0
	}

	// Set scheduled plan change information.
	// Right now we only schedule plan changes either immediately or at the end of the billing period
	// So we are not returning effective dates for now
	if scheduledTargetPlanID != nil {
		subscription.ScheduledTargetPlanId = scheduledTargetPlanID
		// We need to set the future invoice amount manually, as there is no future invoice when you are scheduled to go down in plans
		targetPlan := s.orbConfig.findPlan(*scheduledTargetPlanID)
		if targetPlan != nil {
			planInfo, err := s.orbClient.GetPlanInformation(ctx, orb.ItemIds{IncludedMessagesID: s.orbConfig.IncludedMessagesItemID, SeatsID: s.orbConfig.SeatsItemID}, nil, scheduledTargetPlanID)
			if err != nil {
				log.Error().Err(err).Str("augment_user_id", req.UserId).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to get target plan info")
			} else {
				targetPricePerSeat, err := strconv.ParseFloat(planInfo.PricePerSeat, 64)
				if err != nil {
					log.Error().Err(err).Str("augment_user_id", req.UserId).Str("orb_customer_id", billingInfo.OrbCustomerID).Str("orb_subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to parse target plan price per seat")
				} else {
					subscription.NextBillingCycleAmount = fmt.Sprintf("%.2f", targetPricePerSeat*float64(subscription.Seats))
				}
			}
		}
	}

	// Set the current number of usage units -- the number of seats * the number of messages per seat
	subscription.UsageUnitsIncludedThisBillingCycle = subscription.Seats * int32(orbPlanInfo.MessagesPerSeat)

	// Fetch internal subscription data to get payment failure information
	subscriptionDAO := s.daoFactory.GetSubscriptionDAO()
	internalSubscription, err := subscriptionDAO.Get(ctx, billingInfo.OrbSubscriptionID)
	if err != nil {
		log.Error().Err(err).Str("subscription_id", billingInfo.OrbSubscriptionID).Msg("Failed to get internal subscription data")
		subscription.CancelledDueToPaymentFailure = false
	} else if internalSubscription != nil {
		subscription.CancelledDueToPaymentFailure = internalSubscription.CancelledDueToPaymentFailure
	}

	resp := &authpb.GetUserOrbSubscriptionInfoResponse{
		OrbSubscriptionInfo: &authpb.GetUserOrbSubscriptionInfoResponse_Subscription{
			Subscription: subscription,
		},
	}

	return resp, nil
}

func (s *TeamManagementServer) GetTenantPlanStatus(
	ctx context.Context, req *authpb.GetTenantPlanStatusRequest,
) (*authpb.GetTenantPlanStatusResponse, error) {
	if !s.teamManagementEnabled() {
		log.Warn().Msgf("Team management is disabled for GetTenantPlanStatus")
		return nil, status.Error(codes.Internal, "Disabled")
	}

	// Validate request
	if req.TenantId == "" {
		log.Error().Msg("TenantId is required for GetTenantPlanStatus")
		return nil, status.Error(codes.InvalidArgument, "TenantId is required")
	}

	// Auth check: Ensure the caller has read access to the specified tenant.
	err := s.teamManagementAuthCheck(ctx, &req.TenantId, nil, tokenexchangepb.Scope_AUTH_R, false, "GetTenantPlanStatus")
	if err != nil {
		return nil, err
	}

	log.Info().Str("tenant_id", req.TenantId).Msg("Fetching tenant plan status")

	tenantSubscriptionMappingDAO := s.daoFactory.GetTenantSubscriptionMappingDAO()
	mapping, err := tenantSubscriptionMappingDAO.Get(ctx, req.TenantId)
	if err != nil {
		log.Error().Err(err).Str("tenant_id", req.TenantId).Msg("Failed to fetch tenant subscription mapping")
		return nil, status.Error(codes.Internal, "Failed to fetch tenant subscription mapping")
	}

	if mapping == nil {
		log.Warn().Str("tenant_id", req.TenantId).Msg("Tenant subscription mapping not found")
		return &authpb.GetTenantPlanStatusResponse{
			IsPending: false,
		}, nil
	}

	resp := &authpb.GetTenantPlanStatusResponse{
		IsPending: false, // Default to not pending
	}

	if mapping.PlanChange != nil {
		resp.IsPending = true
		if mapping.PlanChange.TargetOrbPlanId != "" {
			resp.PendingTargetPlanId = &mapping.PlanChange.TargetOrbPlanId
		}
		if mapping.PlanChange.CreatedAt != nil {
			resp.PlanChangeCreatedAt = mapping.PlanChange.CreatedAt
		}
		log.Info().Str("tenant_id", req.TenantId).Msg("Tenant plan change is pending")
	} else {
		log.Info().Str("tenant_id", req.TenantId).Msg("No pending tenant plan change found")
	}

	return resp, nil
}
