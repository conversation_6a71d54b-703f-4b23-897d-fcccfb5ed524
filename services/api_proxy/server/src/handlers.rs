use serde::{Deserialize, Deserializer, Serialize, Serializer};
use std::collections::HashMap;
use std::convert::TryFrom;
use std::sync::Arc;
use std::time::Duration;
use std::time::Instant;
use std::time::SystemTime;

use crate::handler_utils::gate_on_circuit_breaker;
use crate::handler_utils::BATCH_UPLOAD_BLOB_CONTENT_TIMEOUT_MS_FLAG;
use crate::handler_utils::CHECKPOINT_BLOBS_TIMEOUT_MS_FLAG;
use crate::handler_utils::FIND_MISSING_BLOBS_TIMEOUT_MS_FLAG;
use crate::handler_utils::UPLOAD_BLOB_CONTENT_TIMEOUT_MS_FLAG;
use crate::public_api_proto::get_models_response::UserTier;
use crate::request_insight_util::extract_request_metadata;
use actix_web::{web, HttpMessage, HttpRequest, HttpResponse};
use awc::{Client as AwcClient, Connector as AwcConnector};
use futures::future;
use futures::Future;
use model_registry::ModelRegistry;
use request_context::{RequestContext, RequestId, RequestSessionId, TenantInfo};
use request_insight_publisher::request_insight;
use request_insight_publisher::to_tenant_info_proto;
use secrecy::{SecretString, SecretVec};
use sha2::{Digest, Sha256};
use tokio::sync::mpsc::Receiver;
use tracing_actix_web::RootSpan;
use url::Url;
use uuid::Uuid;

use crate::api_auth::User;
use crate::augment::model_instance_config::model_instance_config::ModelConfig::{
    Chat, Edit, Inference, NextEdit,
};
use crate::augment::model_instance_config::Language;
use crate::augment::model_instance_config::ModelType;
use crate::base::blob_names as blob_names_proto;
use crate::chat::{
    FindMissingRequest as ChatFindMissingRequest, FindMissingResponse as ChatFindMissingResponse,
};
use crate::completion::{
    FindMissingRequest as CompletionFindMissingRequest,
    FindMissingResponse as CompletionFindMissingResponse, ReplacementText,
};
use crate::config::Config;
use crate::edit::{
    self, EditPosition, EditRequest, EditResponse, Exchange as EditExchange, InstructionRequest,
    InstructionResponse, ReplaceText,
};
use crate::generation_clients::Client;

use crate::handler_utils::{
    convert_blobs_and_names, get_model, handle_response, request_context_from_req, retry,
    status_to_response, streaming_http_response_from_receiver, EndpointHandler, Handler,
    RetryPolicy,
};
use crate::metrics::{CLIENT_LATENCY_COLLECTOR, CLIENT_METRIC_COLLECTOR, HANDLER_RETRY_COLLECTOR};
use crate::next_edit;
use content_manager_client::{ContentManagerClient, UploadContent};
use feature_flags::FeatureFlagProvider;

use crate::model_registry;
use crate::model_registry::Model;
use crate::public_api_proto;
use crate::public_api_proto::{
    BatchUploadRequest, BatchUploadResponse, ChatFeedback, ChatFeedbackResponse,
    CheckpointBlobsRequest, CheckpointBlobsResponse, ClientCompletionTimelineRequest,
    ClientCompletionTimelineResponse, ClientMetricsRequest, ClientMetricsResponse,
    CompletionFeedback, CompletionFeedbackResponse, CompletionResolution, EditResolution,
    GetSubscriptionInfoRequest, GetSubscriptionInfoResponse, InstructionResolution,
    InstructionResolutionResponse, MemorizeRequest, MemorizeResponse, NextEditFeedback,
    NextEditFeedbackResponse, NextEditResolutionBatch, NextEditResolutionResponse,
    NextEditSessionEventBatch, NextEditSessionEventResponse, OnboardingSessionEventBatch,
    OnboardingSessionEventResponse, PreferenceSample, PreferenceSampleResponse,
    RecordRequestEventsRequest, RecordRequestEventsResponse, RecordSessionEventsRequest,
    RecordSessionEventsResponse, ReportErrorRequest, ReportErrorResponse,
    ReportFeatureVectorRequest, ReportFeatureVectorResponse, ResolveCompletions,
    ResolveEditResponse, SaveChatRequest, SaveChatResponse, SmartPasteResolution,
    SmartPasteResolutionResponse,
};
use crate::request_insight_util::RequestInsightPublisher;
use blob_names::BlobName;
use share_client::proto::share::ChatExchange;

const MAX_MEMORIZE_SIZE_BYTES: u32 = 128 * 1024;

pub const MODEL_FLAG: feature_flags::StringFlag = feature_flags::StringFlag::new("model", "");

pub const EDIT_MODEL_FLAG: feature_flags::StringFlag =
    feature_flags::StringFlag::new("edit_model", "");

pub const CHAT_RAW_OUTPUT_MODEL_FLAG: feature_flags::StringFlag =
    feature_flags::StringFlag::new("chat_raw_output_model", "");

pub const INSTRUCTION_MODEL_FLAG: feature_flags::StringFlag =
    feature_flags::StringFlag::new("instruction_model", "");

pub const INSTRUCTION_FALLBACK_MODELS_FLAG: feature_flags::StringFlag =
    feature_flags::StringFlag::new("instruction_fallback_models", "");

pub const SMART_PASTE_MODEL_FLAG: feature_flags::StringFlag =
    feature_flags::StringFlag::new("smart_paste_model", "");

pub const NEXT_EDIT_MODEL_FLAG: feature_flags::StringFlag =
    feature_flags::StringFlag::new("next_edit_model", "");

pub const CB_FIND_MISSING: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_circuit_breaker_find_missing", false);

pub const CB_CHECKPOINT_BLOBS: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_circuit_breaker_checkpoint_blobs", false);

pub const CB_CLIENT_METRICS: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_circuit_breaker_client_metrics", false);

pub const CB_REPORT_ERROR: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_circuit_breaker_report_error", false);

pub const CB_REPORT_FEATURE_VECTOR: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_circuit_breaker_report_feature_vector", false);

pub const ENABLE_PROMPT_ENHANCER: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("enable_prompt_enhancer", false);

pub const CB_RECORD_REQUEST_EVENTS: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_circuit_breaker_record_request_events", false);

pub const CB_RECORD_SESSION_EVENTS: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_circuit_breaker_record_session_events", false);

pub const CB_RECORD_USER_EVENTS: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_circuit_breaker_record_user_events", false);

pub const CB_PREFERENCE_SAMPLE: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_circuit_breaker_preference_sample", false);

pub const CB_EDIT_RESOLUTION: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_circuit_breaker_edit_resolution", false);

pub const CB_INSTRUCTION_RESOLUTION: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_circuit_breaker_instruction_resolution", false);

pub const CB_SMART_PASTE_RESOLUTION: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_circuit_breaker_smart_paste_resolution", false);

pub const CB_NEXT_EDIT_RESOLUTION: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_circuit_breaker_next_edit_resolution", false);

pub const CB_NEXT_EDIT_USER_EVENT: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_circuit_breaker_next_edit_user_event", false);

pub const CB_ONBOARDING_SESSION_EVENT: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_circuit_breaker_onboarding_session_event", false);

pub const CB_RESOLVE_COMPLETIONS: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_circuit_breaker_resolve_completions", false);

pub const CB_CHAT_FEEDBACK: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_circuit_breaker_chat_feedback", false);

pub const CB_NEXT_EDIT_FEEDBACK: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_circuit_breaker_next_edit_feedback", false);

pub const CB_COMPLETION_FEEDBACK: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_circuit_breaker_completion_feedback", false);

pub const CB_EDIT: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_circuit_breaker_edit", false);

pub const CB_MEMORIZE: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_circuit_breaker_memorize", false);

pub const CB_BATCH_UPLOAD_BLOB: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_circuit_breaker_batch_upload_blob", false);

pub const CB_GET_MODELS: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_circuit_breaker_get_models", false);

pub const CB_GET_SUBSCRIPTION_INFO: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_circuit_breaker_get_subscription_info", false);

pub const FIND_MISSING_THROTTLE_FLAG: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_throttle_find_missing", false);

// Defaults: 500k tokens (blobs -- a very large repo) with a 1k fill per second.
// Should be sufficient for standard usage but prevent the case of opening multiple large repos.
// See handler_utils for a more detailed description of how this works.
pub const FIND_MISSING_THROTTLE_FILL_RATE_PER_SEC_FLAG: feature_flags::FloatFlag =
    feature_flags::FloatFlag::new("api_proxy_throttle_find_missing_fill_rate", 1000.0);

pub const FIND_MISSING_THROTTLE_CAPACITY_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("api_proxy_throttle_find_missing_capacity", 500000);

pub const CHECKPOINT_BLOBS_THROTTLE_FLAG: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_throttle_checkpoint_blobs", false);

pub const CHECKPOINT_BLOBS_THROTTLE_FILL_RATE_PER_SEC_FLAG: feature_flags::FloatFlag =
    feature_flags::FloatFlag::new("api_proxy_checkpoint_blobs_fill_rate", 1.0);

pub const CHECKPOINT_BLOBS_THROTTLE_CAPACITY_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("api_proxy_throttle_checkpoint_blobs_capacity", 1024);

pub const UPLOAD_THROTTLE_FLAG: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_throttle_upload", false);

pub const UPLOAD_THROTTLE_FILL_RATE_PER_SEC_FLAG: feature_flags::FloatFlag =
    feature_flags::FloatFlag::new("api_proxy_throttle_upload_fill_rate", 1000.0);

pub const UPLAOD_THROTTLE_CAPACITY_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("api_proxy_throttle_upload_capacity", 500000);

pub const BLOCKLISTED_CHECKPOINT_IDS_FLAG: feature_flags::StringFlag =
    feature_flags::StringFlag::new("api_proxy_blocklisted_checkpoint_ids", "");

impl From<&public_api_proto::ReplacementText> for ReplacementText {
    fn from(from: &public_api_proto::ReplacementText) -> ReplacementText {
        ReplacementText {
            blob_name: from.blob_name.to_string(),
            path: from.path.to_string(),
            char_start: from.char_start,
            char_end: from.char_end,
            replacement_text: from.replacement_text.to_string(),
            present_in_blob: from.present_in_blob,
            expected_blob_name: from.expected_blob_name.as_ref().map(|n| n.to_string()),
        }
    }
}

impl TryFrom<public_api_proto::FileEditEvent>
    for request_insight_publisher::base::diff_utils::GranularEditEvent
{
    type Error = tonic::Status;
    fn try_from(event: public_api_proto::FileEditEvent) -> Result<Self, Self::Error> {
        Ok(
            request_insight_publisher::base::diff_utils::GranularEditEvent {
                path: event.path.clone(),
                before_blob_name: event.before_blob_name.clone(),
                after_blob_name: event.after_blob_name.clone(),
                edits: event
                    .edits
                    .into_iter()
                    .map(
                        |e| request_insight_publisher::base::diff_utils::SingleEdit {
                            before_start: e.before_start,
                            after_start: e.after_start,
                            before_text: e.before_text.clone(),
                            after_text: e.after_text.clone(),
                        },
                    )
                    .collect(),
            },
        )
    }
}

impl TryFrom<(public_api_proto::EditRequest, &String)> for EditRequest {
    type Error = tonic::Status;

    /// convert the frontend edit request into the modelhost api
    fn try_from(
        from: (public_api_proto::EditRequest, &String),
    ) -> Result<edit::EditRequest, tonic::Status> {
        let (req, model_name) = from;
        let position: Option<EditPosition> = match (req.prefix_begin, req.suffix_end) {
            (Some(prefix_begin), Some(suffix_end)) => Some(EditPosition {
                prefix_begin,
                suffix_end,
                blob_name: req.blob_name.unwrap_or_default().to_string(),
            }),
            _ => None,
        };

        // When converting the frontend request to the backend request, if
        // there is a workspace specified in delta format, try to convert it
        // into the proto version. This may fail if the blob ids are not
        // valid hex strings.
        #[allow(deprecated)]
        let legacy_blob_names: Vec<BlobName> = req
            .blob_names
            .iter()
            .map(|m| BlobName::new(m).unwrap())
            .collect();
        let blobs = convert_blobs_and_names(&req.blobs, Some(&legacy_blob_names))?;

        #[allow(deprecated)]
        Ok(edit::EditRequest {
            model_name: model_name.clone(),
            prefix: req.prefix.clone(),
            suffix: req.suffix.clone(),
            path: req.path.map_or("".to_string(), |s| s.clone()),
            instruction: req.instruction.clone(),
            selected_text: req.selected_text.clone(),
            blobs: Some(blobs),
            lang: req.lang.unwrap_or_default(),
            position,
            sequence_id: req.sequence_id.unwrap_or(0),
        })
    }
}

impl From<EditResponse> for public_api_proto::EditResponse {
    /// transfer from a completion response to the public API completion response.
    fn from(resp: EditResponse) -> Self {
        public_api_proto::EditResponse {
            text: resp.text.clone(),
            unknown_blob_names: resp.unknown_blob_names.clone(),
            checkpoint_not_found: resp.checkpoint_not_found,
        }
    }
}

impl TryFrom<public_api_proto::InstructionRequest> for InstructionRequest {
    type Error = tonic::Status;

    /// convert the frontend instruction request into the modelhost api
    fn try_from(
        req: public_api_proto::InstructionRequest,
    ) -> Result<edit::InstructionRequest, tonic::Status> {
        let position: Option<EditPosition> = match (req.prefix_begin, req.suffix_end) {
            (Some(prefix_begin), Some(suffix_end)) => Some(EditPosition {
                prefix_begin,
                suffix_end,
                blob_name: req.blob_name.unwrap_or_default().to_string(),
            }),
            _ => None,
        };

        let blobs = convert_blobs_and_names(&req.blobs, None)?;
        let chat_history = req
            .chat_history
            .into_iter()
            .map(|e| EditExchange {
                request_message: e.request_message,
                response_text: e.response_text,
                request_id: e.request_id,
            })
            .collect();

        #[allow(deprecated)]
        Ok(edit::InstructionRequest {
            model_name: req.model.clone().unwrap_or_default(),
            prefix: req.prefix.clone(),
            suffix: req.suffix.clone(),
            path: req.path.map_or("".to_string(), |s| s.clone()),
            instruction: req.instruction.clone(),
            selected_text: req.selected_text.clone(),
            code_block: req.code_block.clone(),
            blobs: Some(blobs),
            lang: req.lang.unwrap_or_default(),
            position,
            sequence_id: req.sequence_id.unwrap_or(0),
            chat_history,
            target_file_path: req.target_file_path.clone(),
            target_file_content: req.target_file_content.clone(),
            context_code_exchange_request_id: req.context_code_exchange_request_id.clone(),
            user_guidelines: req.user_guidelines.clone(),
            workspace_guidelines: req.workspace_guidelines.clone(),
        })
    }
}

impl From<InstructionResponse> for public_api_proto::InstructionResponse {
    /// transfer from an instruction response to the public API instruction response.
    fn from(resp: InstructionResponse) -> Self {
        let replace_text: Option<ReplaceText> = resp.replace_text;
        let replacement_text: Option<String> = replace_text
            .clone()
            .and_then(|r: ReplaceText| r.text.clone());
        let replacement_old_text: Option<String> = replace_text
            .clone()
            .and_then(|r: ReplaceText| r.old_text.clone());
        let replacement_start_line: Option<u32> =
            replace_text.clone().and_then(|r: ReplaceText| r.start_line);
        let replacement_end_line: Option<u32> =
            replace_text.clone().and_then(|r: ReplaceText| r.end_line);
        let replacement_sequence_id: Option<u32> = replace_text.map(|r: ReplaceText| r.sequence_id);
        public_api_proto::InstructionResponse {
            text: resp.text.clone(),
            unknown_blob_names: resp.unknown_blob_names.clone(),
            checkpoint_not_found: resp.checkpoint_not_found,
            replacement_text,
            replacement_start_line,
            replacement_end_line,
            replacement_old_text,
            replacement_sequence_id,
        }
    }
}

fn get_feature_flags(
    config: &Config,
    feature_flags: &feature_flags::FeatureFlagsServiceHandle,
) -> public_api_proto::get_models_response::FeatureFlags {
    #[allow(deprecated)]
    let frontend_feature_flags = public_api_proto::get_models_response::FeatureFlags {
        enable_chat: Some(true),
        enable_instructions: Some(true),
        enable_smart_paste: feature_flags.lookup("enable_smart_paste", false),
        enable_smart_paste_min_version: feature_flags.lookup("enable_smart_paste_min_version", ""),
        enable_prompt_enhancer: Some(ENABLE_PROMPT_ENHANCER.get_from(feature_flags)),
        intellij_prompt_enhancer_enabled: feature_flags
            .lookup("intellij_prompt_enhancer_enabled", false),
        enable_view_text_document: Some(true),
        additional_chat_models: feature_flags.lookup("additional_chat_models", ""),
        enable_data_collection: Some(config.enable_client_data_collection),
        small_sync_threshold: feature_flags.lookup("small_sync_threshold", 15),
        big_sync_threshold: feature_flags.lookup("big_sync_threshold", 1000),
        enable_workspace_manager_ui: feature_flags.lookup("enable_workspace_manager_ui", false),
        enable_intellij_chat: feature_flags.lookup("enable_intellij_chat", false),
        enable_external_sources_in_chat: Some(true),
        bypass_language_filter: feature_flags.lookup("bypass_language_filter", true),
        enable_workspace_manager_ui_launch: feature_flags
            .lookup("enable_workspace_manager_ui_launch", false),
        intellij_chat_min_version: feature_flags.lookup("intellij_chat_min_version", ""),
        enable_hindsight: feature_flags.lookup("enable_hindsight", false),
        max_upload_size_bytes: feature_flags
            .lookup("max_upload_size_bytes", MAX_MEMORIZE_SIZE_BYTES as i64),
        intellij_force_completion_min_version: feature_flags
            .lookup("intellij_force_completion_min_version", ""),
        vscode_next_edit_min_version: feature_flags.lookup("vscode_next_edit_min_version", ""),
        vscode_next_edit_bottom_panel_min_version: feature_flags
            .lookup("vscode_next_edit_bottom_panel_min_version", ""),
        vscode_next_edit_ux1_max_version: feature_flags
            .lookup("vscode_next_edit_ux1_max_version", ""),
        vscode_next_edit_ux2_max_version: feature_flags
            .lookup("vscode_next_edit_ux2_max_version", ""),
        vscode_flywheel_min_version: feature_flags.lookup("vscode_flywheel_min_version", ""),
        vscode_external_sources_in_chat_min_version: feature_flags
            .lookup("vscode_external_sources_in_chat_min_version", ""),
        vscode_share_min_version: feature_flags.lookup("vscode_share_min_version", ""),
        intellij_share_min_version: feature_flags.lookup("intellij_share_min_version", ""),
        max_trackable_file_count: feature_flags.lookup("max_trackable_file_count", 250000),
        max_trackable_file_count_without_permission: feature_flags
            .lookup("max_trackable_file_count_without_permission", 150000),
        min_uploaded_percentage_without_permission: feature_flags
            .lookup("min_uploaded_percentage_without_permission", 90),
        // Mmust keep these until all extensions are 0.270.0 or later
        enable_code_edits: Some(true),
        checkpoint_blobs_v2: Some(true),
        vscode_chat_hint_decoration_min_version: feature_flags
            .lookup("vscode_chat_hint_decoration_min_version", ""),
        next_edit_debounce_ms: feature_flags.lookup("next_edit_debounce_ms", 500),
        enable_completion_file_edit_events: feature_flags
            .lookup("enable_completion_file_edit_events", false),
        vscode_sources_min_version: feature_flags.lookup("vscode_sources_min_version", ""),
        vscode_enable_cpu_profile: feature_flags.lookup("vscode_enable_cpu_profile", false),
        verify_folder_is_source_repo: feature_flags.lookup("verify_folder_is_source_repo", false),
        refuse_to_sync_home_directories: feature_flags
            .lookup("refuse_to_sync_home_directories", false),
        enable_file_limits_for_syncing_permission: feature_flags
            .lookup("enable_file_limits_for_syncing_permission", false),
        enable_chat_mermaid_diagrams: feature_flags.lookup("enable_chat_mermaid_diagrams", false),
        enable_summary_titles: feature_flags.lookup("enable_summary_titles", false),
        smart_paste_precompute_mode: feature_flags
            .lookup("smart_paste_precompute_mode", "visible-hover"),
        vscode_new_threads_menu_min_version: feature_flags
            .lookup("vscode_new_threads_menu_min_version", ""),
        intellij_new_threads_menu_min_version: feature_flags
            .lookup("intellij_new_threads_menu_min_version", ""),
        intellij_show_summary: feature_flags.lookup("intellij_show_summary", false),
        vscode_editable_history_min_version: feature_flags
            .lookup("vscode_editable_history_min_version", ""),
        enable_guidelines: feature_flags.lookup("enable_guidelines", false),
        vscode_use_checkpoint_manager_context_min_version: feature_flags
            .lookup("vscode_use_checkpoint_manager_context_min_version", ""),
        vscode_validate_checkpoint_manager_context: feature_flags
            .lookup("vscode_validate_checkpoint_manager_context", false),
        vscode_enable_chat_mermaid_diagrams_min_version: feature_flags
            .lookup("vscode_enable_chat_mermaid_diagrams_min_version", ""),
        vscode_deprecated_version: feature_flags.lookup("vscode_deprecated_version", ""),
        intellij_deprecated_version: feature_flags.lookup("intellij_deprecated_version", ""),
        intellij_completions_history_min_version: feature_flags
            .lookup("intellij_completions_history_min_version", ""),
        intellij_smart_paste_min_version: feature_flags
            .lookup("intellij_smart_paste_min_version", ""),
        vscode_design_system_rich_text_editor_min_version: feature_flags
            .lookup("vscode_design_system_rich_text_editor_min_version", ""),
        allow_client_feature_flag_overrides: feature_flags
            .lookup("allow_client_feature_flag_overrides", false),
        vscode_chat_with_tools_min_version: feature_flags
            .lookup("vscode_chat_with_tools_min_version", ""),
        intellij_chat_with_tools_min_version: feature_flags
            .lookup("intellij_chat_with_tools_min_version", ""),
        vscode_chat_multimodal_min_version: feature_flags
            .lookup("vscode_chat_multimodal_min_version", ""),
        intellij_chat_multimodal_min_version: feature_flags
            .lookup("intellij_chat_multimodal_min_version", ""),
        intellij_enable_chat_mermaid_diagrams_min_version: feature_flags
            .lookup("intellij_enable_chat_mermaid_diagrams_min_version", ""),
        vscode_agent_mode_min_version: feature_flags.lookup("vscode_agent_mode_min_version", ""),
        vscode_agent_mode_min_stable_version: feature_flags
            .lookup("vscode_agent_mode_min_stable_version", ""),
        vscode_agent_edit_tool: feature_flags.lookup("vscode_agent_edit_tool", "backend_edit_tool"),
        intellij_agent_mode_min_version: feature_flags
            .lookup("intellij_agent_mode_min_version", ""),
        intellij_blocked_versions: feature_flags.lookup("intellij_blocked_versions", ""),
        intellij_design_system_rich_text_editor_min_version: feature_flags
            .lookup("intellij_design_system_rich_text_editor_min_version", ""),
        intellij_syncing_progress_min_version: feature_flags
            .lookup("intellij_syncing_progress_min_version", ""),
        memories_params: feature_flags.lookup("memories_params", "{}"),
        vscode_background_agents_min_version: feature_flags
            .lookup("vscode_background_agents_min_version", ""),
        intellij_background_agents_min_version: feature_flags
            .lookup("intellij_background_agents_min_version", ""),
        elo_model_configuration: feature_flags.lookup("elo_model_configuration", "{}"),
        intellij_ask_for_sync_permission_min_version: feature_flags
            .lookup("intellij_ask_for_sync_permission_min_version", ""),
        workspace_guidelines_length_limit: feature_flags
            .lookup("workspace_guidelines_length_limit", 2000),
        user_guidelines_length_limit: feature_flags.lookup("user_guidelines_length_limit", 2000),
        vscode_rich_checkpoint_info_min_version: feature_flags
            .lookup("vscode_rich_checkpoint_info_min_version", ""),
        intellij_remember_tool_min_version: feature_flags
            .lookup("intellij_remember_tool_min_version", ""),
        intellij_enable_user_guidelines: feature_flags
            .lookup("intellij_enable_user_guidelines", false),
        intellij_user_guidelines_in_settings: feature_flags
            .lookup("intellij_user_guidelines_in_settings", false),
        intellij_enable_workspace_guidelines: feature_flags
            .lookup("intellij_enable_workspace_guidelines", false),
        vscode_virtualized_message_list_min_version: feature_flags
            .lookup("vscode_virtualized_message_list_min_version", ""),
        intellij_virtualized_message_list_min_version: feature_flags
            .lookup("intellij_virtualized_message_list_min_version", ""),
        vscode_chat_stable_prefix_truncation_min_version: feature_flags
            .lookup("vscode_chat_stable_prefix_truncation_min_version", ""),
        agent_edit_tool_min_view_size: feature_flags.lookup("agent_edit_tool_min_view_size", 0),
        agent_edit_tool_schema_type: feature_flags.lookup(
            "agent_edit_tool_schema_type",
            "StrReplaceEditorToolDefinitionNested",
        ),
        agent_edit_tool_enable_fuzzy_matching: feature_flags.lookup(
            "agent_edit_tool_enable_fuzzy_matching",
            true,
        ),
        agent_edit_tool_fuzzy_match_success_message: feature_flags.lookup(
            "agent_edit_tool_fuzzy_match_success_message",
            "Replacement successful. old_str and new_str were slightly modified to match the original file content.",
        ),
        agent_edit_tool_fuzzy_match_max_diff: feature_flags.lookup(
            "agent_edit_tool_fuzzy_match_max_diff",
            50,
        ),
        agent_edit_tool_fuzzy_match_max_diff_ratio: feature_flags.lookup(
            "agent_edit_tool_fuzzy_match_max_diff_ratio",
            0.15,
        ),
        agent_edit_tool_fuzzy_match_min_all_match_streak_between_diffs: feature_flags.lookup(
            "agent_edit_tool_fuzzy_match_min_all_match_streak_between_diffs",
            5,
        ),
        agent_edit_tool_instructions_reminder: feature_flags.lookup(
            "agent_edit_tool_instructions_reminder",
            false,
        ),
        agent_edit_tool_max_lines: feature_flags.lookup("agent_edit_tool_max_lines", 200),
        agent_edit_tool_show_result_snippet: feature_flags.lookup(
            "agent_edit_tool_show_result_snippet",
            true,
        ),
        vscode_direct_apply_min_version: feature_flags
            .lookup("vscode_direct_apply_min_version", ""),
        vscode_personalities_min_version: feature_flags
            .lookup("vscode_personalities_min_version", ""),
        memory_classification_on_first_token: feature_flags
            .lookup("memory_classification_on_first_token", false),
        agent_save_file_tool_instructions_reminder: feature_flags
            .lookup("agent_save_file_tool_instructions_reminder", false),
        remote_agent_chat_history_polling_interval_ms: feature_flags
            .lookup("remote_agent_chat_history_polling_interval_ms", 1000),
        remote_agent_list_polling_interval_ms: feature_flags
            .lookup("remote_agent_list_polling_interval_ms", 5000),
        use_memory_snapshot_manager: feature_flags.lookup("use_memory_snapshot_manager", false),
        intellij_enable_homespun_gitignore: feature_flags
            .lookup("intellij_enable_homespun_gitignore", false),
        intellij_preference_collection_allowed_min_version: feature_flags
            .lookup("intellij_preference_collection_allowed_min_version", ""),
        vscode_generate_commit_message_min_version: feature_flags
            .lookup("vscode_generate_commit_message_min_version", ""),
        enable_rules: feature_flags.lookup("enable_rules", false),
        memories_text_editor_enabled: feature_flags.lookup("memories_text_editor_enabled", false),
        enable_model_registry: feature_flags.lookup("enable_model_registry", false),
        open_file_manager_v2_enabled: feature_flags.lookup("open_file_manager_v2_enabled", false),
        model_registry: feature_flags.lookup("model_registry", "{}"),
        vscode_task_list_min_version: feature_flags.lookup("vscode_task_list_min_version", ""),
        intellij_enable_sentry: feature_flags.lookup("intellij_enable_sentry", true),
        intellij_webview_error_sampling_rate: feature_flags.lookup("intellij_webview_error_sampling_rate", 0.0),
        intellij_plugin_error_sampling_rate: feature_flags.lookup("intellij_plugin_error_sampling_rate", 0.0),
        beachhead_enable_sentry: feature_flags.lookup("beachhead_enable_sentry", false),
        beachhead_error_sampling_rate: feature_flags.lookup("beachhead_error_sampling_rate", 0.0),
        beachhead_trace_sampling_rate: feature_flags.lookup("beachhead_trace_sampling_rate", 0.0),
        intellij_webview_trace_sampling_rate: feature_flags.lookup("intellij_webview_trace_sampling_rate", 0.0),
        intellij_plugin_trace_sampling_rate: feature_flags.lookup("intellij_plugin_trace_sampling_rate", 0.0),
        intellij_enable_webview_performance_monitoring: feature_flags.lookup("intellij_enable_webview_performance_monitoring", false),
        intellij_edt_freeze_detection_enabled: feature_flags.lookup("intellij_edt_freeze_detection_enabled", false),
        enable_agent_auto_mode: feature_flags.lookup("enable_agent_auto_mode", false),
        vscode_remote_agent_ssh_min_version: feature_flags.lookup("vscode_remote_agent_ssh_min_version", ""),
        client_announcement: feature_flags.lookup("client_announcement", ""),
        grep_search_tool_enable: feature_flags.lookup("grep_search_tool_enable", false),
        grep_search_tool_timelimit_sec: feature_flags.lookup("grep_search_tool_timelimit_sec", 10),
        grep_search_tool_output_chars_limit: feature_flags.lookup("grep_search_tool_output_chars_limit", 5000),
        grep_search_tool_num_context_lines: feature_flags.lookup("grep_search_tool_num_context_lines", 5),
        agent_report_streamed_chat_every_chunk: feature_flags.lookup("agent_report_streamed_chat_every_chunk", 3),
        agent_max_total_changed_files_size_bytes: feature_flags.lookup("agent_max_total_changed_files_size_bytes", 2 * 1024 * 1024),
        agent_max_changed_files_skipped_paths: feature_flags.lookup("agent_max_changed_files_skipped_paths", 1000),
        agent_idle_status_update_interval_ms: feature_flags.lookup("agent_idle_status_update_interval_ms", 60 * 1000),
        agent_max_iterations: feature_flags.lookup("agent_max_iterations", 100),
        agent_ssh_connection_check_interval_ms: feature_flags.lookup("agent_ssh_connection_check_interval_ms", 5000),
        agent_ssh_connection_check_log_interval_ms: feature_flags.lookup("agent_ssh_connection_check_log_interval_ms", 5 * 60 * 1000),
    };
    frontend_feature_flags
}

impl<MR: ModelRegistry, CNC: ContentManagerClient> Handler<MR, CNC> {
    pub async fn submit_request_insight_http_response<F, Fut>(
        &self,
        request_context: &RequestContext,
        tenant_info: &TenantInfo,
        f: F,
    ) -> Result<HttpResponse, tonic::Status>
    where
        F: FnOnce() -> Fut,
        Fut: Future<Output = Result<HttpResponse, tonic::Status>>,
    {
        let resp = f().await;
        match &resp {
            Err(status) => {
                let r = status_to_response(status);
                self.request_insight_publisher
                    .record_http_response(
                        request_context,
                        tenant_info,
                        r.status().as_u16(),
                        Some(status.message()),
                    )
                    .await;
            }
            Ok(resp) => {
                self.request_insight_publisher
                    .record_http_response(
                        request_context,
                        tenant_info,
                        resp.status().as_u16(),
                        None,
                    )
                    .await;
            }
        }
        resp
    }

    pub async fn get_models(&self, req: &HttpRequest) -> Result<HttpResponse, tonic::Status> {
        let (user, tenant_info, _request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;

        gate_on_circuit_breaker(&CB_GET_MODELS, &feature_flags, req, &tenant_info)?;

        // Find all model names that are the default based on the current flags
        let default_model_names: Vec<String> = Vec::from([
            MODEL_FLAG.get_from(&feature_flags),
            CHAT_RAW_OUTPUT_MODEL_FLAG.get_from(&feature_flags),
            INSTRUCTION_MODEL_FLAG.get_from(&feature_flags),
            SMART_PASTE_MODEL_FLAG.get_from(&feature_flags),
            NEXT_EDIT_MODEL_FLAG.get_from(&feature_flags),
        ]);

        let models: Vec<Model> = self.model_registry.get_models().await;
        let model_with_config: Vec<ModelWithConfig> = models
            .into_iter()
            .map(|m| ModelWithConfig {
                expose_internal_name: self.config.expose_internal_model_names,
                is_default: default_model_names.contains(&m.name),
                model: m,
            })
            .collect();

        let mut response: public_api_proto::GetModelsResponse = model_with_config.into();
        let frontend_feature_flags = get_feature_flags(&self.config, &feature_flags);

        tracing::info!(
            "get_models feature_flags \
            tenant={:?} \
            feature_flags={:?}",
            tenant_info.tenant_name,
            frontend_feature_flags,
        );

        response.feature_flags = Some(frontend_feature_flags);
        response.user_tier = self.get_user_tier().into();
        Ok(HttpResponse::Ok().json(response))
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient> EndpointHandler<BatchUploadRequest>
    for Handler<MR, CNC>
{
    fn get_retry_policy(&self, req: &HttpRequest) -> RetryPolicy {
        self.get_retry_policy_from_default_flags(req)
    }

    async fn handle(
        &self,
        req: &HttpRequest,
        mem_request: BatchUploadRequest,
        _root_span: RootSpan,
    ) -> tonic::Result<HttpResponse> {
        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;

        gate_on_circuit_breaker(&CB_BATCH_UPLOAD_BLOB, &feature_flags, req, &tenant_info)?;

        if UPLOAD_THROTTLE_FLAG.get_from(&feature_flags) {
            let fill_rate_per_second =
                UPLOAD_THROTTLE_FILL_RATE_PER_SEC_FLAG.get_from(&feature_flags);
            let capacity = UPLAOD_THROTTLE_CAPACITY_FLAG.get_from(&feature_flags) as f64;
            let token_claim = mem_request.blobs.len() as f64;
            self.should_throttle(
                &user.user_id,
                "upload",
                fill_rate_per_second,
                capacity,
                token_claim,
            )
            .await?;
        }

        // Record request metadata
        let metadata = extract_request_metadata(
            &request_context,
            request_insight::RequestType::UnknownRequestType,
            &user,
            req,
        );
        self.request_insight_publisher
            .record_request_metadata(&request_context, &tenant_info, metadata)
            .await;

        // Calculate total size of all blobs for the batch upload event
        let total_size: i64 = mem_request
            .blobs
            .iter()
            .map(|blob| blob.content.len() as i64)
            .sum();

        let blob_count = mem_request.blobs.len() as i32;

        let uploads = mem_request
            .blobs
            .into_iter()
            .map(|blob| UploadContent {
                content: SecretVec::<u8>::new(blob.content.into_bytes().to_vec()),
                path: blob.path.into(),
            })
            .collect();
        let timeout = Duration::from_millis(
            BATCH_UPLOAD_BLOB_CONTENT_TIMEOUT_MS_FLAG.get_from(&feature_flags) as u64,
        );
        let instant = Instant::now() + timeout;
        let blob_names = self
            .content_manager_client
            .batch_upload_blob_content(&request_context, uploads, Some(instant))
            .await?;

        // Record the batch-upload event
        self.request_insight_publisher
            .record_batch_upload(&request_context, &tenant_info, blob_count, total_size)
            .await;

        let mem_result = BatchUploadResponse {
            blob_names: blob_names.iter().map(|s| s.to_string()).collect(),
        };
        Ok(HttpResponse::Ok().json(mem_result))
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient> EndpointHandler<MemorizeRequest>
    for Handler<MR, CNC>
{
    fn get_retry_policy(&self, req: &HttpRequest) -> RetryPolicy {
        self.get_retry_policy_from_default_flags(req)
    }

    async fn handle(
        &self,
        req: &HttpRequest,
        mem_request: MemorizeRequest,
        _root_span: RootSpan,
    ) -> tonic::Result<HttpResponse> {
        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;

        gate_on_circuit_breaker(&CB_MEMORIZE, &feature_flags, req, &tenant_info)?;

        if UPLOAD_THROTTLE_FLAG.get_from(&feature_flags) {
            let fill_rate_per_second =
                UPLOAD_THROTTLE_FILL_RATE_PER_SEC_FLAG.get_from(&feature_flags);
            let capacity = UPLAOD_THROTTLE_CAPACITY_FLAG.get_from(&feature_flags) as f64;
            self.should_throttle(&user.user_id, "upload", fill_rate_per_second, capacity, 1.0)
                .await?
        }

        let path = SecretString::from(mem_request.path);
        // transfer the text into bytes. Ideally, we would receive the content
        // as bytes, but that isn't supported with the JSON based interface.
        let content = SecretVec::<u8>::new(mem_request.t.into_bytes().to_vec());

        let timeout = Duration::from_millis(
            UPLOAD_BLOB_CONTENT_TIMEOUT_MS_FLAG.get_from(&feature_flags) as u64,
        );
        let instant = Instant::now() + timeout;
        let blob_name = self
            .content_manager_client
            .upload_blob_content(&request_context, &path, &content, Some(instant))
            .await?;

        let mem_result = MemorizeResponse {
            mem_object_name: blob_name,
        };
        Ok(HttpResponse::Ok().json(mem_result))
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient> EndpointHandler<public_api_proto::EditRequest>
    for Handler<MR, CNC>
{
    fn get_retry_policy(&self, req: &HttpRequest) -> RetryPolicy {
        self.get_retry_policy_from_default_flags(req)
    }

    /// run an edit request and return a http response in the success case or a status in the error case
    async fn handle(
        &self,
        req: &HttpRequest,
        front_edit_request: public_api_proto::EditRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, tonic::Status> {
        tracing::info!("edit request model={:?}", front_edit_request.model,);

        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;

        gate_on_circuit_breaker(&CB_EDIT, &feature_flags, req, &tenant_info)?;

        let metadata = extract_request_metadata(
            &request_context,
            request_insight::RequestType::Edit,
            &user,
            req,
        );
        self.request_insight_publisher
            .record_request_metadata(&request_context, &tenant_info, metadata)
            .await;

        let (com, mi) = get_model::<MR>(
            front_edit_request.model.as_ref(),
            &self.model_registry,
            &feature_flags,
            &EDIT_MODEL_FLAG,
            ModelType::Edit,
        )
        .await?;

        let com = match com {
            Client::Edit(c) => c.clone(),
            _ => return Err(tonic::Status::internal("Model is not an edit model")),
        };
        let edit_request: EditRequest = (front_edit_request, &mi.name).try_into()?;

        let result = com.edit(&request_context, edit_request).await?;
        let result = public_api_proto::EditResponse::from(result);
        Ok(HttpResponse::Ok().json(result))
    }
}

pub async fn instruction_stream_api_auth<MR: ModelRegistry, CNC: ContentManagerClient>(
    data: web::Data<Handler<MR, CNC>>,
    req: HttpRequest,
    item: web::Json<public_api_proto::InstructionRequest>,
    root_span: RootSpan,
) -> HttpResponse {
    let Ok((user, tenant_info, request_context, _start_time)) = request_context_from_req(&req)
    else {
        return HttpResponse::InternalServerError().finish();
    };

    root_span.record("tenant_name", &tenant_info.tenant_name);
    root_span.record("opaque_user_id", &user.opaque_user_id.user_id);

    let request_insight_publisher = data.request_insight_publisher.clone();
    let feature_flags = match data.get_feature_flags(&user, &tenant_info, Some(&req)) {
        Ok(f) => f,
        Err(e) => {
            tracing::error!("Error getting feature flags {:?}", e);
            return status_to_response(&e);
        }
    };

    let front_instruction_request = item.into_inner();
    let front_instruction_model = front_instruction_request
        .model
        .clone()
        .unwrap_or_default()
        .to_string();
    let mut instruction_request: InstructionRequest =
        match front_instruction_request.clone().try_into() {
            Ok(r) => r,
            Err(e) => {
                tracing::error!("Error converting to instruction request {:?}", e);
                return status_to_response(&e);
            }
        };

    #[allow(clippy::too_many_arguments)]
    async fn handle_instruction_stream_iteration<MR: ModelRegistry, CNC: ContentManagerClient>(
        data: web::Data<Handler<MR, CNC>>,
        req: &HttpRequest,
        user: &User,
        tenant_info: &TenantInfo,
        request_context: &RequestContext,
        instruction_request: InstructionRequest,
        request_insight_publisher: Arc<dyn RequestInsightPublisher + Send + Sync>,
        feature_flags: feature_flags::FeatureFlagsServiceHandle,
    ) -> Result<HttpResponse, tonic::Status> {
        let receiver_result: Result<
            Receiver<Result<edit::InstructionResponse, tonic::Status>>,
            tonic::Status,
        > = instruction_stream(
            data,
            req,
            user,
            tenant_info,
            request_context,
            instruction_request,
        )
        .await;

        match receiver_result {
            Ok(receiver) => Ok(streaming_http_response_from_receiver::<
                edit::InstructionResponse,
                public_api_proto::InstructionResponse,
            >(
                "instruction-stream",
                Ok(receiver),
                request_context.clone(),
                tenant_info.clone(),
                request_insight_publisher.clone(),
                &feature_flags,
                false, // Disable heartbeat for instruction-stream
            )
            .await),
            Err(e) => Err(e),
        }
    }
    // Build a list of models to try, start with the initial one
    let mut models_to_try: Vec<String> = vec![front_instruction_model.clone()];

    let is_smart_paste: bool = front_instruction_request
        .code_block
        .as_ref()
        .map_or(false, |block| !block.is_empty());

    // If the client didn't specify a model, we can add fallback/s to the vector
    if !is_smart_paste && front_instruction_model.is_empty() {
        let fallback_models = &INSTRUCTION_FALLBACK_MODELS_FLAG.get_from(&feature_flags);
        if !fallback_models.is_empty() {
            // Parse a list of models from flag
            models_to_try.extend(fallback_models.split(',').map(|s| s.trim().to_string()));
        }
    }
    let mut num_attempt = 1;
    for model_to_try in &models_to_try {
        instruction_request.model_name.clone_from(model_to_try);
        let result = handle_instruction_stream_iteration(
            data.clone(),
            &req,
            &user,
            &tenant_info,
            &request_context,
            instruction_request.clone(),
            request_insight_publisher.clone(),
            feature_flags.clone(),
        )
        .await;
        match result {
            Ok(http_response) => return http_response,
            Err(e)
                if matches!(e.code(), tonic::Code::Unavailable)
                    && num_attempt < models_to_try.len() =>
            {
                tracing::warn!(
                    "Instruction request failed with {:?}, attempting fallback",
                    e.clone()
                );
                num_attempt += 1;
                continue;
            }
            Err(e) => {
                tracing::warn!(
                    "Instruction request failed with {:?} after {} fallbacks",
                    e.clone(),
                    num_attempt - 1
                );
                return status_to_response(&e);
            }
        }
    }
    tracing::error!("This should never happen - no models attempted");
    HttpResponse::InternalServerError().finish()
}

async fn instruction_stream<MR: ModelRegistry, CNC: ContentManagerClient>(
    data: web::Data<Handler<MR, CNC>>,
    req: &HttpRequest,
    user: &User,
    tenant_info: &TenantInfo,
    request_context: &RequestContext,
    instruction_request: InstructionRequest,
) -> Result<Receiver<tonic::Result<edit::InstructionResponse>>, tonic::Status> {
    tracing::info!(
        "instruction request model={:?}",
        instruction_request.model_name
    );

    let feature_flags = data.get_feature_flags(user, tenant_info, Some(req))?;

    gate_on_circuit_breaker(&CB_EDIT, &feature_flags, req, tenant_info)?;

    let metadata = extract_request_metadata(
        request_context,
        request_insight::RequestType::Edit,
        user,
        req,
    );
    data.request_insight_publisher
        .record_request_metadata(request_context, tenant_info, metadata)
        .await;

    let is_smart_paste: bool = instruction_request
        .code_block
        .as_ref()
        .map_or(false, |block| !block.is_empty());
    let relevant_model_flag: &feature_flags::StringFlag = if is_smart_paste {
        &SMART_PASTE_MODEL_FLAG
    } else {
        &INSTRUCTION_MODEL_FLAG
    };

    let (com, mi) = get_model::<MR>(
        Some(&instruction_request.model_name),
        &data.model_registry,
        &feature_flags,
        relevant_model_flag,
        ModelType::Edit,
    )
    .await?;

    let com = match com {
        Client::Edit(c) => c.clone(),
        _ => return Err(tonic::Status::internal("Model is not an edit model")),
    };
    let mut final_request: InstructionRequest = instruction_request.clone();
    final_request.model_name.clone_from(&mi.name);

    let retry_policy = data.get_retry_policy_from_default_flags(req);
    let uri: &str = req.uri().path();
    let metrics_tenant_name = req
        .extensions()
        .get::<TenantInfo>()
        .map(|t| t.metrics_tenant_name())
        .unwrap_or("unknown")
        .to_string();
    let request_source = req
        .extensions()
        .get::<RequestContext>()
        .map(|s| s.request_source().to_string())
        .unwrap_or("unknown".to_string());
    retry(
        retry_policy.clone(),
        || async {
            com.instruction_stream(request_context, final_request.clone())
                .await
        },
        |e| {
            HANDLER_RETRY_COLLECTOR
                .with_label_values(&[
                    uri,
                    &e.code().to_string(),
                    &request_source,
                    &metrics_tenant_name,
                ])
                .inc();
        },
    )
    .await
}

impl<MR: ModelRegistry, CNC: ContentManagerClient> EndpointHandler<CompletionFeedback>
    for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: CompletionFeedback,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, tonic::Status> {
        let (user, tenant_info, _request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;

        gate_on_circuit_breaker(&CB_COMPLETION_FEEDBACK, &feature_flags, req, &tenant_info)?;

        let request_id = RequestId::try_from(request.request_id.as_str());
        if request_id.is_err() {
            tracing::info!(
                "completion_feedback invalid request id: {:?}",
                request.request_id
            );
            return Err(tonic::Status::invalid_argument("Invalid request id"));
        }
        self.request_insight_publisher
            .record_completion_feedback(
                &tenant_info,
                &request_id.unwrap(),
                request.rating,
                &request.note,
            )
            .await;

        Ok(HttpResponse::Ok().json(CompletionFeedbackResponse {}))
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient> EndpointHandler<ChatFeedback>
    for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: ChatFeedback,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, tonic::Status> {
        let (user, tenant_info, _request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;

        gate_on_circuit_breaker(&CB_CHAT_FEEDBACK, &feature_flags, req, &tenant_info)?;

        let request_id = RequestId::try_from(request.request_id.as_str());
        if request_id.is_err() {
            tracing::info!("chat_feedback invalid request id: {:?}", request.request_id);
            return Err(tonic::Status::invalid_argument("Invalid request id"));
        }

        let user_agent = req
            .headers()
            .get("user-agent")
            .map(|h| h.to_str().unwrap_or("not-utf8"))
            .unwrap_or("");

        match public_api_proto::ChatMode::try_from(request.mode) {
            Ok(public_api_proto::ChatMode::Agent) => {
                self.request_insight_publisher
                    .record_agent_feedback(
                        &tenant_info,
                        &request_id.unwrap(),
                        request.rating,
                        &request.note,
                        user_agent,
                    )
                    .await;
            }
            Ok(public_api_proto::ChatMode::RemoteAgent) => {
                self.request_insight_publisher
                    .record_remote_agent_feedback(
                        &tenant_info,
                        &request_id.unwrap(),
                        request.rating,
                        &request.note,
                        user_agent,
                    )
                    .await;
            }
            _ => {
                self.request_insight_publisher
                    .record_chat_feedback(
                        &tenant_info,
                        &request_id.unwrap(),
                        request.rating,
                        &request.note,
                        user_agent,
                    )
                    .await;
            }
        }

        Ok(HttpResponse::Ok().json(ChatFeedbackResponse {}))
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient> EndpointHandler<SaveChatRequest>
    for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: SaveChatRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, tonic::Status> {
        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let metadata = extract_request_metadata(
            &request_context,
            request_insight::RequestType::ShareSaveChat,
            &user,
            req,
        );
        self.request_insight_publisher
            .record_request_metadata(&request_context, &tenant_info, metadata)
            .await;

        self.submit_request_insight_http_response(&request_context, &tenant_info, || async {
            let chat: Vec<ChatExchange> = request
                .chat
                .iter()
                .cloned()
                .map(|e| ChatExchange {
                    message: e.request_message,
                    response: e.response_text.unwrap_or_default(),
                    request_id: e.request_id.unwrap_or_default(),
                })
                .collect();

            let uuid = self
                .share_client
                .save_chat_conversation(
                    &request_context,
                    user.user_id.clone(),
                    request.conversation_id,
                    &chat,
                    request.title,
                )
                .await?;

            let share_svc_url = self.config.share_service_url.clone();
            let url = Url::parse(&share_svc_url)
                .map_err(|e| {
                    tracing::error!("Failure to parse share service URL: {:?}", e);
                    tonic::Status::internal("Invalid share service URL")
                })?
                .join(&uuid)
                .map_err(|e| {
                    tracing::error!("Failure to construct share URL: {:?}", e);
                    tonic::Status::internal("Failed to construct share URL")
                })?
                .to_string();

            Ok(HttpResponse::Ok().json(SaveChatResponse { uuid, url }))
        })
        .await
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient> EndpointHandler<NextEditFeedback>
    for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: NextEditFeedback,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, tonic::Status> {
        let (user, tenant_info, _request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;

        gate_on_circuit_breaker(&CB_NEXT_EDIT_FEEDBACK, &feature_flags, req, &tenant_info)?;

        let request_id = RequestId::try_from(request.request_id.as_str());
        if request_id.is_err() {
            tracing::info!(
                "next_edit_feedback invalid request id: {:?}",
                request.request_id
            );
            return Err(tonic::Status::invalid_argument("Invalid request id"));
        }
        self.request_insight_publisher
            .record_next_edit_feedback(
                &tenant_info,
                &request_id.unwrap(),
                request.rating,
                &request.note,
            )
            .await;

        Ok(HttpResponse::Ok().json(NextEditFeedbackResponse {}))
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient> EndpointHandler<ResolveCompletions>
    for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: ResolveCompletions,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, tonic::Status> {
        let (user, tenant_info, _request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;

        gate_on_circuit_breaker(&CB_RESOLVE_COMPLETIONS, &feature_flags, req, &tenant_info)?;

        for resolution in request.resolutions.iter() {
            let request_id = RequestId::try_from(resolution.request_id.as_str());
            if request_id.is_err() {
                tracing::info!(
                    "resolve_completions invalid request id: {:?}",
                    resolution.request_id
                );
                continue;
            }
            let emit_time = prost_wkt_types::Timestamp {
                seconds: resolution.emit_time_sec,
                nanos: resolution.emit_time_nsec,
            };
            let resolve_time = prost_wkt_types::Timestamp {
                seconds: resolution.resolve_time_sec,
                nanos: resolution.resolve_time_nsec,
            };
            self.request_insight_publisher
                .forward_completion_resolution(
                    &tenant_info,
                    &request_id.unwrap(),
                    emit_time,
                    resolve_time,
                    resolution.accepted_idx,
                )
                .await;
        }

        Ok(HttpResponse::Ok().json(CompletionResolution::default())) // TODO: ???
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient> EndpointHandler<NextEditResolutionBatch>
    for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: NextEditResolutionBatch,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, tonic::Status> {
        let (user, tenant_info, _request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;

        gate_on_circuit_breaker(&CB_NEXT_EDIT_RESOLUTION, &feature_flags, req, &tenant_info)?;

        for resolution in request.resolutions.iter() {
            let resolution_request_id = RequestId::try_from(resolution.request_id.as_str());
            if resolution_request_id.is_err() {
                tracing::info!(
                    "next edit resolution invalid request id: {:?}",
                    resolution_request_id
                );
                return Err(tonic::Status::invalid_argument("Invalid request id"));
            }
            let emit_time = prost_wkt_types::Timestamp {
                seconds: resolution.emit_time_sec,
                nanos: resolution.emit_time_nsec,
            };
            let resolve_time = prost_wkt_types::Timestamp {
                seconds: resolution.resolve_time_sec,
                nanos: resolution.resolve_time_nsec,
            };
            self.request_insight_publisher
                .record_next_edit_resolution(
                    &tenant_info,
                    &resolution_request_id.unwrap(),
                    emit_time,
                    resolve_time,
                    resolution.is_accepted,
                )
                .await;
        }

        Ok(HttpResponse::Ok().json(NextEditResolutionResponse {}))
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient> EndpointHandler<OnboardingSessionEventBatch>
    for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: OnboardingSessionEventBatch,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, tonic::Status> {
        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;

        gate_on_circuit_breaker(
            &CB_ONBOARDING_SESSION_EVENT,
            &feature_flags,
            req,
            &tenant_info,
        )?;

        let user_agent = req
            .headers()
            .get("user-agent")
            .map(|h| h.to_str().unwrap_or("not-utf8"))
            .unwrap_or("");

        let session_events = request
            .events
            .iter()
            .map(|event| {
                let event_time = prost_wkt_types::Timestamp {
                    seconds: event.event_time_sec,
                    nanos: event.event_time_nsec,
                };
                request_insight::SessionEvent {
                    event_id: Some(Uuid::new_v4().to_string()),
                    time: Some(event_time),
                    event: Some(
                        request_insight::session_event::Event::OnboardingSessionEvent(
                            request_insight::OnboardingSessionEvent {
                                event_name: event.event_name.clone(),
                                user_agent: user_agent.to_string(),
                            },
                        ),
                    ),
                }
            })
            .collect();
        self.request_insight_publisher
            .record_session_events(&request_context, &tenant_info, &user, session_events)
            .await;

        Ok(HttpResponse::Ok().json(OnboardingSessionEventResponse {}))
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient> EndpointHandler<NextEditSessionEventBatch>
    for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: NextEditSessionEventBatch,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, tonic::Status> {
        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        // Check that we have a valid session id. Normally session ids are optional and filled in by
        // downstream services, but we're recording an event keyed by session id so really need one.
        if request_context.request_session_id().is_empty() {
            return Err(tonic::Status::invalid_argument("Invalid session id"));
        }

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;

        gate_on_circuit_breaker(&CB_NEXT_EDIT_USER_EVENT, &feature_flags, req, &tenant_info)?;

        let user_agent = req
            .headers()
            .get("user-agent")
            .map(|h| h.to_str().unwrap_or("not-utf8"))
            .unwrap_or("");

        let mut result = Ok(HttpResponse::Ok().json(NextEditSessionEventResponse {}));

        // Construct RI session events from the request.
        let session_events = request
            .events
            .iter()
            .filter_map(|event| {
                let related_request_id = match event.related_request_id.as_deref() {
                    None | Some("") => None,
                    Some(id) => match RequestId::try_from(id) {
                        Ok(request_id) => Some(request_id),
                        Err(e) => {
                            tracing::info!("next_edit_session_event invalid request id: {:?}", e);
                            result = Err(tonic::Status::invalid_argument(
                                "Invalid request id(s) skipped.",
                            ));
                            return None;
                        }
                    },
                };

                let event_time = prost_wkt_types::Timestamp {
                    seconds: event.event_time_sec,
                    nanos: event.event_time_nsec,
                };

                Some(request_insight::SessionEvent {
                    event_id: Some(Uuid::new_v4().to_string()),
                    time: Some(event_time),
                    event: Some(request_insight::session_event::Event::NextEditSessionEvent(
                        request_insight::NextEditSessionEvent {
                            related_request_id: related_request_id.map(|id| id.to_string()),
                            related_suggestion_id: event.related_suggestion_id.clone(),
                            event_name: event.event_name.clone(),
                            event_source: event.event_source.clone(),
                            user_agent: user_agent.to_string(),
                        },
                    )),
                })
            })
            .collect();

        self.request_insight_publisher
            .record_session_events(&request_context, &tenant_info, &user, session_events)
            .await;
        result
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient> EndpointHandler<ClientCompletionTimelineRequest>
    for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: ClientCompletionTimelineRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, tonic::Status> {
        let (_user, tenant_info, _request_context, _start_time) = request_context_from_req(req)?;
        for timeline in request.timelines.iter() {
            let request_id = RequestId::try_from(timeline.request_id.as_str());
            if request_id.is_err() {
                tracing::info!(
                    "client-completion-timeline invalid request id: {:?}",
                    timeline.request_id
                );
                continue;
            }

            let initial_request_time = prost_wkt_types::Timestamp {
                seconds: timeline.initial_request_time_sec,
                nanos: timeline.initial_request_time_nsec,
            };
            let api_start_time = prost_wkt_types::Timestamp {
                seconds: timeline.api_start_time_sec,
                nanos: timeline.api_start_time_nsec,
            };
            let api_end_time = prost_wkt_types::Timestamp {
                seconds: timeline.api_end_time_sec,
                nanos: timeline.api_end_time_nsec,
            };
            let emit_time = prost_wkt_types::Timestamp {
                seconds: timeline.emit_time_sec,
                nanos: timeline.emit_time_nsec,
            };

            self.request_insight_publisher
                .record_client_completion_timeline(
                    &tenant_info,
                    &request_id.unwrap(),
                    initial_request_time,
                    api_start_time,
                    api_end_time,
                    emit_time,
                )
                .await;
        }

        Ok(HttpResponse::Ok().json(ClientCompletionTimelineResponse::default()))
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient> EndpointHandler<EditResolution>
    for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        resolution: EditResolution,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, tonic::Status> {
        let (user, tenant_info, _request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;

        gate_on_circuit_breaker(&CB_EDIT_RESOLUTION, &feature_flags, req, &tenant_info)?;

        let request_id = RequestId::try_from(resolution.request_id.as_str());
        if request_id.is_err() {
            tracing::info!(
                "resolve_edit invalid request id: {:?}",
                resolution.request_id
            );
        }
        let emit_time = prost_wkt_types::Timestamp {
            seconds: resolution.emit_time_sec,
            nanos: resolution.emit_time_nsec,
        };
        let resolve_time = prost_wkt_types::Timestamp {
            seconds: resolution.resolve_time_sec,
            nanos: resolution.resolve_time_nsec,
        };
        self.request_insight_publisher
            .record_edit_resolution(
                &tenant_info,
                &request_id.unwrap(),
                emit_time,
                resolve_time,
                resolution.is_accepted,
                resolution.annotated_text,
                resolution.annotated_instruction,
            )
            .await;

        Ok(HttpResponse::Ok().json(ResolveEditResponse {}))
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient> EndpointHandler<InstructionResolution>
    for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        resolution: InstructionResolution,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, tonic::Status> {
        let (user, tenant_info, _request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;

        gate_on_circuit_breaker(
            &CB_INSTRUCTION_RESOLUTION,
            &feature_flags,
            req,
            &tenant_info,
        )?;

        let request_id = RequestId::try_from(resolution.request_id.as_str());
        if request_id.is_err() {
            tracing::info!(
                "resolve_instruction invalid request id: {:?}",
                resolution.request_id
            );
        }
        let resolve_time = prost_wkt_types::Timestamp {
            seconds: resolution.resolve_time_sec,
            nanos: resolution.resolve_time_nsec,
        };
        let emit_time = if resolution.emit_time_sec == 0 {
            // If no emit time, use resolve time to avoid 0 timestamp that might break data pipelines
            resolve_time
        } else {
            prost_wkt_types::Timestamp {
                seconds: resolution.emit_time_sec,
                nanos: resolution.emit_time_nsec,
            }
        };
        let is_accepted_chunks: Vec<bool> = resolution.is_accepted_chunks.into_iter().collect();
        self.request_insight_publisher
            .record_instruction_resolution(
                &tenant_info,
                &request_id.unwrap(),
                is_accepted_chunks,
                resolution.is_accept_all,
                resolution.is_reject_all,
                emit_time,
                resolve_time,
            )
            .await;

        Ok(HttpResponse::Ok().json(InstructionResolutionResponse {}))
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient> EndpointHandler<SmartPasteResolution>
    for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        resolution: SmartPasteResolution,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, tonic::Status> {
        let (user, tenant_info, _request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;

        gate_on_circuit_breaker(
            &CB_SMART_PASTE_RESOLUTION,
            &feature_flags,
            req,
            &tenant_info,
        )?;

        let request_id = RequestId::try_from(resolution.request_id.as_str());
        if request_id.is_err() {
            tracing::info!(
                "resolve_smart_paste invalid request id: {:?}",
                resolution.request_id
            );
        }
        let initial_request_time = prost_wkt_types::Timestamp {
            seconds: resolution.initial_request_time_sec,
            nanos: resolution.initial_request_time_nsec,
        };
        let stream_finish_time = prost_wkt_types::Timestamp {
            seconds: resolution.stream_finish_time_sec,
            nanos: resolution.stream_finish_time_nsec,
        };
        let apply_time = prost_wkt_types::Timestamp {
            seconds: resolution.apply_time_sec,
            nanos: resolution.apply_time_nsec,
        };
        let resolve_time = prost_wkt_types::Timestamp {
            seconds: resolution.resolve_time_sec,
            nanos: resolution.resolve_time_nsec,
        };
        let is_accepted_chunks: Vec<bool> = resolution.is_accepted_chunks.into_iter().collect();
        self.request_insight_publisher
            .record_smart_paste_resolution(
                &tenant_info,
                &request_id.unwrap(),
                is_accepted_chunks,
                resolution.is_accept_all,
                resolution.is_reject_all,
                initial_request_time,
                stream_finish_time,
                apply_time,
                resolve_time,
            )
            .await;

        Ok(HttpResponse::Ok().json(SmartPasteResolutionResponse {}))
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient> EndpointHandler<PreferenceSample>
    for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        sample: PreferenceSample,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, tonic::Status> {
        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;

        gate_on_circuit_breaker(&CB_PREFERENCE_SAMPLE, &feature_flags, req, &tenant_info)?;

        let mut request_ids = Vec::new();
        for request_id in &sample.request_ids {
            let cur_request_id = RequestId::try_from(request_id.as_str());
            if cur_request_id.is_err() {
                tracing::error!(
                    "record_preference_sample invalid request id: {:?}",
                    request_id,
                );
            }
            request_ids.push(cur_request_id.unwrap());
        }

        self.request_insight_publisher
            .record_preference_sample(
                &request_context,
                &tenant_info,
                &request_ids,
                &sample.scores,
                sample.feedback,
            )
            .await;

        Ok(HttpResponse::Ok().json(PreferenceSampleResponse {}))
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient> EndpointHandler<RecordRequestEventsRequest>
    for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: RecordRequestEventsRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, tonic::Status> {
        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;
        gate_on_circuit_breaker(&CB_RECORD_REQUEST_EVENTS, &feature_flags, req, &tenant_info)?;

        // Make sure the client provided a request id and that every event has a time.
        for event in request.events.iter() {
            if event.time.is_none() {
                return Err(tonic::Status::invalid_argument("Invalid event time"));
            }

            if event.event.is_none() {
                return Err(tonic::Status::invalid_argument("Event type not recognized"));
            }
        }

        // Make sure every event has an id. Assign a random one if the caller didn't specify one.
        let events_with_ids = request
            .events
            .iter()
            .map(|event| {
                let mut event_with_id = event.clone();
                if event_with_id.event_id.is_none() {
                    event_with_id.event_id = Some(Uuid::new_v4().to_string());
                }
                event_with_id
            })
            .collect::<Vec<request_insight::RequestEvent>>();

        self.request_insight_publisher
            .record_request_events(&request_context, &tenant_info, events_with_ids)
            .await;
        Ok(HttpResponse::Ok().json(RecordRequestEventsResponse {}))
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient> EndpointHandler<RecordSessionEventsRequest>
    for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: RecordSessionEventsRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, tonic::Status> {
        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;
        gate_on_circuit_breaker(&CB_RECORD_SESSION_EVENTS, &feature_flags, req, &tenant_info)?;

        // Make sure the client provided a session id and that every event has a time.
        if request_context.request_session_id().is_empty() {
            return Err(tonic::Status::invalid_argument("Invalid session id"));
        }
        for event in request.events.iter() {
            if event.time.is_none() {
                return Err(tonic::Status::invalid_argument("Invalid event time"));
            }

            if event.event.is_none() {
                return Err(tonic::Status::invalid_argument("Event type not recognized"));
            }
        }

        // Make sure every event has an id. Assign a random one if the caller didn't specify one.
        let events_with_ids = request
            .events
            .iter()
            .map(|event| {
                let mut event_with_id = event.clone();
                if event_with_id.event_id.is_none() {
                    event_with_id.event_id = Some(Uuid::new_v4().to_string());
                }
                event_with_id
            })
            .collect::<Vec<request_insight::SessionEvent>>();

        self.request_insight_publisher
            .record_session_events(&request_context, &tenant_info, &user, events_with_ids)
            .await;
        Ok(HttpResponse::Ok().json(RecordSessionEventsResponse {}))
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<request_insight::RecordFullExportUserEventsRequest> for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: request_insight::RecordFullExportUserEventsRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, tonic::Status> {
        let (user, tenant_info, _request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;

        gate_on_circuit_breaker(&CB_RECORD_USER_EVENTS, &feature_flags, req, &tenant_info)?;

        let session_id = RequestSessionId::from_headers(req.headers());

        // The client's json is expected to conform to our backend protos for simplicity, but we
        // don't want clients to set their own session or user id.
        if request.session_id.is_some() {
            return Err(tonic::Status::invalid_argument(
                "session_id should not be set in client JSON",
            ));
        }
        if request.user_id.is_some() {
            return Err(tonic::Status::invalid_argument(
                "user_id should not be set in client JSON",
            ));
        }
        if request.tenant_info.is_some() {
            return Err(tonic::Status::invalid_argument(
                "tenant_info should not be set in client JSON",
            ));
        }

        let mut request_with_ids = request.clone();
        request_with_ids.user_id = Some(user.user_id.to_string());
        request_with_ids.session_id = Some(session_id.to_string());
        request_with_ids.tenant_info = to_tenant_info_proto(&tenant_info);
        self.request_insight_publisher
            .record_full_export_user_events(request_with_ids)
            .await;

        Ok(HttpResponse::Ok().json(request_insight::RecordFullExportUserEventsResponse {}))
    }
}

/*
 Handle find-missing requests
 If a model is provided, returns the result for that specific model
 Otherwise, returns the union of the results for default models of all types
*/
impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<public_api_proto::FindMissingRequest> for Handler<MR, CNC>
{
    fn get_retry_policy(&self, req: &HttpRequest) -> RetryPolicy {
        self.get_retry_policy_from_default_flags(req)
    }

    async fn handle(
        &self,
        req: &HttpRequest,
        request_in: public_api_proto::FindMissingRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, tonic::Status> {
        tracing::info!(
            "find_missing request model={:?} memories={:?}",
            request_in.model,
            request_in.mem_object_names.len()
        );

        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;

        gate_on_circuit_breaker(&CB_FIND_MISSING, &feature_flags, req, &tenant_info)?;

        // check rate limit
        let mut token_claim_per_missing: f64 = 0.0;
        if FIND_MISSING_THROTTLE_FLAG.get_from(&feature_flags) {
            let fill_rate_per_second =
                FIND_MISSING_THROTTLE_FILL_RATE_PER_SEC_FLAG.get_from(&feature_flags);
            let capacity = FIND_MISSING_THROTTLE_CAPACITY_FLAG.get_from(&feature_flags) as f64;
            token_claim_per_missing = 0.95;
            let token_claim =
                (1.0 - token_claim_per_missing) * request_in.mem_object_names.len() as f64;
            self.should_throttle(
                &user.user_id,
                "find_missing",
                fill_rate_per_second,
                capacity,
                token_claim,
            )
            .await?
        }

        // Get nonindexed from relevant Completion client (if any)
        let completion_client_result = get_model::<MR>(
            request_in.model.as_ref(),
            &self.model_registry,
            &feature_flags,
            &MODEL_FLAG,
            ModelType::Inference,
        )
        .await;

        let completion_host_fn = match &completion_client_result {
            Ok((completion_client, completion_mi)) => {
                let request_out_completion = CompletionFindMissingRequest {
                    model_name: completion_mi.name.to_string(),
                    blob_names: request_in.mem_object_names.clone(),
                };
                match &completion_client {
                    Client::Completion(completion_client) => {
                        completion_client.find_missing(&request_context, request_out_completion)
                    }
                    _ => Box::pin(async { Ok(CompletionFindMissingResponse::default()) }),
                }
            }
            _ => Box::pin(async { Ok(CompletionFindMissingResponse::default()) }),
        };

        // Get nonindexed from relevant Chat client (if any)
        let chat_client_result = get_model::<MR>(
            request_in.model.as_ref(),
            &self.model_registry,
            &feature_flags,
            &CHAT_RAW_OUTPUT_MODEL_FLAG,
            ModelType::Chat,
        )
        .await;
        let chat_host_fn = match &chat_client_result {
            Ok((chat_client, chat_mi)) => {
                let request_out_chat = ChatFindMissingRequest {
                    model_name: chat_mi.name.to_string(),
                    blob_names: request_in.mem_object_names.clone(),
                };
                match &chat_client {
                    Client::Chat(chat_client) => {
                        chat_client.find_missing(&request_context, request_out_chat)
                    }
                    _ => Box::pin(async { Ok(ChatFindMissingResponse::default()) }),
                }
            }
            _ => Box::pin(async { Ok(ChatFindMissingResponse::default()) }),
        };

        // Get nonindexed from relevant NextEdit client (if any)
        let next_edit_client_result = get_model::<MR>(
            request_in.model.as_ref(),
            &self.model_registry,
            &feature_flags,
            &MODEL_FLAG,
            ModelType::NextEdit,
        )
        .await;
        let next_edit_host_fn = match &next_edit_client_result {
            Ok((next_edit_client, next_edit_mi)) => {
                let request_out_next_edit = next_edit::FindMissingRequest {
                    model_name: next_edit_mi.name.to_string(),
                    blob_names: request_in.mem_object_names.clone(),
                };
                match &next_edit_client {
                    Client::NextEdit(next_edit_client) => {
                        next_edit_client.find_missing(&request_context, request_out_next_edit)
                    }
                    _ => Box::pin(async { Ok(next_edit::FindMissingResponse::default()) }),
                }
            }
            _ => Box::pin(async { Ok(next_edit::FindMissingResponse::default()) }),
        };

        // We need to make a separate call to the content manager to find missing
        // This also triggers catchup logic on existing blobs for new transformation keys
        let timeout = Duration::from_millis(
            FIND_MISSING_BLOBS_TIMEOUT_MS_FLAG.get_from(&feature_flags) as u64,
        );
        let instant = Instant::now() + timeout;
        let content_manager_fn = self.content_manager_client.find_missing_blobs(
            &request_context,
            &request_in.mem_object_names,
            Some(instant),
        );

        let joined_future = future::join4(
            content_manager_fn,
            completion_host_fn,
            chat_host_fn,
            next_edit_host_fn,
        );
        let response = joined_future.await;

        let (
            unknown_memory_names_result,
            completion_host_response_result,
            chat_host_response_result,
            next_edit_host_response_result,
        ) = response;

        // Content manager and Completion host responses are required - otheres are optional
        let unknown_memory_names: Vec<String> = unknown_memory_names_result?;
        let completion_host_response = completion_host_response_result?;
        let chat_host_response = match chat_host_response_result {
            Ok(r) => r,
            Err(_) => ChatFindMissingResponse::default(),
        };
        let next_edit_host_response = match next_edit_host_response_result {
            Ok(r) => r,
            Err(_) => next_edit::FindMissingResponse::default(),
        };

        // Convert to set for fast filtering of missing blobs from nonindexed
        let unknown_memory_names_set = unknown_memory_names
            .iter()
            .cloned()
            .collect::<std::collections::HashSet<String>>();

        // Union and deduplicate the nonindexed_blob_names from all hosts
        let nonindexed_blob_names: Vec<_> = completion_host_response
            .nonindexed_blob_names
            .iter()
            .chain(chat_host_response.nonindexed_blob_names.iter())
            .chain(next_edit_host_response.nonindexed_blob_names.iter())
            .cloned()
            .collect::<std::collections::HashSet<String>>()
            .into_iter()
            .filter(|blob_name: &String| !unknown_memory_names_set.contains(blob_name))
            .collect();

        // Record request metadata
        let metadata = extract_request_metadata(
            &request_context,
            request_insight::RequestType::UnknownRequestType,
            &user,
            req,
        );
        self.request_insight_publisher
            .record_request_metadata(&request_context, &tenant_info, metadata)
            .await;

        // Publish find-missing event
        let model_name = match request_in.model {
            Some(model) => model,
            None => String::from(""),
        };

        let missing_count = unknown_memory_names.len() as i32;
        let nonindexed_count = nonindexed_blob_names.len() as i32;
        let blob_count = request_in.mem_object_names.len() as i32;

        if token_claim_per_missing > 0.0 {
            // Risk: once the bucket is close to empty, these requests to consume tokens will fail,
            // and we will only be consuming tokens at the discounted rate...
            self.try_consume_tokens(
                &user.user_id,
                "find_missing",
                token_claim_per_missing * (missing_count + nonindexed_count) as f64,
            )
            .await;
        }

        self.request_insight_publisher
            .record_find_missing(
                &request_context,
                &tenant_info,
                model_name,
                blob_count,
                missing_count,
                nonindexed_count,
            )
            .await;

        let find_missing_result = public_api_proto::FindMissingResponse {
            unknown_memory_names,
            nonindexed_blob_names,
        };
        Ok(HttpResponse::Ok().json(find_missing_result))
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient> EndpointHandler<GetSubscriptionInfoRequest>
    for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        _request: GetSubscriptionInfoRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, tonic::Status> {
        let (user, tenant_info, _request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;

        gate_on_circuit_breaker(&CB_GET_SUBSCRIPTION_INFO, &feature_flags, req, &tenant_info)?;

        // Create response directly from user entity subscription information
        let subscription = match &user.subscription {
            Some(auth_sub) => match auth_sub {
                auth_query_client::auth_query::get_token_info_response::Subscription::Enterprise(_) => {
                    Some(public_api_proto::get_subscription_info_response::Subscription::Enterprise(
                        public_api_proto::EnterpriseSubscription {}
                    ))
                }
                auth_query_client::auth_query::get_token_info_response::Subscription::ActiveSubscription(a) => {
                    Some(public_api_proto::get_subscription_info_response::Subscription::ActiveSubscription(
                        public_api_proto::ActiveSubscription {
                            end_date: a.end_date,
                            usage_balance_depleted: a.usage_balance_depleted,
                        }
                    ))
                }
                // Treat trial as active subscription with an end date - we are deprecating the trial subscription type
                auth_query_client::auth_query::get_token_info_response::Subscription::Trial(t) => {
                    Some(public_api_proto::get_subscription_info_response::Subscription::ActiveSubscription(
                        public_api_proto::ActiveSubscription {
                            end_date: t.trial_end,
                            usage_balance_depleted: false,
                        }
                    ))
                }
                auth_query_client::auth_query::get_token_info_response::Subscription::InactiveSubscription(_) => {
                    Some(public_api_proto::get_subscription_info_response::Subscription::InactiveSubscription(
                        public_api_proto::InactiveSubscription {}
                    ))
                }
            },
            None => None,
        };
        let response = GetSubscriptionInfoResponse { subscription };
        Ok(HttpResponse::Ok().json(response))
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient> EndpointHandler<CheckpointBlobsRequest>
    for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: CheckpointBlobsRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, tonic::Status> {
        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;

        gate_on_circuit_breaker(&CB_CHECKPOINT_BLOBS, &feature_flags, req, &tenant_info)?;

        // check rate limit
        if CHECKPOINT_BLOBS_THROTTLE_FLAG.get_from(&feature_flags) {
            let fill_rate_per_second =
                CHECKPOINT_BLOBS_THROTTLE_FILL_RATE_PER_SEC_FLAG.get_from(&feature_flags);
            let capacity = CHECKPOINT_BLOBS_THROTTLE_CAPACITY_FLAG.get_from(&feature_flags) as f64;
            self.should_throttle(
                &user.user_id,
                "checkpoint_blobs",
                fill_rate_per_second,
                capacity,
                1.0,
            )
            .await?
        }

        let Some(request_blobs) = request.blobs else {
            return Err(tonic::Status::invalid_argument(
                "checkpoint_blobs request missing blobs",
            ));
        };

        tracing::trace!(
            "checkpoint_blobs request baseline_checkpoint_id={:?} added_blobs={:?} deleted_blobs={:?}",
            request_blobs.checkpoint_id,
            request_blobs.added_blobs.len(),
            request_blobs.deleted_blobs.len(),
        );

        let blobs: blob_names_proto::Blobs = match (&request_blobs).try_into() {
            Ok(blobs) => blobs,
            Err(e) => {
                return Err(e);
            }
        };

        let checkpoint_id = blobs.baseline_checkpoint_id.clone();
        if checkpoint_id.is_some() {
            let blocklisted_checkpoints: std::collections::HashSet<String> =
                BLOCKLISTED_CHECKPOINT_IDS_FLAG
                    .get_from(&feature_flags)
                    .split(',')
                    .map(|s| s.trim().to_string())
                    .collect::<std::collections::HashSet<String>>();
            if blocklisted_checkpoints.contains(&checkpoint_id.unwrap()) {
                return Err(tonic::Status::not_found(
                    "checkpoint_blobs request baseline_checkpoint_id is blocklisted",
                ));
            }
        }

        let timeout =
            Duration::from_millis(CHECKPOINT_BLOBS_TIMEOUT_MS_FLAG.get_from(&feature_flags) as u64);
        let instant = Instant::now() + timeout;
        let response = self
            .content_manager_client
            .checkpoint_blobs(&request_context, &blobs, Some(instant))
            .await?;

        let checkpoint_result = CheckpointBlobsResponse {
            new_checkpoint_id: response,
        };
        Ok(HttpResponse::Ok().json(checkpoint_result))
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient> EndpointHandler<ClientMetricsRequest>
    for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: ClientMetricsRequest,
        _: RootSpan,
    ) -> Result<HttpResponse, tonic::Status> {
        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        // Check that we have a valid session id. Normally session ids are optional and filled in by
        // downstream services, but we're recording an event keyed by session id so really need one.
        if request_context.request_session_id().is_empty() {
            return Err(tonic::Status::invalid_argument("Invalid session id"));
        }

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;

        gate_on_circuit_breaker(&CB_CLIENT_METRICS, &feature_flags, req, &tenant_info)?;

        let user_agent = req
            .headers()
            .get("user-agent")
            .map(|h| h.to_str().unwrap_or("not-utf8"))
            .unwrap_or("");

        let mut unsupported_metrics = std::collections::HashSet::new();
        for metric in request.metrics {
            let client_metric: &str = metric.client_metric.as_str();
            let value: u64 = metric.value;

            let current_time = prost_wkt_types::Timestamp::from(std::time::SystemTime::now());
            self.request_insight_publisher
                .record_client_metric(
                    &tenant_info,
                    &request_context.request_session_id(),
                    current_time,
                    "client_metric".to_string(),
                    user_agent.to_string(),
                    client_metric.to_string(),
                    value,
                )
                .await;

            // This match statement serves as documentation of the supported metrics list and what
            // kind of metric each label corresponds to. We soft-error unsupported metrics to keep
            // the mechanism lightweight while ensuring API users can't inject bad labels.
            match client_metric {
                // Counter metrics
                "generate_completion_count"
                | "blob_context_mismatch"
                | "webview__chat__chat-mention-folder"
                | "webview__chat__chat-mention-file"
                | "webview__chat__chat-mention-external-source"
                | "webview__chat__chat-clear-context"
                | "webview__chat__chat-restore-default-context"
                | "webview__chat__chat-precompute-smart-paste"
                | "webview__chat__chat-smart-paste"
                | "webview__chat__chat-failed-smart-paste-resolve-file"
                | "webview__chat__chat-new-conversation"
                | "webview__chat__chat-edit-conversation-name"
                | "webview__chat__chat-use-action-find"
                | "webview__chat__chat-use-action-explain"
                | "webview__chat__chat-use-action-write-test"
                | "webview__chat__chat-codeblock-copy"
                | "webview__chat__chat-codeblock-create"
                | "webview__chat__chat-codeblock-go-to-file"
                | "webview__chat__chat-codespan-go-to-file"
                | "webview__chat__chat-codespan-go-to-symbol"
                | "webview__chat__chat-mermaidblock-initialize"
                | "webview__chat__chat-mermaidblock-toggle"
                | "webview__chat__chat-mermaidblock-interact"
                | "webview__chat__chat-mermaidblock-error"
                | "webview__chat__chat-use-suggested-question"
                | "webview__chat__chat-display-suggested-questions"
                | "webview__chat__chat-set-workspace-guidelines"
                | "webview__chat__chat-clear-workspace-guidelines"
                | "webview__chat__chat-set-user-guidelines"
                | "webview__chat__chat-clear-user-guidelines" => {
                    CLIENT_METRIC_COLLECTOR
                        .with_label_values(&[
                            client_metric,
                            user_agent,
                            tenant_info.metrics_tenant_name(),
                        ])
                        .inc_by(value);
                }
                // Latency metrics
                "generate_completion_latency"
                | "generate_edit_latency"
                | "generate_chat_latency"
                | "event_loop_delay"
                | "next_edit_bg_stream_finish_latency_ms"
                | "next_edit_bg_stream_partial_latency_ms"
                | "next_edit_bg_stream_cancel_latency_ms"
                | "next_edit_bg_stream_error_latency_ms"
                | "next_edit_bg_first_change_latency_ms"
                | "next_edit_bg_sufficient_noops_latency_ms"
                | "next_edit_bg_stream_preprocessing_latency_ms" => {
                    CLIENT_LATENCY_COLLECTOR
                        .with_label_values(&[
                            client_metric,
                            user_agent,
                            tenant_info.metrics_tenant_name(),
                        ])
                        .observe(value as f64 / 1000.0);
                }
                // Other metrics
                "theme" | "font_size" | "is_dark" => {
                    // Don't track these in Prometheus.
                }
                // Unsupported metrics go to a dedicated label
                _ => {
                    CLIENT_METRIC_COLLECTOR
                        .with_label_values(&[
                            "unsupported_metric_events",
                            user_agent,
                            tenant_info.metrics_tenant_name(),
                        ])
                        .inc();
                    if !unsupported_metrics.contains(client_metric) {
                        unsupported_metrics.insert(client_metric.to_string());
                        tracing::warn!("Unsupported client metric: {}", client_metric);
                    }
                }
            }
        }

        Ok(HttpResponse::Ok().json(ClientMetricsResponse {}))
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient> EndpointHandler<ReportErrorRequest>
    for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: ReportErrorRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, tonic::Status> {
        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;
        gate_on_circuit_breaker(&CB_REPORT_ERROR, &feature_flags, req, &tenant_info)?;

        // Error line to get this picked up by GCP Error Reporting
        tracing::error!(
            original_request_id = request.original_request_id,
            "Extension error: {}",
            request.sanitized_message,
        );

        let metadata = extract_request_metadata(
            &request_context,
            request_insight::RequestType::ExtensionError,
            &user,
            req,
        );
        self.request_insight_publisher
            .record_request_metadata(&request_context, &tenant_info, metadata)
            .await;
        self.request_insight_publisher
            .record_extension_error(
                &request_context,
                &tenant_info,
                request.original_request_id,
                request.sanitized_message,
                request.stack_trace,
                request
                    .diagnostics
                    .into_iter()
                    .map(|item| (item.key, item.value))
                    .collect(),
            )
            .await;

        Ok(HttpResponse::Ok().json(ReportErrorResponse {}))
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient> EndpointHandler<ReportFeatureVectorRequest>
    for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: ReportFeatureVectorRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, tonic::Status> {
        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;
        gate_on_circuit_breaker(&CB_REPORT_FEATURE_VECTOR, &feature_flags, req, &tenant_info)?;

        let source_ip = req
            .connection_info()
            .realip_remote_addr()
            .unwrap_or("unknown")
            .to_string();

        let user_agent = req
            .headers()
            .get("user-agent")
            .map(|h| h.to_str().unwrap_or("not-utf8"))
            .unwrap_or("")
            .to_string();

        let mut headers = std::collections::HashMap::new();
        for (name, value) in req.headers().iter() {
            let name_str = name.as_str();
            // Filter out authentication headers
            if matches!(name_str.to_lowercase().as_str(), "authorization") {
                continue;
            }
            if let Ok(value_str) = value.to_str() {
                headers.insert(name_str.to_string(), value_str.to_string());
            }
        }

        self.request_insight_publisher
            .record_session_events(
                &request_context,
                &tenant_info,
                &user,
                vec![request_insight::SessionEvent {
                    event_id: Some(Uuid::new_v4().to_string()),
                    time: Some(SystemTime::now().into()),
                    event: Some(request_insight::session_event::Event::FeatureVectorReport(
                        request_insight::FeatureVectorReport {
                            feature_vector: request.feature_vector,
                            source_ip,
                            user_agent,
                            headers,
                        },
                    )),
                }],
            )
            .await;

        Ok(HttpResponse::Ok().json(ReportFeatureVectorResponse {}))
    }
}

impl From<Language> for public_api_proto::Language {
    fn from(val: Language) -> Self {
        Self {
            name: val.name,
            vscode_name: val.vscode_name,
            extensions: val.extensions,
        }
    }
}

struct ModelWithConfig {
    model: Model,
    expose_internal_name: bool,
    is_default: bool,
}

impl From<&ModelWithConfig> for public_api_proto::Model {
    fn from(model_with_config: &ModelWithConfig) -> Self {
        let model = &model_with_config.model;
        let internal_name = if model_with_config.expose_internal_name {
            Some(model.name.clone())
        } else {
            None
        };
        Self {
            name: hex::encode(Sha256::digest(model.name.as_bytes())),
            internal_name,
            suggested_prefix_char_count: match &model.model_config {
                Inference(config) => config.suggested_prefix_char_count,
                Edit(config) => config.suggested_prefix_char_count,
                Chat(config) => config.suggested_prefix_char_count,
                NextEdit(config) => config.suggested_prefix_char_count,
            },
            suggested_suffix_char_count: match &model.model_config {
                Inference(config) => config.suggested_suffix_char_count,
                Edit(config) => config.suggested_suffix_char_count,
                Chat(config) => config.suggested_suffix_char_count,
                NextEdit(config) => config.suggested_suffix_char_count,
            },
            max_memorize_size_bytes: MAX_MEMORIZE_SIZE_BYTES,
            is_default: model_with_config.is_default,
        }
    }
}

impl From<Vec<ModelWithConfig>> for public_api_proto::GetModelsResponse {
    fn from(models_with_config: Vec<ModelWithConfig>) -> Self {
        let response_items = models_with_config.iter().map(|m| m.into()).collect();

        let default_model = models_with_config
            .iter()
            .max_by_key(|m| match &m.model.model_config {
                Inference(config) => config.model_priority,
                _ => -1,
            })
            .map(|m| hex::encode(Sha256::digest(m.model.name.as_bytes())));

        let languages: HashMap<String, Language> = models_with_config
            .into_iter()
            .filter_map(|m| match m.model.model_config {
                Inference(config) => Some(config.languages),
                _ => None,
            })
            .flatten()
            .map(|l| (l.name.clone(), l))
            .collect();

        public_api_proto::GetModelsResponse {
            default_model,
            models: response_items,
            languages: languages.into_iter().map(|l| l.1.into()).collect(),
            // will be set later
            feature_flags: None,
            // will be set later
            user_tier: public_api_proto::get_models_response::UserTier::Unknown.into(),
        }
    }
}

pub async fn get_models_auth<MR: ModelRegistry, CNC: ContentManagerClient>(
    data: web::Data<Handler<MR, CNC>>,
    req: HttpRequest,
) -> HttpResponse {
    handle_response(&req, data.get_models(&req).await).await
}

pub async fn health(_req: HttpRequest) -> HttpResponse {
    HttpResponse::Ok().body("Ok")
}

pub async fn token<MR: ModelRegistry, CNC: ContentManagerClient>(
    data: web::Data<Handler<MR, CNC>>,
    body: web::Bytes,
    req: HttpRequest,
) -> Result<HttpResponse, actix_web::Error> {
    // Direct all requests to auth-central.
    let config = &data.config;
    let auth_url = config.auth_central_url.clone();

    // Construct the full URL for the forward request, including query parameters
    let mut forward_url = Url::parse(&format!("{}/token", auth_url)).map_err(|e| {
        tracing::error!("Failed to parse auth URL '{}': {}", auth_url, e);
        actix_web::error::ErrorInternalServerError("Invalid auth URL configuration")
    })?;
    if let Some(query) = req.uri().query() {
        forward_url.set_query(Some(query));
    }
    // Create a new client
    let use_ssl = forward_url.scheme() == "https";
    let client = if use_ssl {
        let connector = AwcConnector::new().rustls_0_23(Arc::new(data.ssl_config.get_config()));

        AwcClient::builder().connector(connector).finish()
    } else {
        AwcClient::default()
    };

    // Forward the request
    let mut forward_req = client.post(forward_url.as_str());
    for header in req.headers() {
        if header.0 != "host" {
            // Skip the original Host header
            forward_req = forward_req.append_header(header);
        }
    }
    // Set the Host header to the forwarded URL
    if let Some(host) = forward_url.host_str() {
        forward_req = forward_req.append_header(("Host", host));
    }
    let forward_response = forward_req.send_body(body).await.map_err(|e| {
        tracing::error!("Send body error: {}", e);
        actix_web::error::ErrorInternalServerError("Failed to forward request")
    })?;

    // Return response from forwarded request
    let mut response = HttpResponse::build(forward_response.status());
    for header in forward_response.headers() {
        response.append_header(header);
    }
    Ok(response.streaming(forward_response))
}

pub fn serialize_user_tier<S>(user_tier: &i32, serializer: S) -> Result<S::Ok, S::Error>
where
    S: Serializer,
{
    UserTier::try_from(*user_tier)
        .map_err(|_| serde::ser::Error::custom(format!("invalid user tier type: {user_tier}")))?
        .as_str_name()
        .serialize(serializer)
}

pub fn deserialize_user_tier<'de, D>(deserializer: D) -> Result<i32, D::Error>
where
    D: Deserializer<'de>,
{
    let user_tier_str = String::deserialize(deserializer)?;
    UserTier::from_str_name(&user_tier_str)
        .map(|user_tier| user_tier as i32)
        .ok_or_else(|| serde::de::Error::custom(format!("invalid user tier: {user_tier_str}")))
}

pub fn register_handler_flags(registry: &feature_flags::RegistryHandle) {
    EDIT_MODEL_FLAG
        .register(registry)
        .expect("Registering EDIT_MODEL_FLAG");
    INSTRUCTION_MODEL_FLAG
        .register(registry)
        .expect("Registering INSTRUCTION_MODEL_FLAG");
    SMART_PASTE_MODEL_FLAG
        .register(registry)
        .expect("Registering SMART_PASTE_MODEL_FLAG");
    ENABLE_PROMPT_ENHANCER
        .register(registry)
        .expect("Registering ENABLE_PROMPT_ENHANCER");
}

#[cfg(test)]
mod tests {
    use hmac::{Hmac, Mac};
    use itertools::Itertools;
    use std::sync::Arc;

    use crate::agents_client;
    use crate::augment::model_instance_config::ModelType;
    use crate::base::blob_names::Blobs;
    use crate::completion::{
        CompletionRequest, FindMissingRequest as CompletionFindMissingRequest, RecencyInfo,
        TabSwitchEvent,
    };
    use crate::config::Config;
    use crate::edit::EditRequest;
    use crate::handler_utils::tests::{
        create_model_instance_config, new_api_auth, new_root_span, setup_app_state, setup_req,
        DEFAULT_PREFIX_CHAR_COUNT, DEFAULT_SUFFIX_CHAR_COUNT,
    };
    use crate::handler_utils::{handle_api_auth, Handler};
    use crate::handlers::get_models_auth;
    use crate::handlers_external_sources::ExternalSourceClients;
    use crate::model_registry::tests::{create_registry, FakeClientFactory};
    use crate::model_registry::{DynamicModelRegistry, ModelRegistry};
    use crate::public_api_proto::NextEditResolution;
    use crate::request_insight_util::tests::{
        ClientTimelineData, CompletionData, EditResolutionData, FakeRequestInsightPublisher,
        NextEditResolutionData, PreferenceData,
    };
    use actix_web::{body, http, web, HttpMessage};
    use blob_names::BlobName;
    use content_manager_client::MockContentManagerClient;
    use request_insight_publisher::auth_entities;
    use secrecy::{SecretString, SecretVec};

    use super::*;

    async fn add_inference_model(registry: &DynamicModelRegistry) {
        registry
            .update_models(vec![create_model_instance_config(
                "model1",
                ModelType::Inference,
                10,
            )])
            .await
            .unwrap();
    }

    async fn add_inference_models(registry: &DynamicModelRegistry) {
        registry
            .update_models(vec![
                create_model_instance_config("model1", ModelType::Inference, 10),
                create_model_instance_config("model2", ModelType::Inference, 1),
            ])
            .await
            .unwrap();
    }

    async fn add_instruction_model(registry: &DynamicModelRegistry) {
        registry
            .update_models(vec![
                create_model_instance_config("model1Instruction", ModelType::Edit, 1),
                create_model_instance_config("unavailableInstructionModel", ModelType::Edit, 1),
            ])
            .await
            .unwrap();
    }

    async fn add_edit_model(registry: &DynamicModelRegistry) {
        registry
            .update_models(vec![create_model_instance_config(
                "model1Edit",
                ModelType::Edit,
                1,
            )])
            .await
            .unwrap();
    }

    async fn add_chat_models(registry: &DynamicModelRegistry) {
        registry
            .update_models(vec![
                create_model_instance_config("model1Chat", ModelType::Chat, 5),
                create_model_instance_config("model2Chat", ModelType::Chat, 2),
            ])
            .await
            .unwrap();
    }

    async fn add_multiple_models(registry: &DynamicModelRegistry) {
        registry
            .update_models(vec![
                create_model_instance_config("model1Inference", ModelType::Inference, 10),
                create_model_instance_config("model2Inference", ModelType::Inference, 1),
                create_model_instance_config("model1Chat", ModelType::Chat, 5),
                create_model_instance_config("model2Chat", ModelType::Chat, 2),
            ])
            .await
            .unwrap();
    }

    async fn get_last_find_missing_request_for_completion_model(
        model_name: &str,
        fake_client_factory: Arc<FakeClientFactory>,
    ) -> CompletionFindMissingRequest {
        fake_client_factory
            .fake_clients
            .lock()
            .unwrap()
            .iter()
            .find(|c| c.model_name.as_ref() == model_name)
            .expect("model not found")
            .get_last_find_missing_request()
    }

    async fn get_last_find_missing_request_for_chat_model(
        model_name: &str,
        fake_client_factory: Arc<FakeClientFactory>,
    ) -> ChatFindMissingRequest {
        fake_client_factory
            .fake_chat_clients
            .lock()
            .unwrap()
            .iter()
            .find(|c| c.model_name.as_ref() == model_name)
            .expect("model not found")
            .get_last_find_missing_request()
    }

    #[actix_web::test]
    async fn test_complete_model_not_found() {
        let fake_client_factory = Arc::new(FakeClientFactory::new());
        let registry = Arc::new(create_registry(fake_client_factory));
        let config = Config::default();
        let api_auth = new_api_auth();
        let request_insight_publisher = Arc::new(FakeRequestInsightPublisher::new());
        let content_manager_client = MockContentManagerClient::new();
        let external_source_clients = ExternalSourceClients::new_for_test();
        let share_client = Arc::new(share_client::MockShareClient::new());
        let agents_client = Arc::new(agents_client::MockAgentsClient::new());
        let remote_agents_client = Arc::new(remote_agents_client::MockRemoteAgentsClient::new());
        let github_processor_client =
            Arc::new(github_processor_client::MockGithubProcessorClient::new());
        let app_state = web::Data::new(Handler::new(
            config,
            api_auth,
            registry,
            request_insight_publisher,
            content_manager_client,
            external_source_clients,
            feature_flags::setup_local(),
            share_client,
            agents_client,
            remote_agents_client,
            github_processor_client,
            None,
        ));

        let c = public_api_proto::CompletionRequest {
            model: Some("model1".to_string()),
            prompt: "def quicksort".to_string(),
            suffix: None,
            path: "quicksort.py".to_string(),
            ..Default::default()
        };

        let req = setup_req();

        let root_span = new_root_span();
        let resp = handle_api_auth(
            app_state.clone(),
            req.clone(),
            web::Json(c.clone()),
            root_span,
        )
        .await;
        assert_eq!(resp.status(), http::StatusCode::NOT_FOUND);
    }

    #[actix_web::test]
    async fn test_complete_feature_flag_model_not_found() {
        let fake_client_factory = Arc::new(FakeClientFactory::new());
        let registry = Arc::new(create_registry(fake_client_factory));
        let config = Config::default();
        let api_auth = new_api_auth();
        let request_insight_publisher = Arc::new(FakeRequestInsightPublisher::new());
        let content_manager_client = MockContentManagerClient::new();
        let feature_flags_handle = feature_flags::setup_local();
        let external_source_clients = ExternalSourceClients::new_for_test();
        let share_client = Arc::new(share_client::MockShareClient::new());
        let agents_client = Arc::new(agents_client::MockAgentsClient::new());
        let remote_agents_client = Arc::new(remote_agents_client::MockRemoteAgentsClient::new());
        let github_processor_client =
            Arc::new(github_processor_client::MockGithubProcessorClient::new());
        let app_state = web::Data::new(Handler::new(
            config,
            api_auth,
            registry,
            request_insight_publisher,
            content_manager_client,
            external_source_clients,
            feature_flags_handle.clone(),
            share_client,
            agents_client,
            remote_agents_client,
            github_processor_client,
            None,
        ));

        MODEL_FLAG.set_local(&feature_flags_handle, "invalid-model");

        let c = public_api_proto::CompletionRequest {
            model: None,
            prompt: "def quicksort".to_string(),
            suffix: None,
            path: "quicksort.py".to_string(),
            ..Default::default()
        };

        let req = setup_req();
        let root_span = new_root_span();
        let resp = handle_api_auth(
            app_state.clone(),
            req.clone(),
            web::Json(c.clone()),
            root_span,
        )
        .await;
        assert_eq!(resp.status(), http::StatusCode::INTERNAL_SERVER_ERROR);
    }

    async fn run_edit(
        request: &public_api_proto::EditRequest,
    ) -> (EditRequest, public_api_proto::EditResponse) {
        let (resp, fake_client_factory) = run_edit_raw(request).await;
        assert_eq!(resp.status(), http::StatusCode::OK);
        let result: public_api_proto::EditResponse =
            serde_json::from_slice(&body::to_bytes(resp.into_body()).await.unwrap()).unwrap();
        let modelhost_request = get_last_edit_request(fake_client_factory).await;
        (modelhost_request, result)
    }

    async fn run_edit_raw(
        request: &public_api_proto::EditRequest,
    ) -> (HttpResponse, Arc<FakeClientFactory>) {
        let fake_client_factory = Arc::new(FakeClientFactory::new());
        let registry = Arc::new(create_registry(fake_client_factory.clone()));
        let config = Config::default();
        let api_auth = new_api_auth();
        let request_insight_publisher = Arc::new(FakeRequestInsightPublisher::new());
        let content_manager_client = MockContentManagerClient::new();
        let external_source_clients = ExternalSourceClients::new_for_test();
        let share_client = Arc::new(share_client::MockShareClient::new());
        let agents_client = Arc::new(agents_client::MockAgentsClient::new());
        let remote_agents_client = Arc::new(remote_agents_client::MockRemoteAgentsClient::new());
        let github_processor_client =
            Arc::new(github_processor_client::MockGithubProcessorClient::new());
        let app_state = web::Data::new(Handler::new(
            config,
            api_auth,
            registry,
            request_insight_publisher,
            content_manager_client,
            external_source_clients,
            feature_flags::setup_local(),
            share_client,
            agents_client,
            remote_agents_client,
            github_processor_client,
            None,
        ));
        let req = setup_req();
        {
            let registry = app_state.model_registry.clone();
            add_edit_model(&registry).await;
        }
        let root_span = new_root_span();
        let resp = handle_api_auth(
            app_state.clone(),
            req,
            web::Json(request.clone()),
            root_span,
        )
        .await;
        (resp, fake_client_factory)
    }

    async fn run_instruction_stream_raw(
        request: &public_api_proto::InstructionRequest,
    ) -> (HttpResponse, Arc<FakeClientFactory>) {
        let fake_client_factory = Arc::new(FakeClientFactory::new());
        let registry = Arc::new(create_registry(fake_client_factory.clone()));
        let config = Config::default();
        let api_auth = new_api_auth();
        let request_insight_publisher = Arc::new(FakeRequestInsightPublisher::new());
        let content_manager_client = MockContentManagerClient::new();
        let external_source_clients = ExternalSourceClients::new_for_test();
        let share_client = Arc::new(share_client::MockShareClient::new());
        let agents_client = Arc::new(agents_client::MockAgentsClient::new());
        let remote_agents_client = Arc::new(remote_agents_client::MockRemoteAgentsClient::new());
        let local_feature_flags = feature_flags::setup_local();
        let github_processor_client =
            Arc::new(github_processor_client::MockGithubProcessorClient::new());
        let app_state = web::Data::new(Handler::new(
            config,
            api_auth,
            registry,
            request_insight_publisher,
            content_manager_client,
            external_source_clients,
            local_feature_flags.clone(),
            share_client,
            agents_client,
            remote_agents_client,
            github_processor_client,
            None,
        ));
        INSTRUCTION_MODEL_FLAG.set_local(&local_feature_flags, "model1Instruction");

        let req = setup_req();
        {
            let registry = app_state.model_registry.clone();
            add_instruction_model(&registry).await;
        }
        let root_span = new_root_span();
        let resp = instruction_stream_api_auth(
            app_state.clone(),
            req,
            web::Json(request.clone()),
            root_span,
        )
        .await;
        (resp, fake_client_factory)
    }

    async fn get_last_edit_request(fake_client_factory: Arc<FakeClientFactory>) -> EditRequest {
        match fake_client_factory.fake_edit_clients.lock().unwrap().last() {
            Some(client) => client.get_last_edit_request(),
            None => panic!("No client"),
        }
    }

    #[actix_web::test]
    async fn test_json_parse_empty_recency_object() {
        let json_str = r#"{"path": "", "prompt": "foo", "memories": [], "recency_info": {}}"#;
        let front: public_api_proto::CompletionRequest =
            serde_json::from_str(json_str).expect(json_str);
        let model: String = "model".to_string();
        let back: CompletionRequest = (front, &model).try_into().expect("try into");

        assert!(back
            .recency_info
            .as_ref()
            .unwrap()
            .tab_switch_events
            .is_empty());
        assert!(back
            .recency_info
            .as_ref()
            .unwrap()
            .git_diff_file_info
            .is_empty());
    }

    #[actix_web::test]
    async fn test_json_parse_user_event() {
        let json_str = r#"{"time": "2024-03-31T20:46:15Z", "file_path": "file.py", "edit_request_id_issued": {"request_id": "test-request"}}"#;
        let user_event: request_insight::FullExportUserEvent =
            serde_json::from_str(json_str).expect(json_str);
        assert!(user_event.event.is_some());
        assert!(matches!(
            user_event.event.unwrap(),
            request_insight::full_export_user_event::Event::EditRequestIdIssued(
                request_insight::EditRequestIdIssuedEvent { request_id: x, file_path: None }
            ) if &x == "test-request"
        ));
    }

    #[actix_web::test]
    async fn test_edit_model_not_specified() {
        let (modelhost_request, response) = run_edit(&public_api_proto::EditRequest {
            model: None,
            selected_text: "fn sort(&vec: Vec<u8>) {}".to_string(),
            instruction: "Add useful comments".to_string(),
            sequence_id: Some(0),
            ..Default::default()
        })
        .await;

        assert_eq!(response.text, "hello");
        print!("{modelhost_request:?}");
        // default values, including model_name, filled in
        assert_eq!(
            modelhost_request,
            EditRequest {
                model_name: "model1Edit".to_string(),
                selected_text: "fn sort(&vec: Vec<u8>) {}".to_string(),
                instruction: "Add useful comments".to_string(),
                suffix: "".to_string(),
                path: "".to_string(),
                blobs: Some(blob_names_proto::Blobs::default()),
                lang: "".to_string(),
                position: None,
                sequence_id: 0,
                ..EditRequest::default()
            }
        );
    }

    #[actix_web::test]
    async fn test_instruction_stream() {
        let request = &public_api_proto::InstructionRequest {
            instruction: "Add useful comments".to_string(),
            blobs: Some(public_api_proto::Blobs::default()),
            ..Default::default()
        };
        let (resp, _): (HttpResponse, Arc<FakeClientFactory>) =
            run_instruction_stream_raw(request).await;
        assert_eq!(resp.status(), http::StatusCode::OK);
    }

    #[actix_web::test]
    async fn test_instruction_stream_status_code_error() {
        let big_message = "a".repeat(2048);
        let request = &public_api_proto::InstructionRequest {
            selected_text: big_message,
            instruction: "Add useful comments".to_string(),
            blobs: Some(public_api_proto::Blobs::default()),
            ..Default::default()
        };
        let (resp, _): (HttpResponse, Arc<FakeClientFactory>) =
            run_instruction_stream_raw(request).await;
        assert_eq!(resp.status(), http::StatusCode::PAYLOAD_TOO_LARGE);
    }

    #[actix_web::test]
    async fn test_instruction_stream_fallback() {
        let request = &public_api_proto::InstructionRequest {
            instruction: "Add useful comments".to_string(),
            blobs: Some(public_api_proto::Blobs::default()),
            ..Default::default()
        };
        let fake_client_factory: Arc<FakeClientFactory> = Arc::new(FakeClientFactory::new());
        let app_state = setup_app_state(fake_client_factory.clone());
        INSTRUCTION_MODEL_FLAG.set_local(&app_state.feature_flags, "unavailableInstructionModel");
        // Model with this name always fails

        let req = setup_req();
        {
            let registry = app_state.model_registry.clone();
            add_instruction_model(&registry).await;
        }
        let root_span = new_root_span();

        // No fallback, fails
        let resp = instruction_stream_api_auth(
            app_state.clone(),
            req.clone(),
            web::Json(request.clone()),
            root_span.clone(),
        )
        .await;
        assert_eq!(resp.status(), http::StatusCode::SERVICE_UNAVAILABLE);

        // Add fallback, succeeds
        INSTRUCTION_FALLBACK_MODELS_FLAG.set_local(&app_state.feature_flags, "model1Instruction");
        let resp = instruction_stream_api_auth(
            app_state.clone(),
            req,
            web::Json(request.clone()),
            root_span,
        )
        .await;
        assert_eq!(resp.status(), http::StatusCode::OK);
    }

    #[actix_web::test]
    async fn test_edit_model_with_blobs() {
        let default_frontend_req = public_api_proto::EditRequest {
            model: None,
            selected_text: "fn sort(&vec: Vec<u8>) {}".to_string(),
            instruction: "Add useful comments".to_string(),
            sequence_id: Some(0),
            ..Default::default()
        };

        let default_edit_req = EditRequest {
            model_name: "model1Edit".to_string(),
            selected_text: "fn sort(&vec: Vec<u8>) {}".to_string(),
            instruction: "Add useful comments".to_string(),
            suffix: "".to_string(),
            path: "".to_string(),
            lang: "".to_string(),
            position: None,
            sequence_id: 0,
            ..EditRequest::default()
        };

        let blob1 = BlobName::from_bytes(&[0; 32]).unwrap();

        for use_blob_names in [true, false] {
            for use_blobs in [true, false] {
                // Test all of the different combinations
                println!(
                    "use_blob_names: {}, use_blobs: {}",
                    use_blob_names, use_blobs
                );

                let blobs = if use_blobs {
                    Some(public_api_proto::Blobs {
                        checkpoint_id: None,
                        added_blobs: vec![String::from(&blob1)],
                        deleted_blobs: vec![],
                    })
                } else {
                    None
                };
                let blob_names = if use_blob_names {
                    vec![String::from(&blob1)]
                } else {
                    vec![]
                };

                // No matter how the API gets the blobs from the client, we want
                // to send this to the rest of the backend
                let blobs_out = if use_blob_names || use_blobs {
                    Blobs {
                        baseline_checkpoint_id: None,
                        added: vec![blob1.as_bytes().to_vec()],
                        deleted: vec![],
                    }
                } else {
                    Blobs {
                        baseline_checkpoint_id: None,
                        added: vec![],
                        deleted: vec![],
                    }
                };

                #[allow(deprecated)]
                let frontend_req = public_api_proto::EditRequest {
                    blob_names,
                    blobs,
                    ..default_frontend_req.clone()
                };

                #[allow(deprecated)]
                let edit_req = EditRequest {
                    blobs: Some(blobs_out),
                    ..default_edit_req.clone()
                };

                let (modelhost_request, _response) = run_edit(&frontend_req).await;
                print!("{modelhost_request:?}");
                assert_eq!(modelhost_request, edit_req);
            }
        }
    }

    #[actix_web::test]
    async fn test_resolve_edit() {
        let fake_client_factory = Arc::new(FakeClientFactory::new());
        let registry = Arc::new(create_registry(fake_client_factory));
        let config = Config::default();
        let api_auth = new_api_auth();
        let request_insight_publisher = Arc::new(FakeRequestInsightPublisher::new());
        let content_manager_client = MockContentManagerClient::new();
        let external_source_clients = ExternalSourceClients::new_for_test();
        let share_client = Arc::new(share_client::MockShareClient::new());
        let agents_client = Arc::new(agents_client::MockAgentsClient::new());
        let remote_agents_client = Arc::new(remote_agents_client::MockRemoteAgentsClient::new());
        let github_processor_client =
            Arc::new(github_processor_client::MockGithubProcessorClient::new());
        let app_state = web::Data::new(Handler::new(
            config,
            api_auth,
            registry,
            request_insight_publisher.clone(),
            content_manager_client,
            external_source_clients,
            feature_flags::setup_local(),
            share_client,
            agents_client,
            remote_agents_client,
            github_processor_client,
            None,
        ));
        let req = setup_req();
        let request_id = RequestId::create_random();
        let resolution = EditResolution {
            request_id: request_id.to_string(),
            emit_time_sec: 100,
            emit_time_nsec: 101,
            resolve_time_sec: 102,
            resolve_time_nsec: 103,
            is_accepted: true,
            annotated_text: Some("hello".to_string()),
            annotated_instruction: Some("Useful comment".to_string()),
            ..EditResolution::default()
        };
        let root_span = new_root_span();
        let resp = handle_api_auth(
            app_state.clone(),
            req,
            actix_web::web::Json(resolution.clone()),
            root_span,
        )
        .await;
        assert_eq!(resp.status(), http::StatusCode::OK);

        match request_insight_publisher._get_edit_resolution_data(&request_id) {
            Some(data) => {
                verify_edit_resolution_data(&data, &resolution);
            }
            None => {
                panic!();
            }
        }
    }

    #[actix_web::test]
    async fn test_record_preference_sample() {
        let fake_client_factory = Arc::new(FakeClientFactory::new());
        let registry = Arc::new(create_registry(fake_client_factory));
        let config = Config::default();
        let api_auth = new_api_auth();
        let request_insight_publisher = Arc::new(FakeRequestInsightPublisher::new());
        let content_manager_client = MockContentManagerClient::new();
        let external_source_clients = ExternalSourceClients::new_for_test();
        let share_client = Arc::new(share_client::MockShareClient::new());
        let agents_client = Arc::new(agents_client::MockAgentsClient::new());
        let remote_agents_client = Arc::new(remote_agents_client::MockRemoteAgentsClient::new());
        let github_processor_client =
            Arc::new(github_processor_client::MockGithubProcessorClient::new());
        let app_state = web::Data::new(Handler::new(
            config,
            api_auth,
            registry,
            request_insight_publisher.clone(),
            content_manager_client,
            external_source_clients,
            feature_flags::setup_local(),
            share_client,
            agents_client,
            remote_agents_client,
            github_processor_client,
            None,
        ));
        let req = setup_req();
        let mut scores = HashMap::new();
        scores.insert("score1".to_string(), 1);
        scores.insert("score2".to_string(), 2);

        let sample = PreferenceSample {
            request_ids: [
                RequestId::create_random().to_string(),
                RequestId::create_random().to_string(),
            ]
            .to_vec(),
            scores,
            feedback: "some feedback".to_string(),
        };
        let root_span = new_root_span();
        let resp = handle_api_auth(
            app_state.clone(),
            req.clone(),
            actix_web::web::Json(sample.clone()),
            root_span,
        )
        .await;
        assert_eq!(resp.status(), http::StatusCode::OK);

        let request_context = req.extensions().get::<RequestContext>().unwrap().clone();
        let request_id = request_context.request_id();
        match request_insight_publisher._get_preference_data(&request_id) {
            Some(data) => {
                verify_preference_data(&data, &sample);
            }
            None => {
                panic!();
            }
        }
    }

    async fn run_memorization(request: &public_api_proto::MemorizeRequest) -> MemorizeResponse {
        let fake_client_factory = Arc::new(FakeClientFactory::new());
        let registry = Arc::new(create_registry(fake_client_factory));
        let config = Config::default();
        let api_auth = new_api_auth();
        let request_insight_publisher = Arc::new(FakeRequestInsightPublisher::new());
        let mut content_manager_client = MockContentManagerClient::new();
        content_manager_client
            .expect_upload_blob_content()
            .returning(|_, path, content, _| Ok(blob_names::get_blob_name(path, content)));
        let external_source_clients = ExternalSourceClients::new_for_test();
        let share_client = Arc::new(share_client::MockShareClient::new());
        let agents_client = Arc::new(agents_client::MockAgentsClient::new());
        let remote_agents_client = Arc::new(remote_agents_client::MockRemoteAgentsClient::new());
        let github_processor_client =
            Arc::new(github_processor_client::MockGithubProcessorClient::new());
        let app_state = web::Data::new(Handler::new(
            config,
            api_auth,
            registry,
            request_insight_publisher,
            content_manager_client,
            external_source_clients,
            feature_flags::setup_local(),
            share_client,
            agents_client,
            remote_agents_client,
            github_processor_client,
            None,
        ));
        add_inference_model(&app_state.model_registry).await;
        let req = setup_req();
        let root_span = new_root_span();
        let resp = handle_api_auth(
            app_state.clone(),
            req,
            web::Json(request.clone()),
            root_span,
        )
        .await;
        assert_eq!(resp.status(), http::StatusCode::OK);
        serde_json::from_slice(&body::to_bytes(resp.into_body()).await.unwrap()).unwrap()
    }

    async fn run_batch_upload(request: &BatchUploadRequest) -> BatchUploadResponse {
        let fake_client_factory = Arc::new(FakeClientFactory::new());
        let registry = Arc::new(create_registry(fake_client_factory));
        let config = Config::default();
        let api_auth = new_api_auth();
        let request_insight_publisher = Arc::new(FakeRequestInsightPublisher::new());
        let mut content_manager_client = MockContentManagerClient::new();
        content_manager_client
            .expect_batch_upload_blob_content()
            .returning(move |_, blobs, _| {
                Ok(blobs
                    .iter()
                    .map(|b| BlobName::new_from_contents(&b.path, &b.content))
                    .collect())
            });
        let external_source_clients = ExternalSourceClients::new_for_test();
        let share_client = Arc::new(share_client::MockShareClient::new());
        let agents_client = Arc::new(agents_client::MockAgentsClient::new());
        let remote_agents_client = Arc::new(remote_agents_client::MockRemoteAgentsClient::new());
        let github_processor_client =
            Arc::new(github_processor_client::MockGithubProcessorClient::new());
        let app_state = web::Data::new(Handler::new(
            config,
            api_auth,
            registry,
            request_insight_publisher,
            content_manager_client,
            external_source_clients,
            feature_flags::setup_local(),
            share_client,
            agents_client,
            remote_agents_client,
            github_processor_client,
            None,
        ));
        let req = setup_req();

        add_inference_models(&app_state.model_registry).await;
        let root_span = new_root_span();
        let resp = handle_api_auth(
            app_state.clone(),
            req,
            web::Json(request.clone()),
            root_span,
        )
        .await;
        assert_eq!(resp.status(), http::StatusCode::OK);
        serde_json::from_slice(&body::to_bytes(resp.into_body()).await.unwrap()).unwrap()
    }

    #[actix_web::test]
    async fn test_memorize() {
        #[allow(deprecated)]
        let request = public_api_proto::MemorizeRequest {
            model: "model1".to_string(),
            t: "text".to_string(),
            path: "foo/bar.txt".to_string(),
            blob_name: None,
        };
        let response = run_memorization(&request).await;
        let blob_name = BlobName::new_from_contents(
            &SecretString::new("foo/bar.txt".to_string()),
            &SecretVec::new(b"text".to_vec()),
        );

        assert_eq!(response.mem_object_name, blob_name.to_string());
    }

    #[actix_web::test]
    async fn test_memorize_model_not_found() {
        #[allow(deprecated)]
        let request = public_api_proto::MemorizeRequest {
            model: "model2".to_string(),
            t: "text".to_string(),
            path: "foo/bar.txt".to_string(),
            blob_name: None,
        };

        let response = run_memorization(&request).await;
        let blob_name = BlobName::new_from_contents(
            &SecretString::new("foo/bar.txt".to_string()),
            &SecretVec::new(b"text".to_vec()),
        );

        assert_eq!(response.mem_object_name, blob_name.to_string());
    }

    #[actix_web::test]
    async fn test_batch_upload() {
        let request = BatchUploadRequest {
            blobs: vec![public_api_proto::UploadBlob {
                content: "text".to_string(),
                path: "foo/bar.txt".to_string(),
                blob_name: None,
            }],
        };
        let response = run_batch_upload(&request).await;
        let blob_name = String::from(&BlobName::new_from_contents(
            &SecretString::new("foo/bar.txt".to_string()),
            &SecretVec::new(b"text".to_vec()),
        ));

        assert_eq!(response.blob_names, vec![blob_name]);
    }

    #[actix_web::test]
    async fn test_memorize_with_blob_name() {
        let path = "foo/bar.txt".to_string();
        let blob_name = BlobName::new_from_contents(
            &SecretString::new(path.clone()),
            &SecretVec::new(b"text".to_vec()),
        );
        #[allow(deprecated)]
        let request = public_api_proto::MemorizeRequest {
            model: "model1".to_string(),
            t: "text".to_string(),
            path,
            blob_name: Some(String::from(&blob_name)),
        };
        let response = run_memorization(&request).await;

        assert_eq!(response.mem_object_name, blob_name.to_string());
    }

    #[actix_web::test]
    async fn test_blob_name_too_long() {
        let path = SecretString::new("foo/bar.txt".to_string());
        let short = BlobName::new_from_contents(&path, &SecretVec::new(b"text".to_vec()));
        let short_blob_name = String::from(&short);

        let long_blob_name = format!("{}+", short_blob_name);
        BlobName::new(&long_blob_name)
            .expect_err("blob name longer than sha256 length should fail");
    }

    async fn run_find_missing_completions(
        request: &public_api_proto::FindMissingRequest,
        model_flag_value: &str,
        expected_model: &str,
    ) -> (
        CompletionFindMissingRequest,
        public_api_proto::FindMissingResponse,
    ) {
        let fake_client_factory = Arc::new(FakeClientFactory::new());
        let registry = Arc::new(create_registry(fake_client_factory.clone()));
        let config = Config::default();
        let api_auth = new_api_auth();
        let request_insight_publisher = Arc::new(FakeRequestInsightPublisher::new());
        let mut content_manager_client = MockContentManagerClient::new();
        content_manager_client
            .expect_find_missing_blobs()
            .returning(|_, blob_names, _| {
                Ok(blob_names
                    .iter()
                    .filter(|n| n.contains("missing"))
                    .cloned()
                    .collect())
            });
        let feature_flags_handle = feature_flags::setup_local();
        let external_source_clients = ExternalSourceClients::new_for_test();
        let share_client = Arc::new(share_client::MockShareClient::new());
        let agents_client = Arc::new(agents_client::MockAgentsClient::new());
        let remote_agents_client = Arc::new(remote_agents_client::MockRemoteAgentsClient::new());
        let github_processor_client =
            Arc::new(github_processor_client::MockGithubProcessorClient::new());
        let app_state = web::Data::new(Handler::new(
            config,
            api_auth,
            registry,
            request_insight_publisher,
            content_manager_client,
            external_source_clients,
            feature_flags_handle.clone(),
            share_client,
            agents_client,
            remote_agents_client,
            github_processor_client,
            None,
        ));

        MODEL_FLAG.set_local(&feature_flags_handle, model_flag_value);

        let req = setup_req();

        add_inference_models(&app_state.model_registry).await;
        let root_span: RootSpan = new_root_span();
        let resp: HttpResponse = handle_api_auth(
            app_state.clone(),
            req.clone(),
            web::Json(request.clone()),
            root_span.clone(),
        )
        .await;
        assert_eq!(resp.status(), http::StatusCode::OK);
        let response: public_api_proto::FindMissingResponse =
            serde_json::from_slice(&body::to_bytes(resp.into_body()).await.unwrap()).unwrap();
        let find_last_completion_request = get_last_find_missing_request_for_completion_model(
            expected_model,
            fake_client_factory.clone(),
        )
        .await;
        (find_last_completion_request, response)
    }

    async fn run_find_missing_combined(
        request: &public_api_proto::FindMissingRequest,
        model_flag_value: &str,
        expected_completion_model: &str,
        expected_chat_model: &str,
        expected_http_status: http::StatusCode,
    ) -> (
        CompletionFindMissingRequest,
        ChatFindMissingRequest,
        public_api_proto::FindMissingResponse,
    ) {
        let fake_client_factory = Arc::new(FakeClientFactory::new());
        let registry = Arc::new(create_registry(fake_client_factory.clone()));
        let config = Config::default();
        let api_auth = new_api_auth();
        let request_insight_publisher = Arc::new(FakeRequestInsightPublisher::new());
        let mut content_manager_client = MockContentManagerClient::new();
        content_manager_client
            .expect_find_missing_blobs()
            .returning(|_, blob_names, _| {
                Ok(blob_names
                    .iter()
                    .filter(|n| n.contains("missing"))
                    .cloned()
                    .collect())
            });
        let feature_flags_handle = feature_flags::setup_local();
        let external_source_clients = ExternalSourceClients::new_for_test();
        let share_client = Arc::new(share_client::MockShareClient::new());
        let agents_client = Arc::new(agents_client::MockAgentsClient::new());
        let remote_agents_client = Arc::new(remote_agents_client::MockRemoteAgentsClient::new());
        let github_processor_client =
            Arc::new(github_processor_client::MockGithubProcessorClient::new());
        let app_state = web::Data::new(Handler::new(
            config,
            api_auth,
            registry,
            request_insight_publisher,
            content_manager_client,
            external_source_clients,
            feature_flags_handle.clone(),
            share_client,
            agents_client,
            remote_agents_client,
            github_processor_client,
            None,
        ));

        MODEL_FLAG.set_local(&feature_flags_handle, model_flag_value);

        let req = setup_req();

        add_multiple_models(&app_state.model_registry).await;
        let root_span: RootSpan = new_root_span();
        let resp: HttpResponse = handle_api_auth(
            app_state.clone(),
            req.clone(),
            web::Json(request.clone()),
            root_span.clone(),
        )
        .await;
        let find_last_completion_request = get_last_find_missing_request_for_completion_model(
            expected_completion_model,
            fake_client_factory.clone(),
        )
        .await;
        let find_last_chat_request = get_last_find_missing_request_for_chat_model(
            expected_chat_model,
            fake_client_factory.clone(),
        )
        .await;

        assert_eq!(resp.status(), expected_http_status);

        if expected_http_status != http::StatusCode::OK {
            return (
                find_last_completion_request,
                find_last_chat_request,
                public_api_proto::FindMissingResponse::default(),
            );
        }

        let response: public_api_proto::FindMissingResponse =
            serde_json::from_slice(&body::to_bytes(resp.into_body()).await.unwrap()).unwrap();
        (
            find_last_completion_request,
            find_last_chat_request,
            response,
        )
    }

    async fn run_find_missing_chat(
        request: &public_api_proto::FindMissingRequest,
        model_flag_value: &str,
        expected_model: &str,
    ) -> (
        ChatFindMissingRequest,
        public_api_proto::FindMissingResponse,
    ) {
        let fake_client_factory = Arc::new(FakeClientFactory::new());
        let registry = Arc::new(create_registry(fake_client_factory.clone()));
        let config = Config::default();
        let api_auth = new_api_auth();
        let request_insight_publisher = Arc::new(FakeRequestInsightPublisher::new());
        let mut content_manager_client = MockContentManagerClient::new();
        content_manager_client
            .expect_find_missing_blobs()
            .returning(|_, blob_names, _| {
                Ok(blob_names
                    .iter()
                    .filter(|n| n.contains("missing"))
                    .cloned()
                    .collect())
            });
        let feature_flags_handle = feature_flags::setup_local();
        let external_source_clients = ExternalSourceClients::new_for_test();
        let share_client = Arc::new(share_client::MockShareClient::new());
        let agents_client = Arc::new(agents_client::MockAgentsClient::new());
        let remote_agents_client = Arc::new(remote_agents_client::MockRemoteAgentsClient::new());
        let github_processor_client =
            Arc::new(github_processor_client::MockGithubProcessorClient::new());
        let app_state = web::Data::new(Handler::new(
            config,
            api_auth,
            registry,
            request_insight_publisher,
            content_manager_client,
            external_source_clients,
            feature_flags_handle.clone(),
            share_client,
            agents_client,
            remote_agents_client,
            github_processor_client,
            None,
        ));

        CHAT_RAW_OUTPUT_MODEL_FLAG.set_local(&feature_flags_handle, model_flag_value);

        let req = setup_req();

        add_chat_models(&app_state.model_registry).await;
        let root_span: RootSpan = new_root_span();
        let resp: HttpResponse = handle_api_auth(
            app_state.clone(),
            req.clone(),
            web::Json(request.clone()),
            root_span.clone(),
        )
        .await;
        assert_eq!(resp.status(), http::StatusCode::OK);
        let response: public_api_proto::FindMissingResponse =
            serde_json::from_slice(&body::to_bytes(resp.into_body()).await.unwrap()).unwrap();
        let find_last_chat_request = get_last_find_missing_request_for_chat_model(
            expected_model,
            fake_client_factory.clone(),
        )
        .await;
        (find_last_chat_request, response)
    }

    async fn test_find_missing_completions_actual(
        front_end_model: &str,
        feature_flag_model: &str,
        expected_model: &str,
    ) {
        let request = public_api_proto::FindMissingRequest {
            model: Some(front_end_model.to_string()),
            mem_object_names: vec!["mem1".to_string(), "mem2".to_string()],
        };
        let (completion_request, response) =
            run_find_missing_completions(&request, feature_flag_model, expected_model).await;
        assert_eq!(
            vec!["mem1".to_string(), "mem2".to_string()],
            completion_request.blob_names
        );
        assert!(response.unknown_memory_names.is_empty());
        assert!(response.nonindexed_blob_names.is_empty());
    }

    async fn test_find_missing_chat_actual(
        front_end_model: &str,
        feature_flag_model: &str,
        expected_model: &str,
    ) {
        let request = public_api_proto::FindMissingRequest {
            model: Some(front_end_model.to_string()),
            mem_object_names: vec!["mem1".to_string(), "mem2".to_string()],
        };
        let (chat_request, response) =
            run_find_missing_chat(&request, feature_flag_model, expected_model).await;
        assert_eq!(
            vec!["mem1".to_string(), "mem2".to_string()],
            chat_request.blob_names
        );
        assert!(response.unknown_memory_names.is_empty());
        assert!(response.nonindexed_blob_names.is_empty());
    }

    #[actix_web::test]
    async fn test_find_missing_1() {
        test_find_missing_completions_actual("model2", "", "model2").await
    }

    #[actix_web::test]
    async fn test_find_missing_2() {
        test_find_missing_completions_actual("", "", "model1").await
    }

    #[actix_web::test]
    async fn test_find_missing_3() {
        test_find_missing_completions_actual("", "model2", "model2").await
    }

    #[actix_web::test]
    async fn test_find_missing_4() {
        test_find_missing_completions_actual("model2", "model1", "model2").await
    }

    #[actix_web::test]
    async fn test_find_missing_chat_1() {
        test_find_missing_chat_actual("model2Chat", "", "model2Chat").await
    }

    #[actix_web::test]
    async fn test_find_missing_chat_2() {
        test_find_missing_chat_actual("", "", "model1Chat").await
    }

    #[actix_web::test]
    async fn test_find_missing_chat_3() {
        test_find_missing_chat_actual("", "model2Chat", "model2Chat").await
    }

    #[actix_web::test]
    async fn test_find_missing_chat_4() {
        test_find_missing_chat_actual("model2Chat", "model1Chat", "model2Chat").await
    }

    #[actix_web::test]
    /*
       Completion and Chat models are both checked when the model is not specified
    */
    async fn test_find_missing_combined() {
        let request = public_api_proto::FindMissingRequest {
            model: Some("".to_string()),
            mem_object_names: vec![
                "missing1".to_string(),         // Missing and nonindexed everywhere
                "nonindexed_chat1".to_string(), // nonindexed in chat only
                "nonindexed1".to_string(),      // Nonindexed in all hosts
                "indexed1".to_string(),         // Indexed in all hosts
            ],
        };
        let (completion_request, chat_request, response) = run_find_missing_combined(
            &request,
            "",
            "model1Inference",
            "model1Chat",
            http::StatusCode::OK,
        )
        .await;
        assert_eq!(
            vec![
                "missing1".to_string(),
                "nonindexed_chat1".to_string(),
                "nonindexed1".to_string(),
                "indexed1".to_string()
            ],
            completion_request.blob_names
        );
        assert_eq!(
            vec![
                "missing1".to_string(),
                "nonindexed_chat1".to_string(),
                "nonindexed1".to_string(),
                "indexed1".to_string()
            ],
            chat_request.blob_names
        );
        assert!(response.unknown_memory_names == vec!["missing1".to_string()]);
        // nonindexed1 is deduped, missing1 is filtered because it's already in unknown_memory_names
        // the ordering is not guaranteed
        assert_eq!(
            response
                .nonindexed_blob_names
                .into_iter()
                .sorted()
                .collect::<Vec<_>>(),
            vec!["nonindexed1".to_string(), "nonindexed_chat1".to_string()]
        );
    }

    #[actix_web::test]
    /*
       Errors in chat find_missing are ignored
    */
    async fn test_find_missing_chat_errors_combined() {
        let request = public_api_proto::FindMissingRequest {
            model: Some("".to_string()),
            mem_object_names: vec![
                "nonindexed1".to_string(),
                "nonindexed_chat1".to_string(),
                "chat_error".to_string(), // Trigger error in chat find_missing
                "indexed1".to_string(),
            ],
        };
        let (_completion_request, _chat_request, response) = run_find_missing_combined(
            &request,
            "",
            "model1Inference",
            "model1Chat",
            http::StatusCode::OK,
        )
        .await;
        assert!(response.unknown_memory_names.is_empty());
        // nonindexed_chat1 was not returned because chat find_missing failed
        assert!(response.nonindexed_blob_names == vec!["nonindexed1".to_string()]);
    }

    #[actix_web::test]
    /*
       Errors in completion find_missing fail the request
    */
    async fn test_find_missing_completion_errors_combined() {
        let request = public_api_proto::FindMissingRequest {
            model: Some("".to_string()),
            mem_object_names: vec![
                "nonindexed1".to_string(),
                "completion_error".to_string(), // Trigger error in completion find_missing
                "indexed1".to_string(),
            ],
        };
        run_find_missing_combined(
            &request,
            "",
            "model1Inference",
            "model1Chat",
            http::StatusCode::INTERNAL_SERVER_ERROR,
        )
        .await;
    }

    async fn run_get_models(
        expose_internal_model_names: bool,
    ) -> public_api_proto::GetModelsResponse {
        let fake_client_factory = Arc::new(FakeClientFactory::new());
        let registry = Arc::new(create_registry(fake_client_factory));
        let config = Config {
            expose_internal_model_names,
            ..Default::default()
        };
        let api_auth = new_api_auth();
        let request_insight_publisher = Arc::new(FakeRequestInsightPublisher::new());
        let content_manager_client = MockContentManagerClient::new();
        let external_source_clients = ExternalSourceClients::new_for_test();
        let share_client = Arc::new(share_client::MockShareClient::new());
        let agents_client = Arc::new(agents_client::MockAgentsClient::new());
        let remote_agents_client = Arc::new(remote_agents_client::MockRemoteAgentsClient::new());
        let github_processor_client =
            Arc::new(github_processor_client::MockGithubProcessorClient::new());
        let app_state = web::Data::new(Handler::new(
            config,
            api_auth,
            registry,
            request_insight_publisher,
            content_manager_client,
            external_source_clients,
            feature_flags::setup_local(),
            share_client,
            agents_client,
            remote_agents_client,
            github_processor_client,
            None,
        ));
        let req = setup_req();

        add_inference_model(&app_state.model_registry).await;

        let resp = get_models_auth(app_state.clone(), req).await;
        assert_eq!(resp.status(), http::StatusCode::OK);
        serde_json::from_slice(&body::to_bytes(resp.into_body()).await.unwrap()).unwrap()
    }

    #[actix_web::test]
    async fn test_get_models() {
        let response = run_get_models(false).await;
        #[allow(deprecated)]
        let expected = public_api_proto::GetModelsResponse {
            default_model: Some(
                "22ff0efe270b305371c97346e0619c987cfd10a5bb7c3acfcd7c92790a9ca91c".to_string(),
            ),
            models: vec![public_api_proto::Model {
                name: "22ff0efe270b305371c97346e0619c987cfd10a5bb7c3acfcd7c92790a9ca91c"
                    .to_string(),
                internal_name: None,
                suggested_prefix_char_count: DEFAULT_PREFIX_CHAR_COUNT,
                suggested_suffix_char_count: DEFAULT_SUFFIX_CHAR_COUNT,
                max_memorize_size_bytes: 131072,
                is_default: false,
            }],
            languages: vec![public_api_proto::Language {
                name: "C".to_string(),
                vscode_name: "c".to_string(),
                extensions: vec![".c".to_string()],
            }],
            user_tier: public_api_proto::get_models_response::UserTier::Unknown.into(),
            feature_flags: Some(public_api_proto::get_models_response::FeatureFlags {
                enable_chat: Some(true),
                enable_instructions: Some(true),
                enable_smart_paste: Some(false),
                enable_smart_paste_min_version: Some("".to_string()),
                enable_view_text_document: Some(true),
                additional_chat_models: Some("".to_string()),
                enable_code_edits: Some(true),
                checkpoint_blobs_v2: Some(true),
                enable_data_collection: Some(false),
                small_sync_threshold: Some(15),
                big_sync_threshold: Some(1000),
                enable_workspace_manager_ui: Some(false),
                enable_intellij_chat: Some(false),
                enable_external_sources_in_chat: Some(true),
                bypass_language_filter: Some(true),
                enable_workspace_manager_ui_launch: Some(false),
                intellij_chat_min_version: Some("".to_string()),
                vscode_direct_apply_min_version: Some("".to_string()),
                vscode_next_edit_min_version: Some("".to_string()),
                vscode_next_edit_bottom_panel_min_version: Some("".to_string()),
                vscode_next_edit_ux1_max_version: Some("".to_string()),
                vscode_next_edit_ux2_max_version: Some("".to_string()),
                vscode_flywheel_min_version: Some("".to_string()),
                enable_hindsight: Some(false),
                max_upload_size_bytes: Some(MAX_MEMORIZE_SIZE_BYTES as i64),
                intellij_force_completion_min_version: Some("".to_string()),
                vscode_external_sources_in_chat_min_version: Some("".to_string()),
                vscode_share_min_version: Some("".to_string()),
                intellij_share_min_version: Some("".to_string()),
                max_trackable_file_count: Some(250000),
                max_trackable_file_count_without_permission: Some(150000),
                min_uploaded_percentage_without_permission: Some(90),
                vscode_sources_min_version: Some("".to_string()),
                vscode_chat_hint_decoration_min_version: Some("".to_string()),
                next_edit_debounce_ms: Some(500),
                enable_completion_file_edit_events: Some(false),
                vscode_enable_cpu_profile: Some(false),
                verify_folder_is_source_repo: Some(false),
                refuse_to_sync_home_directories: Some(false),
                enable_file_limits_for_syncing_permission: Some(false),
                enable_chat_mermaid_diagrams: Some(false),
                enable_summary_titles: Some(false),
                smart_paste_precompute_mode: Some("visible-hover".to_string()),
                vscode_new_threads_menu_min_version: Some("".to_string()),
                intellij_new_threads_menu_min_version: Some("".to_string()),
                intellij_show_summary: Some(false),
                vscode_editable_history_min_version: Some("".to_string()),
                enable_guidelines: Some(false),
                vscode_use_checkpoint_manager_context_min_version: Some("".to_string()),
                vscode_validate_checkpoint_manager_context: Some(false),
                vscode_enable_chat_mermaid_diagrams_min_version: Some("".to_string()),
                vscode_deprecated_version: Some("".to_string()),
                intellij_deprecated_version: Some("".to_string()),
                intellij_completions_history_min_version: Some("".to_string()),
                intellij_smart_paste_min_version: Some("".to_string()),
                vscode_design_system_rich_text_editor_min_version: Some("".to_string()),
                allow_client_feature_flag_overrides: Some(false),
                vscode_chat_with_tools_min_version: Some("".to_string()),
                intellij_chat_with_tools_min_version: Some("".to_string()),
                vscode_chat_multimodal_min_version: Some("".to_string()),
                intellij_chat_multimodal_min_version: Some("".to_string()),
                intellij_enable_chat_mermaid_diagrams_min_version: Some("".to_string()),
                vscode_agent_mode_min_version: Some("".to_string()),
                vscode_agent_mode_min_stable_version: Some("".to_string()),
                vscode_agent_edit_tool: Some("backend_edit_tool".to_string()),
                intellij_agent_mode_min_version: Some("".to_string()),
                intellij_blocked_versions: Some("".to_string()),
                intellij_design_system_rich_text_editor_min_version: Some("".to_string()),
                intellij_syncing_progress_min_version: Some("".to_string()),
                memories_params: Some("{}".to_string()),
                vscode_background_agents_min_version: Some("".to_string()),
                intellij_background_agents_min_version: Some("".to_string()),
                elo_model_configuration: Some("{}".to_string()),
                intellij_ask_for_sync_permission_min_version: Some("".to_string()),
                workspace_guidelines_length_limit: Some(2000),
                user_guidelines_length_limit: Some(2000),
                vscode_rich_checkpoint_info_min_version: Some("".to_string()),
                intellij_remember_tool_min_version: Some("".to_string()),
                intellij_enable_user_guidelines: Some(false),
                intellij_user_guidelines_in_settings: Some(false),
                intellij_enable_workspace_guidelines: Some(false),
                vscode_virtualized_message_list_min_version: Some("".to_string()),
                intellij_virtualized_message_list_min_version: Some("".to_string()),
                vscode_chat_stable_prefix_truncation_min_version: Some("".to_string()),
                agent_edit_tool_min_view_size: Some(0),
                agent_edit_tool_schema_type: Some(
                    "StrReplaceEditorToolDefinitionNested".to_string(),
                ),
                agent_edit_tool_enable_fuzzy_matching: Some(true),
                agent_edit_tool_fuzzy_match_success_message: Some(
                    "Replacement successful. old_str and new_str were slightly modified to match the original file content.".to_string(),
                ),
                agent_edit_tool_fuzzy_match_max_diff: Some(50),
                agent_edit_tool_fuzzy_match_max_diff_ratio: Some(0.15),
                agent_edit_tool_fuzzy_match_min_all_match_streak_between_diffs: Some(5),
                agent_edit_tool_instructions_reminder: Some(false),
                agent_edit_tool_max_lines: Some(200),
                vscode_personalities_min_version: Some("".to_string()),
                agent_edit_tool_show_result_snippet: Some(true),
                memory_classification_on_first_token: Some(false),
                agent_save_file_tool_instructions_reminder: Some(false),
                remote_agent_chat_history_polling_interval_ms: Some(1000),
                remote_agent_list_polling_interval_ms: Some(5000),
                use_memory_snapshot_manager: Some(false),
                intellij_enable_homespun_gitignore: Some(false),
                intellij_preference_collection_allowed_min_version: Some("".to_string()),
                vscode_generate_commit_message_min_version: Some("".to_string()),
                enable_rules: Some(false),
                memories_text_editor_enabled: Some(false),
                enable_prompt_enhancer: Some(false),
                enable_model_registry: Some(false),
                model_registry: Some("{}".to_string()),
                vscode_task_list_min_version: Some("".to_string()),
                intellij_prompt_enhancer_enabled: Some(false),
                intellij_enable_sentry: Some(true),
                intellij_webview_error_sampling_rate: Some(0.0),
                intellij_plugin_error_sampling_rate: Some(0.0),
                beachhead_enable_sentry: Some(false),
                beachhead_error_sampling_rate: Some(0.0),
                beachhead_trace_sampling_rate: Some(0.0),
                intellij_webview_trace_sampling_rate: Some(0.0),
                intellij_plugin_trace_sampling_rate: Some(0.0),
                open_file_manager_v2_enabled: Some(false),
                intellij_enable_webview_performance_monitoring: Some(false),
                intellij_edt_freeze_detection_enabled: Some(false),
                enable_agent_auto_mode: Some(false),
                vscode_remote_agent_ssh_min_version: Some("".to_string()),
                client_announcement: Some("".to_string()),
                grep_search_tool_enable: Some(false),
                grep_search_tool_timelimit_sec: Some(10),
                grep_search_tool_output_chars_limit: Some(5000),
                grep_search_tool_num_context_lines: Some(5),
                agent_report_streamed_chat_every_chunk: Some(3),
                agent_max_total_changed_files_size_bytes: Some(2 * 1024 * 1024),
                agent_max_changed_files_skipped_paths: Some(1000),
                agent_idle_status_update_interval_ms: Some(60 * 1000),
                agent_max_iterations: Some(100),
                agent_ssh_connection_check_interval_ms: Some(5000),
                agent_ssh_connection_check_log_interval_ms: Some(5 * 60 * 1000),
            }),
        };
        assert_eq!(response, expected);
    }

    #[actix_web::test]
    async fn test_get_models_internal_names() {
        let response: public_api_proto::GetModelsResponse = run_get_models(true).await;
        let expected_default_model =
            Some("22ff0efe270b305371c97346e0619c987cfd10a5bb7c3acfcd7c92790a9ca91c".to_string());
        let expected_models = vec![public_api_proto::Model {
            name: "22ff0efe270b305371c97346e0619c987cfd10a5bb7c3acfcd7c92790a9ca91c".to_string(),
            internal_name: Some("model1".to_string()),
            suggested_prefix_char_count: DEFAULT_PREFIX_CHAR_COUNT,
            suggested_suffix_char_count: DEFAULT_SUFFIX_CHAR_COUNT,
            max_memorize_size_bytes: 131072,
            is_default: false,
        }];
        assert_eq!(response.default_model, expected_default_model);
        assert_eq!(response.models, expected_models);
    }

    fn verify_completion_data(received: &CompletionData, expected: &CompletionResolution) {
        assert_eq!(received.emit_time.seconds, expected.emit_time_sec);
        assert_eq!(received.emit_time.nanos, expected.emit_time_nsec);
        assert_eq!(received.resolve_time.seconds, expected.resolve_time_sec);
        assert_eq!(received.resolve_time.nanos, expected.resolve_time_nsec);
        assert_eq!(received.accepted_idx, expected.accepted_idx);
    }

    fn verify_next_edit_resolution_data(
        received: &NextEditResolutionData,
        expected: &NextEditResolution,
    ) {
        assert_eq!(received.emit_time.seconds, expected.emit_time_sec);
        assert_eq!(received.emit_time.nanos, expected.emit_time_nsec);
        assert_eq!(received.resolve_time.seconds, expected.resolve_time_sec);
        assert_eq!(received.resolve_time.nanos, expected.resolve_time_nsec);
        assert_eq!(received.is_accepted, expected.is_accepted);
    }

    fn verify_edit_resolution_data(received: &EditResolutionData, expected: &EditResolution) {
        assert_eq!(received.emit_time.seconds, expected.emit_time_sec);
        assert_eq!(received.emit_time.nanos, expected.emit_time_nsec);
        assert_eq!(received.resolve_time.seconds, expected.resolve_time_sec);
        assert_eq!(received.resolve_time.nanos, expected.resolve_time_nsec);
        assert_eq!(received.is_accepted, expected.is_accepted);
        assert_eq!(received.annotated_text, expected.annotated_text);
        assert_eq!(
            received.annotated_instruction,
            expected.annotated_instruction
        );
    }

    fn verify_preference_data(received: &PreferenceData, expected: &PreferenceSample) {
        assert_eq!(received.request_ids.len(), expected.request_ids.len());
        for (i, request_id) in received.request_ids.iter().enumerate() {
            assert_eq!(request_id.to_string(), expected.request_ids[i].to_string());
        }
        assert_eq!(received.scores, expected.scores);
        assert_eq!(received.feedback.to_string(), expected.feedback.to_string());
    }

    #[actix_web::test]
    async fn test_resolve_completions() {
        let fake_client_factory = Arc::new(FakeClientFactory::new());
        let registry = Arc::new(create_registry(fake_client_factory));
        let config = Config::default();
        let api_auth = new_api_auth();
        let request_insight_publisher = Arc::new(FakeRequestInsightPublisher::new());
        let content_manager_client = MockContentManagerClient::new();
        let external_source_clients = ExternalSourceClients::new_for_test();
        let share_client = Arc::new(share_client::MockShareClient::new());
        let agents_client = Arc::new(agents_client::MockAgentsClient::new());
        let remote_agents_client = Arc::new(remote_agents_client::MockRemoteAgentsClient::new());
        let github_processor_client =
            Arc::new(github_processor_client::MockGithubProcessorClient::new());
        let app_state = web::Data::new(Handler::new(
            config,
            api_auth,
            registry,
            request_insight_publisher.clone(),
            content_manager_client,
            external_source_clients,
            feature_flags::setup_local(),
            share_client,
            agents_client,
            remote_agents_client,
            github_processor_client,
            None,
        ));
        let req = setup_req();
        let request_id0 = RequestId::create_random();
        let request_id1 = RequestId::create_random();
        let resolutions = vec![
            CompletionResolution {
                request_id: request_id0.to_string(),
                emit_time_sec: 100,
                emit_time_nsec: 101,
                resolve_time_sec: 102,
                resolve_time_nsec: 103,
                accepted_idx: 0,
            },
            CompletionResolution {
                request_id: request_id1.to_string(),
                emit_time_sec: 200,
                emit_time_nsec: 201,
                resolve_time_sec: 203,
                resolve_time_nsec: 204,
                accepted_idx: -1,
            },
        ];
        let root_span = new_root_span();
        let resp = handle_api_auth(
            app_state.clone(),
            req,
            actix_web::web::Json(ResolveCompletions {
                resolutions: resolutions.clone(),
                ..ResolveCompletions::default()
            }),
            root_span,
        )
        .await;
        assert_eq!(resp.status(), http::StatusCode::OK);

        match request_insight_publisher._get_completion_data(&request_id0) {
            Some(data) => {
                verify_completion_data(&data, &resolutions[0]);
            }
            None => {
                panic!();
            }
        }
        match request_insight_publisher._get_completion_data(&request_id1) {
            Some(data) => {
                verify_completion_data(&data, &resolutions[1]);
            }
            None => {
                panic!();
            }
        }
    }

    #[actix_web::test]
    async fn test_next_edit_resolution() {
        // Setup the app state
        let fake_client_factory = Arc::new(FakeClientFactory::new());
        let registry = Arc::new(create_registry(fake_client_factory));
        let config = Config::default();
        let api_auth = new_api_auth();
        let request_insight_publisher = Arc::new(FakeRequestInsightPublisher::new());
        let content_manager_client = MockContentManagerClient::new();
        let external_source_clients = ExternalSourceClients::new_for_test();
        let share_client = Arc::new(share_client::MockShareClient::new());
        let agents_client = Arc::new(agents_client::MockAgentsClient::new());
        let remote_agents_client = Arc::new(remote_agents_client::MockRemoteAgentsClient::new());
        let github_processor_client =
            Arc::new(github_processor_client::MockGithubProcessorClient::new());
        let app_state = web::Data::new(Handler::new(
            config,
            api_auth,
            registry,
            request_insight_publisher.clone(),
            content_manager_client,
            external_source_clients,
            feature_flags::setup_local(),
            share_client,
            agents_client,
            remote_agents_client,
            github_processor_client,
            None,
        ));

        // Setup the request
        let req = setup_req();

        // Setup two resolutions to test the iteration
        let request_id0 = RequestId::create_random();
        let request_id1 = RequestId::create_random();

        // Create a batch with two resolutions
        let resolution_batch = NextEditResolutionBatch {
            resolutions: vec![
                NextEditResolution {
                    request_id: request_id0.to_string(),
                    emit_time_sec: 100,
                    emit_time_nsec: 101,
                    resolve_time_sec: 102,
                    resolve_time_nsec: 103,
                    is_accepted: true,
                },
                NextEditResolution {
                    request_id: request_id1.to_string(),
                    emit_time_sec: 100,
                    emit_time_nsec: 101,
                    resolve_time_sec: 102,
                    resolve_time_nsec: 103,
                    is_accepted: true,
                },
            ],
        };

        // Send the batch
        let resp = handle_api_auth(
            app_state.clone(),
            req,
            actix_web::web::Json(resolution_batch.clone()),
            new_root_span(),
        )
        .await;

        // Verify that the status code is OK
        assert_eq!(resp.status(), http::StatusCode::OK);

        // Check the values of the two resolutions
        match request_insight_publisher._get_next_edit_resolution_data(&request_id0) {
            Some(data) => {
                verify_next_edit_resolution_data(&data, &resolution_batch.resolutions[0]);
            }
            None => {
                panic!();
            }
        }

        match request_insight_publisher._get_next_edit_resolution_data(&request_id1) {
            Some(data) => {
                verify_next_edit_resolution_data(&data, &resolution_batch.resolutions[1]);
            }
            None => {
                panic!();
            }
        }
    }

    #[actix_web::test]
    async fn test_completion_feedback() {
        let fake_client_factory = Arc::new(FakeClientFactory::new());
        let registry = Arc::new(create_registry(fake_client_factory));
        let config = Config::default();
        let api_auth = new_api_auth();
        let request_insight_publisher = Arc::new(FakeRequestInsightPublisher::new());
        let content_manager_client = MockContentManagerClient::new();
        let external_source_clients = ExternalSourceClients::new_for_test();
        let share_client = Arc::new(share_client::MockShareClient::new());
        let agents_client = Arc::new(agents_client::MockAgentsClient::new());
        let remote_agents_client = Arc::new(remote_agents_client::MockRemoteAgentsClient::new());
        let github_processor_client =
            Arc::new(github_processor_client::MockGithubProcessorClient::new());
        let app_state = web::Data::new(Handler::new(
            config,
            api_auth,
            registry,
            request_insight_publisher.clone(),
            content_manager_client,
            external_source_clients,
            feature_flags::setup_local(),
            share_client,
            agents_client,
            remote_agents_client,
            github_processor_client,
            None,
        ));
        let req = setup_req();
        let request_id = RequestId::create_random();
        let feedback = CompletionFeedback {
            request_id: request_id.to_string(),
            rating: 1,
            note: "some note".to_string(),
        };
        let root_span = new_root_span();
        let resp = handle_api_auth(
            app_state.clone(),
            req,
            actix_web::web::Json(feedback.clone()),
            root_span,
        )
        .await;
        assert_eq!(resp.status(), http::StatusCode::OK);

        match request_insight_publisher._get_feedback(&request_id) {
            Some(data) => {
                assert_eq!(data.rating, feedback.rating);
                assert_eq!(data.note, feedback.note);
            }
            None => {
                panic!();
            }
        }
    }

    #[actix_web::test]
    async fn test_chat_feedback() {
        let fake_client_factory = Arc::new(FakeClientFactory::new());
        let registry = Arc::new(create_registry(fake_client_factory));
        let config = Config::default();
        let api_auth = new_api_auth();
        let request_insight_publisher = Arc::new(FakeRequestInsightPublisher::new());
        let content_manager_client = MockContentManagerClient::new();
        let external_source_clients = ExternalSourceClients::new_for_test();
        let share_client = Arc::new(share_client::MockShareClient::new());
        let agents_client = Arc::new(agents_client::MockAgentsClient::new());
        let remote_agents_client = Arc::new(remote_agents_client::MockRemoteAgentsClient::new());
        let github_processor_client =
            Arc::new(github_processor_client::MockGithubProcessorClient::new());
        let app_state = web::Data::new(Handler::new(
            config,
            api_auth,
            registry,
            request_insight_publisher.clone(),
            content_manager_client,
            external_source_clients,
            feature_flags::setup_local(),
            share_client,
            agents_client,
            remote_agents_client,
            github_processor_client,
            None,
        ));

        let req = setup_req();
        let request_id = RequestId::create_random();
        let feedback = ChatFeedback {
            request_id: request_id.to_string(),
            rating: 1,
            note: "some note".to_string(),
            mode: public_api_proto::ChatMode::Chat as i32,
        };
        let root_span = new_root_span();
        let resp = handle_api_auth(
            app_state.clone(),
            req,
            actix_web::web::Json(feedback.clone()),
            root_span,
        )
        .await;
        assert_eq!(resp.status(), http::StatusCode::OK);

        match request_insight_publisher._get_chat_feedback(&request_id) {
            Some(data) => {
                assert_eq!(data.rating, feedback.rating);
                assert_eq!(data.note, feedback.note);
            }
            None => {
                panic!();
            }
        }

        let req = setup_req();
        let request_id = RequestId::create_random();
        let feedback = ChatFeedback {
            request_id: request_id.to_string(),
            rating: 2,
            note: "some other note".to_string(),
            mode: public_api_proto::ChatMode::Agent as i32,
        };
        let root_span = new_root_span();
        let resp = handle_api_auth(
            app_state.clone(),
            req,
            actix_web::web::Json(feedback.clone()),
            root_span,
        )
        .await;
        assert_eq!(resp.status(), http::StatusCode::OK);

        match request_insight_publisher._get_agent_feedback(&request_id) {
            Some(data) => {
                assert_eq!(data.rating, feedback.rating);
                assert_eq!(data.note, feedback.note);
            }
            None => {
                panic!();
            }
        }
    }

    #[test]
    fn test_chat_feedback_deserialization() {
        // Mode is optional and defaults to CHAT
        let json_data = r#"{"request_id": "123", "rating": 2, "note": "could be better"}"#;
        let mut feedback: public_api_proto::ChatFeedback = serde_json::from_str(json_data).unwrap();
        assert_eq!(feedback.request_id, "123");
        assert_eq!(feedback.rating, 2);
        assert_eq!(feedback.note, "could be better");
        assert_eq!(feedback.mode, public_api_proto::ChatMode::Chat as i32);

        let json_data = r#"{"request_id": "456", "rating": 1, "note": "fixed tests without help", "mode": "AGENT"}"#;
        feedback = serde_json::from_str(json_data).unwrap();
        assert_eq!(feedback.request_id, "456");
        assert_eq!(feedback.rating, 1);
        assert_eq!(feedback.note, "fixed tests without help");
        assert_eq!(feedback.mode, public_api_proto::ChatMode::Agent as i32);
    }

    #[actix_web::test]
    async fn test_record_session_events() {
        let fake_client_factory = Arc::new(FakeClientFactory::new());
        let registry = Arc::new(create_registry(fake_client_factory));
        let config = Config::default();
        let api_auth = new_api_auth();
        let request_insight_publisher = Arc::new(FakeRequestInsightPublisher::new());
        let content_manager_client = MockContentManagerClient::new();
        let external_source_clients = ExternalSourceClients::new_for_test();
        let share_client = Arc::new(share_client::MockShareClient::new());
        let agents_client = Arc::new(agents_client::MockAgentsClient::new());
        let remote_agents_client = Arc::new(remote_agents_client::MockRemoteAgentsClient::new());
        let github_processor_client =
            Arc::new(github_processor_client::MockGithubProcessorClient::new());
        let app_state = web::Data::new(Handler::new(
            config,
            api_auth,
            registry,
            request_insight_publisher.clone(),
            content_manager_client,
            external_source_clients,
            feature_flags::setup_local(),
            share_client,
            agents_client,
            remote_agents_client,
            github_processor_client,
            None,
        ));
        let req = setup_req();
        let request_context = req.extensions().get::<RequestContext>().unwrap().clone();

        // Test a request with event ids.
        let request = public_api_proto::RecordSessionEventsRequest {
            events: vec![request_insight::SessionEvent {
                event_id: Some("test-event-id".to_string()),
                time: Some(prost_wkt_types::Timestamp {
                    seconds: 100,
                    nanos: 200,
                }),
                event: Some(
                    request_insight::session_event::Event::OnboardingSessionEvent(
                        request_insight::OnboardingSessionEvent {
                            event_name: "test-event-name".to_string(),
                            user_agent: "test-user-agent".to_string(),
                        },
                    ),
                ),
            }],
        };
        let root_span = new_root_span();
        let resp = handle_api_auth(
            app_state.clone(),
            req.clone(),
            actix_web::web::Json(request.clone()),
            root_span,
        )
        .await;
        assert_eq!(resp.status(), http::StatusCode::OK);
        match request_insight_publisher._get_session_events(&request_context.request_session_id()) {
            Some(data) => {
                assert_eq!(data.events, request.events);
            }
            None => {
                panic!();
            }
        }

        // Test a request without event ids.
        let request = public_api_proto::RecordSessionEventsRequest {
            events: vec![request_insight::SessionEvent {
                event_id: None,
                time: Some(prost_wkt_types::Timestamp {
                    seconds: 100,
                    nanos: 200,
                }),
                event: Some(
                    request_insight::session_event::Event::OnboardingSessionEvent(
                        request_insight::OnboardingSessionEvent {
                            event_name: "test-event-name".to_string(),
                            user_agent: "test-user-agent".to_string(),
                        },
                    ),
                ),
            }],
        };

        let root_span = new_root_span();
        let resp = handle_api_auth(
            app_state.clone(),
            req.clone(),
            actix_web::web::Json(request.clone()),
            root_span,
        )
        .await;
        assert_eq!(resp.status(), http::StatusCode::OK);
        match request_insight_publisher._get_session_events(&request_context.request_session_id()) {
            Some(data) => {
                assert!(data.events[0].event_id.is_some());
            }
            None => {
                panic!();
            }
        }

        // Test that the request fails if an event is missing a timestamp.
        let request = public_api_proto::RecordSessionEventsRequest {
            events: vec![request_insight::SessionEvent {
                event_id: None,
                time: None,
                event: Some(
                    request_insight::session_event::Event::OnboardingSessionEvent(
                        request_insight::OnboardingSessionEvent {
                            event_name: "test-event-name".to_string(),
                            user_agent: "test-user-agent".to_string(),
                        },
                    ),
                ),
            }],
        };
        let root_span = new_root_span();
        let resp = handle_api_auth(
            app_state.clone(),
            req.clone(),
            actix_web::web::Json(request.clone()),
            root_span,
        )
        .await;
        assert_eq!(resp.status(), http::StatusCode::BAD_REQUEST);
    }

    #[actix_web::test]
    async fn test_record_request_events() {
        let fake_client_factory = Arc::new(FakeClientFactory::new());
        let registry = Arc::new(create_registry(fake_client_factory));
        let config = Config::default();
        let api_auth = new_api_auth();
        let request_insight_publisher = Arc::new(FakeRequestInsightPublisher::new());
        let content_manager_client = MockContentManagerClient::new();
        let external_source_clients = ExternalSourceClients::new_for_test();
        let share_client = Arc::new(share_client::MockShareClient::new());
        let agents_client = Arc::new(agents_client::MockAgentsClient::new());
        let remote_agents_client = Arc::new(remote_agents_client::MockRemoteAgentsClient::new());
        let github_processor_client =
            Arc::new(github_processor_client::MockGithubProcessorClient::new());
        let app_state = web::Data::new(Handler::new(
            config,
            api_auth,
            registry,
            request_insight_publisher.clone(),
            content_manager_client,
            external_source_clients,
            feature_flags::setup_local(),
            share_client,
            agents_client,
            remote_agents_client,
            github_processor_client,
            None,
        ));
        let req = setup_req();
        let request_context = req.extensions().get::<RequestContext>().unwrap().clone();
        let feedback_event = Some(request_insight::request_event::Event::CompletionFeedback(
            request_insight::CompletionFeedback {
                rating: request_insight::FeedbackRating::Positive.into(),
                note: "test-note".to_string(),
            },
        ));

        // Test a request with event ids.
        let request = public_api_proto::RecordRequestEventsRequest {
            events: vec![request_insight::RequestEvent {
                event_id: Some("test-event-id".to_string()),
                time: Some(prost_wkt_types::Timestamp {
                    seconds: 100,
                    nanos: 200,
                }),
                event: feedback_event.clone(),
            }],
        };
        let root_span = new_root_span();
        let resp = handle_api_auth(
            app_state.clone(),
            req.clone(),
            actix_web::web::Json(request.clone()),
            root_span,
        )
        .await;
        assert_eq!(resp.status(), http::StatusCode::OK);
        match request_insight_publisher._get_request_events(&request_context.request_id()) {
            Some(data) => {
                assert_eq!(data.events, request.events);
            }
            None => {
                panic!();
            }
        }

        // Test a request without event ids.
        let request = public_api_proto::RecordRequestEventsRequest {
            events: vec![request_insight::RequestEvent {
                event_id: None,
                time: Some(prost_wkt_types::Timestamp {
                    seconds: 100,
                    nanos: 200,
                }),
                event: feedback_event.clone(),
            }],
        };

        let root_span = new_root_span();
        let resp = handle_api_auth(
            app_state.clone(),
            req.clone(),
            actix_web::web::Json(request.clone()),
            root_span,
        )
        .await;
        assert_eq!(resp.status(), http::StatusCode::OK);
        match request_insight_publisher._get_request_events(&request_context.request_id()) {
            Some(data) => {
                assert!(data.events[0].event_id.is_some());
            }
            None => {
                panic!();
            }
        }

        // Test that the request fails if an event is missing a timestamp.
        let request = public_api_proto::RecordRequestEventsRequest {
            events: vec![request_insight::RequestEvent {
                event_id: None,
                time: None,
                event: feedback_event.clone(),
            }],
        };
        let root_span = new_root_span();
        let resp = handle_api_auth(
            app_state.clone(),
            req.clone(),
            actix_web::web::Json(request.clone()),
            root_span,
        )
        .await;
        assert_eq!(resp.status(), http::StatusCode::BAD_REQUEST);
    }

    #[actix_web::test]
    async fn test_record_user_events() {
        let fake_client_factory = Arc::new(FakeClientFactory::new());
        let registry = Arc::new(create_registry(fake_client_factory));
        let config = Config::default();
        let api_auth = new_api_auth();
        let request_insight_publisher = Arc::new(FakeRequestInsightPublisher::new());
        let content_manager_client = MockContentManagerClient::new();
        let external_source_clients = ExternalSourceClients::new_for_test();
        let share_client = Arc::new(share_client::MockShareClient::new());
        let agents_client = Arc::new(agents_client::MockAgentsClient::new());
        let remote_agents_client = Arc::new(remote_agents_client::MockRemoteAgentsClient::new());
        let github_processor_client =
            Arc::new(github_processor_client::MockGithubProcessorClient::new());
        let app_state = web::Data::new(Handler::new(
            config,
            api_auth,
            registry,
            request_insight_publisher.clone(),
            content_manager_client,
            external_source_clients,
            feature_flags::setup_local(),
            share_client,
            agents_client,
            remote_agents_client,
            github_processor_client,
            None,
        ));
        let req = setup_req();
        let request_context = req.extensions().get::<RequestContext>().unwrap().clone();

        let request = request_insight::RecordFullExportUserEventsRequest {
            extension_data: Some(request_insight::ExtensionData {
                user_events: vec![request_insight::FullExportUserEvent {
                    time: Some(prost_wkt_types::Timestamp {
                        seconds: 100,
                        nanos: 200,
                    }),
                    file_path: Some("/test_path".to_string()),
                    event: Some(
                        request_insight::full_export_user_event::Event::CompletionRequestIdIssued(
                            request_insight::CompletionRequestIdIssuedEvent {
                                request_id: RequestId::create_random().to_string(),
                                file_path: None,
                            },
                        ),
                    ),
                }],
            }),
            ..Default::default()
        };
        let root_span = new_root_span();
        let resp = handle_api_auth(
            app_state.clone(),
            req.clone(),
            actix_web::web::Json(request.clone()),
            root_span,
        )
        .await;
        assert_eq!(resp.status(), http::StatusCode::OK);

        match request_insight_publisher._get_user_events(&request_context.request_session_id()) {
            Some(data) => {
                assert_eq!(data.extension_data, request.extension_data);
                assert!(data.user_id.is_some());
                assert!(data.session_id.is_some());
            }
            None => {
                panic!();
            }
        }
    }

    #[actix_web::test]
    async fn test_record_user_events_bad_request() {
        let fake_client_factory = Arc::new(FakeClientFactory::new());
        let registry = Arc::new(create_registry(fake_client_factory));
        let config = Config::default();
        let api_auth = new_api_auth();
        let request_insight_publisher = Arc::new(FakeRequestInsightPublisher::new());
        let content_manager_client = MockContentManagerClient::new();
        let external_source_clients = ExternalSourceClients::new_for_test();
        let share_client = Arc::new(share_client::MockShareClient::new());
        let agents_client = Arc::new(agents_client::MockAgentsClient::new());
        let remote_agents_client = Arc::new(remote_agents_client::MockRemoteAgentsClient::new());
        let github_processor_client =
            Arc::new(github_processor_client::MockGithubProcessorClient::new());
        let app_state = web::Data::new(Handler::new(
            config,
            api_auth,
            registry,
            request_insight_publisher.clone(),
            content_manager_client,
            external_source_clients,
            feature_flags::setup_local(),
            share_client,
            agents_client,
            remote_agents_client,
            github_processor_client,
            None,
        ));
        let req = setup_req();

        // Test that the request fails if the client sets a user_id.
        let req_with_user_id = request_insight::RecordFullExportUserEventsRequest {
            extension_data: Some(request_insight::ExtensionData {
                user_events: vec![],
            }),
            user_id: Some("user".to_string()),
            ..Default::default()
        };
        let root_span = new_root_span();
        let resp = handle_api_auth(
            app_state.clone(),
            req.clone(),
            actix_web::web::Json(req_with_user_id.clone()),
            root_span,
        )
        .await;
        assert_eq!(resp.status(), http::StatusCode::BAD_REQUEST);

        // Test that the request fails if the client sets a session_id.
        let req_with_user_id = request_insight::RecordFullExportUserEventsRequest {
            extension_data: Some(request_insight::ExtensionData {
                user_events: vec![],
            }),
            session_id: Some("session".to_string()),
            ..Default::default()
        };
        let root_span = new_root_span();
        let resp = handle_api_auth(
            app_state.clone(),
            req.clone(),
            actix_web::web::Json(req_with_user_id.clone()),
            root_span,
        )
        .await;
        assert_eq!(resp.status(), http::StatusCode::BAD_REQUEST);
    }

    #[actix_web::test]
    async fn test_checkpoint_blobs_blocklisted() {
        let fake_client_factory = Arc::new(FakeClientFactory::new());
        let registry = Arc::new(create_registry(fake_client_factory));
        let config = Config::default();
        let api_auth = new_api_auth();
        let request_insight_publisher = Arc::new(FakeRequestInsightPublisher::new());
        let content_manager_client = MockContentManagerClient::new();
        let feature_flags_handle = feature_flags::setup_local();
        let external_source_clients = ExternalSourceClients::new_for_test();
        let share_client = Arc::new(share_client::MockShareClient::new());
        let agents_client = Arc::new(agents_client::MockAgentsClient::new());
        let remote_agents_client = Arc::new(remote_agents_client::MockRemoteAgentsClient::new());
        let github_processor_client =
            Arc::new(github_processor_client::MockGithubProcessorClient::new());
        let app_state = web::Data::new(Handler::new(
            config,
            api_auth,
            registry,
            request_insight_publisher,
            content_manager_client,
            external_source_clients,
            feature_flags_handle.clone(),
            share_client,
            agents_client,
            remote_agents_client,
            github_processor_client,
            None,
        ));
        let req = setup_req();

        BLOCKLISTED_CHECKPOINT_IDS_FLAG.set_local(&feature_flags_handle, "blocked_checkpoint");

        let c = CheckpointBlobsRequest {
            blobs: Some(public_api_proto::Blobs {
                checkpoint_id: Some("blocked_checkpoint".to_string()),
                added_blobs: vec![],
                deleted_blobs: vec![],
            }),
        };
        let root_span = new_root_span();
        let resp = handle_api_auth(app_state.clone(), req, web::Json(c), root_span).await;
        assert_eq!(resp.status(), http::StatusCode::NOT_FOUND);
    }

    #[actix_web::test]
    async fn test_checkpoint_blobs() {
        let fake_client_factory = Arc::new(FakeClientFactory::new());
        let registry = Arc::new(create_registry(fake_client_factory));
        let config = Config::default();
        let api_auth = new_api_auth();
        let request_insight_publisher = Arc::new(FakeRequestInsightPublisher::new());
        let mut content_manager_client = MockContentManagerClient::new();
        content_manager_client
            .expect_checkpoint_blobs()
            .returning(|_, _, _| Ok(("fake_checkpoint_id").to_string()));
        let feature_flags_handle = feature_flags::setup_local();
        let external_source_clients = ExternalSourceClients::new_for_test();
        let share_client = Arc::new(share_client::MockShareClient::new());
        let agents_client = Arc::new(agents_client::MockAgentsClient::new());
        let remote_agents_client = Arc::new(remote_agents_client::MockRemoteAgentsClient::new());
        let github_processor_client =
            Arc::new(github_processor_client::MockGithubProcessorClient::new());
        let app_state = web::Data::new(Handler::new(
            config,
            api_auth,
            registry,
            request_insight_publisher,
            content_manager_client,
            external_source_clients,
            feature_flags_handle.clone(),
            share_client,
            agents_client,
            remote_agents_client,
            github_processor_client,
            None,
        ));
        let req = setup_req();

        let c = CheckpointBlobsRequest {
            blobs: Some(public_api_proto::Blobs {
                checkpoint_id: None,
                added_blobs: vec![],
                deleted_blobs: vec![],
            }),
        };
        let root_span = new_root_span();
        let resp = handle_api_auth(app_state.clone(), req, web::Json(c), root_span).await;
        assert_eq!(resp.status(), http::StatusCode::OK);
    }

    #[test]
    fn test_recency_info_without_optional_fields() {
        let json_data = "{}";
        let recency_info: RecencyInfo = serde_json::from_str(json_data).unwrap();
        assert_eq!(recency_info, RecencyInfo::default());

        let json_data =
            "{\"tab_switch_events\": [{\"path\": \"path1\", \"file_blob_name\": \"blob1\"}]}";
        let recency_info: RecencyInfo = serde_json::from_str(json_data).unwrap();
        assert_eq!(
            recency_info,
            RecencyInfo {
                tab_switch_events: vec![TabSwitchEvent {
                    path: "path1".to_string(),
                    file_blob_name: "blob1".to_string(),
                }],
                git_diff_file_info: vec![],
                recent_changes: vec![],
            }
        );
    }

    #[test]
    fn test_completion_response_backwards_compatibility() {
        #[allow(deprecated)]
        let completion_response = public_api_proto::CompletionResponse {
            text: "text".to_string(),
            unknown_memory_names: vec!["unknown_memory_name".to_string()],
            completion_items: vec![],
            suggested_prefix_char_count: 1,
            suggested_suffix_char_count: 2,
            completion_timeout_ms: None,
            unknown_checkpoint_id: None,
            checkpoint_not_found: false,
        };
        let json_data = serde_json::to_string(&completion_response).unwrap();
        assert_eq!(json_data, "{\"text\":\"text\",\"unknown_memory_names\":[\"unknown_memory_name\"],\"suggested_prefix_char_count\":1,\"suggested_suffix_char_count\":2,\"completion_items\":[],\"checkpoint_not_found\":false}");
    }

    #[test]
    fn test_completion_request_backwards_compatibility() {
        let json_data = "{\"prompt\": \"hello \"}";
        let completion_request: public_api_proto::CompletionRequest =
            serde_json::from_str(json_data).unwrap();
        assert_eq!(
            completion_request,
            public_api_proto::CompletionRequest {
                prompt: "hello ".to_string(),
                ..Default::default()
            }
        );
    }

    #[test]
    fn test_edit_request_backwards_compatibility() {
        let json_data = "{\"prefix\": \"hello \", \"instruction\": \"fix this\"}";
        let edit_request: public_api_proto::EditRequest = serde_json::from_str(json_data).unwrap();
        assert_eq!(
            edit_request,
            public_api_proto::EditRequest {
                prefix: "hello ".to_string(),
                instruction: "fix this".to_string(),
                ..Default::default()
            }
        );
    }

    #[actix_web::test]
    async fn test_report_error() {
        let fake_client_factory = Arc::new(FakeClientFactory::new());
        let registry = Arc::new(create_registry(fake_client_factory));
        let config = Config::default();
        let api_auth = new_api_auth();
        let request_insight_publisher = Arc::new(FakeRequestInsightPublisher::new());
        let content_manager_client = MockContentManagerClient::new();
        let external_source_clients = ExternalSourceClients::new_for_test();
        let share_client = Arc::new(share_client::MockShareClient::new());
        let agents_client = Arc::new(agents_client::MockAgentsClient::new());
        let remote_agents_client = Arc::new(remote_agents_client::MockRemoteAgentsClient::new());
        let github_processor_client =
            Arc::new(github_processor_client::MockGithubProcessorClient::new());
        let app_state = web::Data::new(Handler::new(
            config,
            api_auth,
            registry,
            request_insight_publisher.clone(),
            content_manager_client,
            external_source_clients,
            feature_flags::setup_local(),
            share_client,
            agents_client,
            remote_agents_client,
            github_processor_client,
            None,
        ));
        let req = setup_req();
        let request_context = req.extensions().get::<RequestContext>().unwrap().clone();

        let r = ReportErrorRequest {
            original_request_id: Some("11111111-1111-1111-1111-111111111111".to_string()),
            sanitized_message: "error".to_string(),
            stack_trace: "".to_string(),
            diagnostics: vec![],
        };
        let root_span = new_root_span();
        let resp = handle_api_auth(app_state.clone(), req, web::Json(r), root_span).await;
        assert_eq!(resp.status(), http::StatusCode::OK);
        assert_eq!(
            request_insight_publisher._get_request_metadata(&request_context.request_id()),
            Some(request_insight::RequestMetadata {
                request_type: request_insight::RequestType::ExtensionError.into(),
                session_id: request_context.request_session_id().to_string(),
                user_id: "34567".to_string(),
                opaque_user_id: Some(auth_entities::UserId {
                    user_id: "34567".to_string(),
                    user_id_type: auth_entities::user_id::UserIdType::ApiToken.into(),
                }),
                user_email: None,
                user_agent: "".to_string(),
                ..Default::default()
            })
        );
    }

    #[test]
    fn test_hmac() {
        // Confirm that the sha in the code matches the sha generated by:
        // echo -n <EMAIL> | openssl sha256 -hex -mac HMAC -macopt hexkey:855e699d6e5d53bff46b624acbf3040c
        let fake_key = hex::decode("855e699d6e5d53bff46b624acbf3040c").unwrap();
        let mut mac = Hmac::<Sha256>::new_from_slice(&fake_key).unwrap();
        mac.update(b"<EMAIL>");
        let result = mac.finalize();
        let code = hex::encode(result.into_bytes());
        assert_eq!(
            code,
            "b1203241b47850c566c44cf8b1e65f998944a6d4edd8d96ea1706dd0bfa77abd"
        )
    }

    fn verify_client_completion_data(received: &ClientTimelineData, expected: &ClientTimelineData) {
        assert_eq!(
            received.initial_request_time.seconds,
            expected.initial_request_time.seconds
        );
        assert_eq!(
            received.initial_request_time.nanos,
            expected.initial_request_time.nanos
        );
        assert_eq!(
            received.api_start_time.seconds,
            expected.api_start_time.seconds
        );
        assert_eq!(received.api_start_time.nanos, expected.api_start_time.nanos);
        assert_eq!(received.api_end_time.seconds, expected.api_end_time.seconds);
        assert_eq!(received.api_end_time.nanos, expected.api_end_time.nanos);
        assert_eq!(received.emit_time.seconds, expected.emit_time.seconds);
        assert_eq!(received.emit_time.nanos, expected.emit_time.nanos);
    }

    #[actix_web::test]
    async fn test_client_completion_timelines() {
        let fake_client_factory = Arc::new(FakeClientFactory::new());
        let registry = Arc::new(create_registry(fake_client_factory));
        let config = Config::default();
        let api_auth = new_api_auth();
        let request_insight_publisher = Arc::new(FakeRequestInsightPublisher::new());
        let content_manager_client = MockContentManagerClient::new();
        let external_source_clients = ExternalSourceClients::new_for_test();
        let share_client = Arc::new(share_client::MockShareClient::new());
        let agents_client = Arc::new(agents_client::MockAgentsClient::new());
        let remote_agents_client = Arc::new(remote_agents_client::MockRemoteAgentsClient::new());
        let github_processor_client =
            Arc::new(github_processor_client::MockGithubProcessorClient::new());
        let app_state = web::Data::new(Handler::new(
            config,
            api_auth,
            registry,
            request_insight_publisher.clone(),
            content_manager_client,
            external_source_clients,
            feature_flags::setup_local(),
            share_client,
            agents_client,
            remote_agents_client,
            github_processor_client,
            None,
        ));
        let req = setup_req();
        let request_id = RequestId::create_random();
        let timeline_request = public_api_proto::ClientCompletionTimelineRequest {
            timelines: vec![public_api_proto::ClientCompletionTimeline {
                request_id: request_id.to_string(),
                initial_request_time_sec: 100,
                initial_request_time_nsec: 101,
                api_start_time_sec: 200,
                api_start_time_nsec: 201,
                api_end_time_sec: 300,
                api_end_time_nsec: 301,
                emit_time_sec: 400,
                emit_time_nsec: 401,
            }],
        };
        let root_span = new_root_span();
        let resp = handle_api_auth(
            app_state.clone(),
            req,
            web::Json(timeline_request),
            root_span,
        )
        .await;
        assert_eq!(resp.status(), http::StatusCode::OK);

        let expected = ClientTimelineData {
            initial_request_time: prost_wkt_types::Timestamp {
                seconds: 100,
                nanos: 101,
            },
            api_start_time: prost_wkt_types::Timestamp {
                seconds: 200,
                nanos: 201,
            },
            api_end_time: prost_wkt_types::Timestamp {
                seconds: 300,
                nanos: 301,
            },
            emit_time: prost_wkt_types::Timestamp {
                seconds: 400,
                nanos: 401,
            },
        };
        match request_insight_publisher._get_client_completion_timelines(&request_id) {
            Some(data) => {
                verify_client_completion_data(&data, &expected);
            }
            None => {
                panic!();
            }
        }
    }
    #[actix_web::test]
    async fn test_share_api() {
        // Validate that the save/get api to the share service is functional.
        let fake_client_factory = Arc::new(FakeClientFactory::new());
        let registry = Arc::new(create_registry(fake_client_factory));
        let config = Config {
            share_service_url: "https://test-url.com/share/".to_string(),
            ..Config::default()
        };
        let api_auth = new_api_auth();
        let request_insight_publisher = Arc::new(FakeRequestInsightPublisher::new());
        let content_manager_client = MockContentManagerClient::new();
        let feature_flags_handle = feature_flags::setup_local();
        let external_source_clients = ExternalSourceClients::new_for_test();
        let share_client = Arc::new(share_client::MockShareClient::new());
        let agents_client = Arc::new(agents_client::MockAgentsClient::new());
        let remote_agents_client = Arc::new(remote_agents_client::MockRemoteAgentsClient::new());
        let github_processor_client =
            Arc::new(github_processor_client::MockGithubProcessorClient::new());
        let app_state = web::Data::new(Handler::new(
            config,
            api_auth,
            registry,
            request_insight_publisher,
            content_manager_client,
            external_source_clients,
            feature_flags_handle.clone(),
            share_client,
            agents_client,
            remote_agents_client,
            github_processor_client,
            None,
        ));
        let req = setup_req();

        let c = SaveChatRequest {
            conversation_id: "test-conversation-id".to_string(),
            chat: vec![public_api_proto::Exchange {
                request_id: Some("test-request-id".to_string()),
                request_message: "test-message".to_string(),
                response_text: Some("test-response".to_string()),
                ..Default::default()
            }],
            title: "This is a test title".to_string(),
        };
        let root_span = new_root_span();
        let resp = handle_api_auth(
            app_state.clone(),
            req.clone(),
            web::Json(c),
            root_span.clone(),
        )
        .await;
        assert_eq!(resp.status(), http::StatusCode::OK);
        let response: public_api_proto::SaveChatResponse =
            serde_json::from_slice(&body::to_bytes(resp.into_body()).await.unwrap()).unwrap();
        assert_ne!(response.uuid, "");
    }
}
