package main

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"sync"
	"testing"
	"time"

	bigtableproto "cloud.google.com/go/bigtable/apiv2/bigtablepb"
	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/go/secretstring"
	bigtableproxy "github.com/augmentcode/augment/services/bigtable_proxy/client"
	proxyproto "github.com/augmentcode/augment/services/bigtable_proxy/proto"
	chatproto "github.com/augmentcode/augment/services/chat_host/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	memstoreclient "github.com/augmentcode/augment/services/memstore/client"
	remoteagentsproto "github.com/augmentcode/augment/services/remote_agents/proto"
	entitiesproto "github.com/augmentcode/augment/services/remote_agents/server/entities_proto"
	"github.com/augmentcode/augment/services/remote_agents/server/ws"
	agentsmock "github.com/augmentcode/augment/services/remote_agents/server/ws/mocks"
	wsmock "github.com/augmentcode/augment/services/remote_agents/server/ws/mocks"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/testing/protocmp"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// Use the global variables from test_setup.go

func TestStorageRemoteAgentReadWrite(t *testing.T) {
	// First, create an agent in BigTable
	testParameters := setupTestParameters()

	// Create the agent config in BigTable
	agentConfig := createTestAgentConfig()
	// UserId is set in writeRemoteAgentAtCreation

	// Write the agent config to BigTable
	err := writeRemoteAgentAtCreation(
		context.Background(),
		testParameters.RequestCtx,
		realBigtableProxyClient,
		testParameters.TenantID,
		testParameters.UserID,
		testParameters.AgentID,
		agentConfig,
		"test-namespace",
	)
	require.NoError(t, err, "Failed to write agent config to BigTable")

	// Now read the agent back
	agent, err := readRemoteAgent(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.UserID, testParameters.AgentID, 30)
	require.NoError(t, err, "Failed to read agent from BigTable")
	require.NotNil(t, agent, "Agent should not be nil")
	require.Equal(t, testParameters.AgentID, agent.RemoteAgentId, "Agent ID should match")
	require.Equal(t, remoteagentsproto.AgentStatus_AGENT_STATUS_STARTING, agent.Status, "Agent status should match")
	require.True(t, proto.Equal(agentConfig.WorkspaceSetup, agent.Config.WorkspaceSetup), "Agent config should match")
	require.True(t, proto.Equal(agentConfig.StartingNodes[0], agent.Config.StartingNodes[0]), "Agent starting nodes should match")
	require.Equal(t, agentConfig.UserGuidelines, agent.Config.UserGuidelines, "Agent user guidelines should match")
	require.Equal(t, agentConfig.WorkspaceGuidelines, agent.Config.WorkspaceGuidelines, "Agent workspace guidelines should match")
	require.Equal(t, agentConfig.AgentMemories, agent.Config.AgentMemories, "Agent memories should match")
	require.Equal(t, agentConfig.Model, agent.Config.Model, "Agent model should match")

	testParameters = setupTestParameters()
	err = writeRemoteAgentAtCreation(
		context.Background(),
		testParameters.RequestCtx,
		realBigtableProxyClient,
		testParameters.TenantID,
		testParameters.UserID,
		testParameters.AgentID,
		agentConfig,
		"test-namespace",
	)
	require.NoError(t, err, "Failed to write agent config to BigTable")
	agentEntity, err := readRemoteAgentEntity(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.AgentID)
	require.NoError(t, err, "Failed to read agent config from BigTable")
	require.NotNil(t, agentEntity, "Agent entity should not be nil")
	require.True(t, proto.Equal(agentConfig.WorkspaceSetup, agentEntity.Config.InputConfig.WorkspaceSetup), "Agent config should match")
	require.True(t, proto.Equal(agentConfig.StartingNodes[0], agentEntity.Config.InputConfig.StartingNodes[0]), "Agent starting nodes should match")
	require.Equal(t, agentConfig.UserGuidelines, agentEntity.Config.InputConfig.UserGuidelines, "Agent user guidelines should match")
	require.Equal(t, agentConfig.WorkspaceGuidelines, agentEntity.Config.InputConfig.WorkspaceGuidelines, "Agent workspace guidelines should match")
	require.Equal(t, agentConfig.AgentMemories, agentEntity.Config.InputConfig.AgentMemories, "Agent memories should match")
	require.Equal(t, agentConfig.Model, agentEntity.Config.InputConfig.Model, "Agent model should match")
	require.Equal(t, testParameters.UserID, agentEntity.Config.UserId, "Agent user ID should match")
	require.Equal(t, "test-namespace", agentEntity.Config.Namespace, "Agent namespace should match")
}

func TestCreateAgent_HappyPath(t *testing.T) {
	tse := setupTestServerWithEmulator(t)

	testParameters := setupTestParameters()

	// Create request with config
	req := &remoteagentsproto.CreateAgentRequest{
		Config: createTestAgentConfig(),
	}

	// Create a mock agent with the expected values
	createdAt := time.Now()
	mockAgent := &remoteagentsproto.Agent{
		RemoteAgentId: "test-agent-id", // This will be replaced in the test
		Status:        remoteagentsproto.AgentStatus_AGENT_STATUS_STARTING,
		Config:        req.Config,
		CreatedAt:     timestamppb.New(createdAt),
	}

	// Create a mock workspace
	mockWorkspace := wsmock.NewMockRemoteWorkspace(tse.ctrl)

	// Expect ApplyWorkspace to be called with the correct parameters
	tse.mockRWC.EXPECT().
		ApplyWorkspace(gomock.Any(), testParameters.UserID, gomock.Any(), gomock.Any()).
		DoAndReturn(func(_ interface{}, _ string, argAgentID string, wsCfg *remoteagentsproto.WorkspaceAgentConfig) (ws.RemoteWorkspace, error) {
			// Verify the WorkspaceAgentConfig has the correct values
			require.Equal(t, argAgentID, wsCfg.RemoteAgentId, "WorkspaceAgentConfig should have the correct agent ID")
			require.True(t, proto.Equal(req.Config, wsCfg.Config), "WorkspaceAgentConfig should have the correct config")
			// Verify the agent ID is a valid UUID
			_, err := uuid.Parse(argAgentID)
			require.NoError(t, err, "Agent ID should be a valid UUID")

			// Update the mock agent with the actual agent ID
			mockAgent.RemoteAgentId = argAgentID

			return mockWorkspace, nil
		})

	mockWorkspace.EXPECT().
		Agent(gomock.Any()).
		Return(mockAgent, nil).
		Times(1)

	// Create a context with metadata
	ctx := requestcontext.NewIncomingContext(testParameters.Ctx, testParameters.RequestCtx)

	// Call the CreateAgent method
	resp, err := tse.server.CreateAgent(ctx, req)

	// Verify the response
	require.NoError(t, err)
	require.NotNil(t, resp)
	require.Equal(t, mockAgent, resp.Agent)

	// Verify that the agent was written to BigTable by reading it back
	configFromBigTable, err := readRemoteAgent(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.UserID, resp.Agent.RemoteAgentId, 30)

	// Verify that the cell contains the expected data
	require.NotNil(t, configFromBigTable.Config, "Agent config should not be nil")
	require.NotNil(t, configFromBigTable.Config.WorkspaceSetup, "Agent config workspace setup should not be nil")
	githubRef := configFromBigTable.Config.WorkspaceSetup.GetGithubRef()
	require.NotNil(t, githubRef, "Agent config github ref should not be nil")
	require.Equal(t, req.Config.WorkspaceSetup.GetGithubRef().Url, githubRef.Url, "Agent config URL should match")
	require.Equal(t, req.Config.WorkspaceSetup.GetGithubRef().Ref, githubRef.Ref, "Agent config Ref should match")

	userMappingFromBigTable, err := readUserAgentMapping(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.UserID)
	require.NoError(t, err, "Failed to read user agent mapping from BigTable")
	require.Contains(t, userMappingFromBigTable, resp.Agent.RemoteAgentId, "User agent mapping should contain the new agent ID")

	// Verify we don't put the token into RI
	require.Len(t, tse.riPublisher.RequestEventCalls, 2)
	riCreateReq := tse.riPublisher.RequestEventCalls[0].Event.GetRemoteAgentsCreateRequest().Request
	require.Equal(t, "https://github.com/augmentcode/augment", riCreateReq.Config.WorkspaceSetup.GetGithubRef().Url)
	require.Equal(t, "main", riCreateReq.Config.WorkspaceSetup.GetGithubRef().Ref)
	require.Nil(t, riCreateReq.Config.WorkspaceSetup.Token, "The token should be private and not posted to RI")
}

func TestListAgent(t *testing.T) {
	// First, create an agent in BigTable
	testParameters := setupTestParameters()
	agentConfig := createTestAgentConfig()
	err := writeRemoteAgentAtCreation(
		context.Background(),
		testParameters.RequestCtx,
		realBigtableProxyClient,
		testParameters.TenantID,
		testParameters.UserID,
		testParameters.AgentID,
		agentConfig,
		"test-namespace",
	)
	require.NoError(t, err, "Failed to write agent config to BigTable")

	tse := setupTestServerWithEmulator(t)

	ctx := requestcontext.NewIncomingContext(testParameters.Ctx, testParameters.RequestCtx)
	resp, err := tse.server.ListAgents(ctx, &remoteagentsproto.ListAgentsRequest{})
	require.NoError(t, err)
	require.NotNil(t, resp)
	require.Equal(t, 1, len(resp.Agents))
	require.Equal(t, testParameters.AgentID, resp.Agents[0].RemoteAgentId)
	created := resp.Agents[0].CreatedAt
	updated := resp.Agents[0].UpdatedAt

	// Now try updating the status
	_, err = tse.server.Chat(ctx, &remoteagentsproto.ChatRequest{
		RemoteAgentId: testParameters.AgentID,
		RequestDetails: &remoteagentsproto.ChatRequestDetails{
			RequestNodes: []*chatproto.ChatRequestNode{
				{
					Id:   1,
					Type: chatproto.ChatRequestNodeType_TEXT,
					TextNode: &chatproto.ChatRequestText{
						Content: "Hello, agent!",
					},
				},
			},
		},
	})
	require.NoError(t, err)

	resp, err = tse.server.ListAgents(ctx, &remoteagentsproto.ListAgentsRequest{})
	require.NoError(t, err)
	require.NotNil(t, resp)
	require.Equal(t, 1, len(resp.Agents))
	require.Equal(t, testParameters.AgentID, resp.Agents[0].RemoteAgentId)
	require.Equal(t, created, resp.Agents[0].CreatedAt)
	require.NotEqual(t, updated, resp.Agents[0].UpdatedAt)
}

func TestWorkspaceReportStatus_HappyPath(t *testing.T) {
	testParameters := setupTestParameters()

	agentConfig := createTestAgentConfig()
	// Write the agent config to BigTable
	err := writeRemoteAgentAtCreation(
		context.Background(),
		testParameters.RequestCtx,
		realBigtableProxyClient,
		testParameters.TenantID,
		testParameters.UserID,
		testParameters.AgentID,
		agentConfig,
		"test-namespace",
	)
	require.NoError(t, err, "Failed to write agent config to BigTable")

	// Now set up the server and test the status update
	tse := setupTestServerWithEmulator(t)

	// Create request with agent ID and status
	req := &remoteagentsproto.WorkspaceReportStatusRequest{
		RemoteAgentId: testParameters.AgentID,
		Status:        remoteagentsproto.AgentStatus_AGENT_STATUS_RUNNING,
	}

	// Create a context with metadata
	ctx := requestcontext.NewIncomingContext(testParameters.Ctx, testParameters.RequestCtx)

	// Call the WorkspaceReportStatus method
	resp, err := tse.server.WorkspaceReportStatus(ctx, req)

	// Verify the response
	require.NoError(t, err)
	require.NotNil(t, resp)

	// Verify that the status was updated in BigTable by reading the agent directly
	agent, err := readRemoteAgent(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.UserID, testParameters.AgentID, 30)
	fmt.Printf("Agent: %+v\n", agent)
	require.NoError(t, err, "Failed to read agent from BigTable")
	require.NotNil(t, agent, "Agent should not be nil")
	require.Equal(t, req.Status, agent.Status, "Agent status should match the expected status")
}

func TestWorkspaceReportChatHistory_HappyPath(t *testing.T) {
	// Mock the time function to make the test deterministic
	fixedTime := time.Date(2023, 12, 25, 10, 30, 0, 0, time.UTC)
	originalNowFunc := nowFunc
	nowFunc = func() time.Time { return fixedTime }
	defer func() { nowFunc = originalNowFunc }()

	// First, create an agent in BigTable
	testParameters := setupTestParameters()
	agentConfig := createTestAgentConfig()
	err := writeRemoteAgentAtCreation(
		context.Background(),
		testParameters.RequestCtx,
		realBigtableProxyClient,
		testParameters.TenantID,
		testParameters.UserID,
		testParameters.AgentID,
		agentConfig,
		"test-namespace",
	)
	require.NoError(t, err, "Failed to write agent config to BigTable")

	tse := setupTestServerWithEmulator(t)
	ctx := requestcontext.NewIncomingContext(testParameters.Ctx, testParameters.RequestCtx)

	// Create multiple exchanges with different sequence IDs
	// Note: We'll add them out of order to test sorting
	exchanges := make([]*remoteagentsproto.ChatHistoryExchange, 3)
	// Exchange 1 (sequence ID 2) - middle exchange
	responseText1 := "I can help with that!"
	requestId1 := "request-id-1"
	exchange1 := &chatproto.Exchange{
		RequestMessage: "Can you help me with this code?",
		ResponseText:   &responseText1,
		RequestId:      &requestId1,
	}
	changedFile1 := &remoteagentsproto.ChangedFile{
		OldPath:     "src/main.py",
		NewPath:     "src/main.py",
		OldContents: "def hello():\n    print('hello')",
		NewContents: "def hello():\n    print('hello world')",
		ChangeType:  remoteagentsproto.ChangedFile_MODIFIED,
	}
	exchanges[1] = &remoteagentsproto.ChatHistoryExchange{
		Exchange:     exchange1,
		ChangedFiles: []*remoteagentsproto.ChangedFile{changedFile1},
		SequenceId:   2, // Middle sequence ID
		FinishedAt:   timestamppb.Now(),
	}

	// Exchange 2 (sequence ID 3) - last exchange
	responseText2 := "Done! I've added the function."
	requestId2 := "request-id-2"
	exchange2 := &chatproto.Exchange{
		RequestMessage: "Add a goodbye function too",
		ResponseText:   &responseText2,
		RequestId:      &requestId2,
	}
	changedFile2 := &remoteagentsproto.ChangedFile{
		OldPath:     "src/main.py",
		NewPath:     "src/main.py",
		OldContents: "def hello():\n    print('hello world')",
		NewContents: "def hello():\n    print('hello world')\n\ndef goodbye():\n    print('goodbye')",
		ChangeType:  remoteagentsproto.ChangedFile_MODIFIED,
	}
	exchanges[2] = &remoteagentsproto.ChatHistoryExchange{
		Exchange:     exchange2,
		ChangedFiles: []*remoteagentsproto.ChangedFile{changedFile2},
		SequenceId:   3, // Highest sequence ID
		FinishedAt:   timestamppb.Now(),
	}

	// Exchange 0 (sequence ID 1) - first exchange
	responseText0 := "Hello! How can I help you today?"
	requestId0 := "request-id-0"
	exchange0 := &chatproto.Exchange{
		RequestMessage: "Hello, agent!",
		ResponseText:   &responseText0,
		RequestId:      &requestId0,
	}
	exchanges[0] = &remoteagentsproto.ChatHistoryExchange{
		Exchange:     exchange0,
		ChangedFiles: []*remoteagentsproto.ChangedFile{},
		SequenceId:   1, // Lowest sequence ID
		FinishedAt:   timestamppb.Now(),
	}

	// Report the exchanges in a deliberately mixed order to test sorting
	// First report exchanges 0 and 2
	req1 := &remoteagentsproto.WorkspaceReportChatHistoryRequest{
		RemoteAgentId: testParameters.AgentID,
		ChatHistory:   []*remoteagentsproto.ChatHistoryExchange{exchanges[0], exchanges[2]},
	}
	resp1, err := tse.server.WorkspaceReportChatHistory(ctx, req1)
	require.NoError(t, err)
	require.NotNil(t, resp1)

	// Then report exchange 1
	req2 := &remoteagentsproto.WorkspaceReportChatHistoryRequest{
		RemoteAgentId: testParameters.AgentID,
		ChatHistory:   []*remoteagentsproto.ChatHistoryExchange{exchanges[1]},
	}
	resp2, err := tse.server.WorkspaceReportChatHistory(ctx, req2)
	require.NoError(t, err)
	require.NotNil(t, resp2)

	// Now retrieve the chat history using the ChatHistory endpoint
	chatHistoryReq := &remoteagentsproto.ChatHistoryRequest{
		RemoteAgentId: testParameters.AgentID,
	}
	chatHistoryResp, err := tse.server.ChatHistory(ctx, chatHistoryReq)
	require.NoError(t, err, "Failed to get chat history")
	require.NotNil(t, chatHistoryResp, "Chat history response should not be nil")
	require.Len(t, chatHistoryResp.ChatHistory, 0, "Chat history should be empty while in starting")

	// Update the status to running, we don't check for chat history when it's starting
	agentEntity, err := readRemoteAgentEntity(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.AgentID)
	require.NoError(t, err, "Failed to read agent entity")
	agentEntity.Status.Status = remoteagentsproto.AgentStatus_AGENT_STATUS_RUNNING
	err = agentEntity.AtomicSave(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID)

	require.NoError(t, err, "Failed to write agent status to BigTable")
	chatHistoryResp, err = tse.server.ChatHistory(ctx, chatHistoryReq)
	require.NoError(t, err, "Failed to get chat history")
	require.NotNil(t, chatHistoryResp, "Chat history response should not be nil")

	// Verify that all exchanges are present and in the correct order by sequence ID
	require.Len(t, chatHistoryResp.ChatHistory, 3, "Chat history should contain exactly three exchanges")

	// Verify the order is correct (by sequence ID)
	require.Equal(t, uint32(1), chatHistoryResp.ChatHistory[0].SequenceId, "First exchange should have sequence ID 1")
	require.Equal(t, uint32(2), chatHistoryResp.ChatHistory[1].SequenceId, "Second exchange should have sequence ID 2")
	require.Equal(t, uint32(3), chatHistoryResp.ChatHistory[2].SequenceId, "Third exchange should have sequence ID 3")

	// Verify the content matches what we sent
	require.True(t, proto.Equal(exchange0, chatHistoryResp.ChatHistory[0].Exchange), "First exchange should match")
	require.True(t, proto.Equal(exchange1, chatHistoryResp.ChatHistory[1].Exchange), "Second exchange should match")
	require.True(t, proto.Equal(exchange2, chatHistoryResp.ChatHistory[2].Exchange), "Third exchange should match")

	// Also verify using the direct BigTable read function
	// Use pageSize=0 for backward compatibility (return all exchanges)
	directChatHistory, err := readChatHistory(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.AgentID, 0, 0)
	require.NoError(t, err, "Failed to read chat history directly from BigTable")
	require.NotNil(t, directChatHistory, "Direct chat history should not be nil")
	require.Len(t, directChatHistory, 3, "Direct chat history should contain exactly three exchanges")

	// Verify the direct read order is also correct
	require.Equal(t, uint32(1), directChatHistory[0].SequenceId, "First direct exchange should have sequence ID 1")
	require.Equal(t, uint32(2), directChatHistory[1].SequenceId, "Second direct exchange should have sequence ID 2")
	require.Equal(t, uint32(3), directChatHistory[2].SequenceId, "Third direct exchange should have sequence ID 3")

	// Verify that last_agent_update_received_at timestamp was set
	agentEntity, err = readRemoteAgentEntity(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.AgentID)
	require.NoError(t, err, "Failed to read agent entity")
	require.NotNil(t, agentEntity.Status.LastAgentUpdateReceivedAt, "last_agent_update_received_at should be set")
	require.Equal(t, fixedTime.Unix(), agentEntity.Status.LastAgentUpdateReceivedAt.Seconds, "last_agent_update_received_at should match the fixed time")
}

// TestAgentHistoryStream_ClearsHasUpdatesFlag tests that calling AgentHistoryStream sets has_updates to false
func TestAgentHistoryStream_ClearsHasUpdatesFlag(t *testing.T) {
	// First, create an agent in BigTable
	testParameters := setupTestParameters()
	agentConfig := createTestAgentConfig()
	err := writeRemoteAgentAtCreation(
		context.Background(),
		testParameters.RequestCtx,
		realBigtableProxyClient,
		testParameters.TenantID,
		testParameters.UserID,
		testParameters.AgentID,
		agentConfig,
		"test-namespace",
	)
	require.NoError(t, err, "Failed to write agent config to BigTable")

	// Set up the server
	tse := setupTestServerWithEmulator(t)

	// Create a context with metadata
	ctx := requestcontext.NewIncomingContext(testParameters.Ctx, testParameters.RequestCtx)

	// Step 1: Set has_updates to true by reporting status
	statusReq := &remoteagentsproto.WorkspaceReportStatusRequest{
		RemoteAgentId: testParameters.AgentID,
		Status:        remoteagentsproto.AgentStatus_AGENT_STATUS_RUNNING,
	}
	_, err = tse.server.WorkspaceReportStatus(ctx, statusReq)
	require.NoError(t, err, "Failed to report status")

	// Verify that has_updates is now true
	agent, err := readRemoteAgent(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.UserID, testParameters.AgentID, 30)
	require.NoError(t, err, "Failed to read agent from BigTable")
	hasUpdates := agent.HasUpdates != nil && *agent.HasUpdates
	require.True(t, hasUpdates, "has_updates should be true after reporting status")

	// Step 2: Call AgentHistoryStream to clear has_updates
	agentHistoryStreamReq := &remoteagentsproto.AgentHistoryStreamRequest{
		RemoteAgentId: testParameters.AgentID,
	}

	// Create a cancellable context for the stream
	streamCtx, cancel := context.WithCancel(requestcontext.NewIncomingContext(testParameters.Ctx, testParameters.RequestCtx))
	defer cancel()

	// Create a mock stream for the AgentHistoryStream call
	mockStream := &mockAgentHistoryStreamServer{
		ctx: streamCtx,
	}

	// Start AgentHistoryStream in a goroutine since it's a long-running streaming method
	streamErr := make(chan error, 1)
	go func() {
		err := tse.server.AgentHistoryStream(agentHistoryStreamReq, mockStream)
		streamErr <- err
	}()

	// Give the stream a moment to start and clear the has_updates flag
	time.Sleep(100 * time.Millisecond)

	// Step 3: Verify that has_updates is now false
	agent, err = readRemoteAgent(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.UserID, testParameters.AgentID, 30)
	require.NoError(t, err, "Failed to read agent from BigTable")
	hasUpdates = agent.HasUpdates != nil && *agent.HasUpdates
	require.False(t, hasUpdates, "has_updates should be false after starting agent history stream")

	// Cancel the stream
	cancel()

	// Wait for the stream to finish
	select {
	case err := <-streamErr:
		// The stream should end with a context cancellation error, which is expected
		require.True(t, errors.Is(err, context.Canceled), "Stream should end with context cancellation error")
	case <-time.After(1 * time.Second):
		t.Fatal("Stream did not finish within expected time")
	}
}

func TestSequenceIDFormatting(t *testing.T) {
	// Test that sequence IDs in row keys are properly formatted with 10-digit zero padding

	// Test getRemoteAgentChatHistoryRowKey
	testCases := []struct {
		agentID    string
		sequenceID uint32
		expected   string
	}{
		{"test-agent", 1, "ExchangeHistory#test-agent#00000000000000000001"},
		{"test-agent", 10, "ExchangeHistory#test-agent#00000000000000000010"},
		{"test-agent", 100, "ExchangeHistory#test-agent#00000000000000000100"},
		{"test-agent", 1000, "ExchangeHistory#test-agent#00000000000000001000"},
		{"test-agent", 1_000_000_000, "ExchangeHistory#test-agent#00000000001000000000"},
	}

	for _, tc := range testCases {
		result := getRemoteAgentChatHistoryRowKey(tc.agentID, tc.sequenceID)
		require.Equal(t, tc.expected, string(result), "Chat history row key format incorrect for sequence ID %d", tc.sequenceID)
	}
}

func TestWorkspaceReportSetupLogs_ChunksLongLogs(t *testing.T) {
	// First, create an agent in BigTable
	testParameters := setupTestParameters()
	agentConfig := createTestAgentConfig()
	err := writeRemoteAgentAtCreation(
		context.Background(),
		testParameters.RequestCtx,
		realBigtableProxyClient,
		testParameters.TenantID,
		testParameters.UserID,
		testParameters.AgentID,
		agentConfig,
		"test-namespace",
	)
	require.NoError(t, err, "Failed to write agent config to BigTable")

	// Create a server with the emulator
	tse := setupTestServerWithEmulator(t)

	// Set up mock expectations
	mockWorkspace := agentsmock.NewMockRemoteWorkspace(tse.ctrl)
	tse.mockRWC.EXPECT().
		GetWorkspace(gomock.Any(), testParameters.UserID, testParameters.AgentID).
		Return(mockWorkspace, nil).
		AnyTimes()

	// Mock PodStatus to return a running status
	mockWorkspace.EXPECT().
		PodStatus(gomock.Any()).
		Return("Running").
		AnyTimes()

	// Create a series of steps with logs exceeding 1000 chars
	log1 := strings.Repeat("A", 400_000)
	log2 := strings.Repeat("B", 200_000)
	log3 := strings.Repeat("C", 300_000)
	workspaceSetupStatus := &remoteagentsproto.WorkspaceSetupStatus{
		Steps: []*remoteagentsproto.WorkspaceSetupStep{
			{
				StepDescription: "Git Clone",
				Logs:            log1,
				Status:          remoteagentsproto.WorkspaceSetupStepStatus_WORKSPACE_SETUP_STEP_STATUS_RUNNING,
				SequenceId:      1,
				StepNumber:      0,
			},
			{
				StepDescription: "Git Clone",
				Logs:            log2,
				Status:          remoteagentsproto.WorkspaceSetupStepStatus_WORKSPACE_SETUP_STEP_STATUS_SUCCESS,
				SequenceId:      2,
				StepNumber:      0,
			},
			{
				StepDescription: "Git Clone",
				Logs:            log3,
				Status:          remoteagentsproto.WorkspaceSetupStepStatus_WORKSPACE_SETUP_STEP_STATUS_RUNNING,
				SequenceId:      3,
				StepNumber:      0,
			},
		},
	}

	// Report the logs
	ctx := requestcontext.NewIncomingContext(testParameters.Ctx, testParameters.RequestCtx)
	req := &remoteagentsproto.WorkspaceReportSetupLogsRequest{
		RemoteAgentId:        testParameters.AgentID,
		WorkspaceSetupStatus: workspaceSetupStatus,
	}
	resp1, err := tse.server.WorkspaceReportSetupLogs(ctx, req)
	require.NoError(t, err, "Failed to report workspace logs")
	require.NotNil(t, resp1, "Workspace report logs response should not be nil")

	// Now request the logs to verify they were stored correctly, step 1 because we want to ignore the container step
	lastProcessedStep := uint32(1)
	lastProcessedSequenceId := uint32(0)
	logsReq := &remoteagentsproto.WorkspaceLogsRequest{
		RemoteAgentId:           testParameters.AgentID,
		LastProcessedStep:       &lastProcessedStep,
		LastProcessedSequenceId: &lastProcessedSequenceId,
	}
	logsResp, err := tse.server.WorkspaceLogs(ctx, logsReq)
	require.NoError(t, err, "Failed to get workspace logs")
	require.NotNil(t, logsResp, "Workspace logs response should not be nil")
	require.Equal(t, testParameters.AgentID, logsResp.RemoteAgentId, "Agent ID should match")
	require.NotNil(t, logsResp.WorkspaceSetupStatus, "Workspace setup status should not be nil")

	// when called for the first time, it should return only the first step
	require.Len(t, logsResp.WorkspaceSetupStatus.Steps, 1, "Should have exactly one step")
	require.Equal(t, log1, logsResp.WorkspaceSetupStatus.Steps[0].Logs, "Logs should match")
	require.Equal(t, uint32(1), logsResp.WorkspaceSetupStatus.Steps[0].SequenceId, "Sequence ID should match")
	require.Equal(t, uint32(1), logsResp.WorkspaceSetupStatus.Steps[0].StepNumber, "Step number should match")

	// Now request the logs again, it should return both steps
	lastProcessedStep2 := uint32(1)
	lastProcessedSequenceId2 := uint32(2)
	logsReq2 := &remoteagentsproto.WorkspaceLogsRequest{
		RemoteAgentId:           testParameters.AgentID,
		LastProcessedStep:       &lastProcessedStep2,
		LastProcessedSequenceId: &lastProcessedSequenceId2,
	}
	logsResp2, err := tse.server.WorkspaceLogs(ctx, logsReq2)
	require.NoError(t, err, "Failed to get workspace logs")
	require.NotNil(t, logsResp2, "Workspace logs response should not be nil")
	require.Len(t, logsResp2.WorkspaceSetupStatus.Steps, 2, "Should have exactly two steps")
	require.Equal(t, log2, logsResp2.WorkspaceSetupStatus.Steps[0].Logs, "Logs should match")
	require.Equal(t, log3, logsResp2.WorkspaceSetupStatus.Steps[1].Logs, "Logs should match")
	require.Equal(t, uint32(2), logsResp2.WorkspaceSetupStatus.Steps[0].SequenceId, "Sequence ID should match")
	require.Equal(t, uint32(3), logsResp2.WorkspaceSetupStatus.Steps[1].SequenceId, "Sequence ID should match")
	require.Equal(t, uint32(1), logsResp2.WorkspaceSetupStatus.Steps[0].StepNumber, "Step number should match")
	require.Equal(t, uint32(1), logsResp2.WorkspaceSetupStatus.Steps[1].StepNumber, "Step number should match")
}

func TestInitialUpdateSequenceID(t *testing.T) {
	// First, create an agent in BigTable
	testParameters := setupTestParameters()

	// Create the agent config in BigTable
	agentConfig := createTestAgentConfig()

	// Write the agent config to BigTable
	err := writeRemoteAgentAtCreation(
		context.Background(),
		testParameters.RequestCtx,
		realBigtableProxyClient,
		testParameters.TenantID,
		testParameters.UserID,
		testParameters.AgentID,
		agentConfig,
		"test-namespace",
	)
	require.NoError(t, err, "Failed to write agent config to BigTable")

	// Verify that the sequence ID was initialized to 0
	// Read the sequence ID directly from BigTable
	filter := bigtableproxy.ChainFilters(
		bigtableproxy.FamilyFilter(outputFamilyName),
		bigtableproxy.ColumnFilter(sequenceIDColumn),
		bigtableproxy.LatestNFilter(1),
	).Proto()

	rows, err := realBigtableProxyClient.ReadRows(
		context.Background(),
		testParameters.TenantID,
		proxyproto.TableName_REMOTE_AGENTS,
		&bigtableproto.RowSet{
			RowKeys: [][]byte{getRemoteAgentUpdateSequenceIDKey(testParameters.AgentID)},
		},
		filter,
		0,
		testParameters.RequestCtx,
	)
	require.NoError(t, err, "Failed to read sequence ID from BigTable")
	require.Len(t, rows, 1, "Should have exactly one row for sequence ID")

	// Extract the sequence ID from the row
	var seqID uint32
	for _, cell := range rows[0].Cells {
		if cell.FamilyName == outputFamilyName && string(cell.Qualifier) == sequenceIDColumn {
			// Unmarshal the proto
			seqIDProto := &entitiesproto.UpdateSequenceID{}
			err := proto.Unmarshal(cell.Value, seqIDProto)
			require.NoError(t, err, "Failed to unmarshal sequence ID")
			seqID = seqIDProto.Value
			break
		}
	}

	// Verify that the sequence ID is 0
	require.Equal(t, uint32(0), seqID, "Sequence ID should be initialized to 0")

	// Now test the deletion case
	err = deleteRemoteAgent(
		context.Background(),
		testParameters.RequestCtx,
		realBigtableProxyClient,
		testParameters.TenantID,
		testParameters.UserID,
		testParameters.AgentID,
	)
	require.NoError(t, err, "Failed to delete agent config from BigTable")

	// Verify that the sequence ID row was deleted
	rows, err = realBigtableProxyClient.ReadRows(
		context.Background(),
		testParameters.TenantID,
		proxyproto.TableName_REMOTE_AGENTS,
		&bigtableproto.RowSet{
			RowKeys: [][]byte{getRemoteAgentUpdateSequenceIDKey(testParameters.AgentID)},
		},
		filter,
		0,
		testParameters.RequestCtx,
	)
	require.NoError(t, err, "Failed to read sequence ID from BigTable after deletion")
	require.Len(t, rows, 0, "Should have no rows for sequence ID after deletion")
}

// TestCreateAgent_MaxAgentsLimit verifies that the CreateAgent operation fails when the user has reached the maximum number of agents
func TestCreateAgent_MaxAgentsLimit(t *testing.T) {
	tse := setupTestServerWithEmulator(t)
	testParameters := setupTestParameters()
	max_agents, err := featureflags.NewIntFlag("max_remote_agents_per_user", 100).Get(featureflags.NewLocalFeatureFlagHandler())
	if err != nil {
		t.Fatalf("Failed to get max_remote_agents_per_user flag: %v", err)
	}

	// Create the agent config in BigTable

	// Create agents up to the limit
	for i := 0; i < max_agents; i++ {
		agentID := fmt.Sprintf("test-agent-%d", i)
		agentConfig := createTestAgentConfig()
		err := writeRemoteAgentAtCreation(
			context.Background(),
			testParameters.RequestCtx,
			realBigtableProxyClient,
			testParameters.TenantID,
			testParameters.UserID,
			agentID,
			agentConfig,
			"test-namespace",
		)
		require.NoError(t, err, "Failed to write agent config to BigTable")

		if i%2 == 0 {
			// Update the workspace status to PAUSING
			agentEntity, err := readRemoteAgentEntity(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, agentID)
			require.NoError(t, err, "Failed to read agent entity")
			agentEntity.Status.Status = remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE
			agentEntity.Status.WorkspaceStatus = remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSING
			err = agentEntity.AtomicSave(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID)
			require.NoError(t, err, "Failed to update agent status to PAUSED")
		}
	}

	// Now try to create one more agent, which should fail
	ctx := requestcontext.NewIncomingContext(testParameters.Ctx, testParameters.RequestCtx)
	req := &remoteagentsproto.CreateAgentRequest{Config: createTestAgentConfig()}

	// The test should fail before ApplyWorkspace is called, but we need to set up the mock
	// to handle the case where the test fails in a different way
	tse.mockRWC.EXPECT().
		ApplyWorkspace(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
		Return(nil, errors.New("This should not be called")).
		AnyTimes()

	resp, err := tse.server.CreateAgent(ctx, req)
	// Verify that the request failed with ResourceExhausted error
	require.Error(t, err, "Expected error when exceeding max agents limit")
	require.Nil(t, resp, "Response should be nil when exceeding max agents limit")

	// Check the error code and message
	st, ok := status.FromError(err)
	require.True(t, ok, "Error should be a gRPC status error")
	require.Equal(t, codes.ResourceExhausted, st.Code(), "Error should be ResourceExhausted")
	require.Contains(t, st.Message(), fmt.Sprintf("maximum number of remote agents (%d)", max_agents), "Error message should mention the limit")
}

// TestCreateAgent_MaxActiveAgentsLimit verifies that the CreateAgent operation fails when the user has reached the maximum number of active agents
func TestCreateAgent_MaxActiveAgentsLimit(t *testing.T) {
	tse := setupTestServerWithEmulator(t)
	testParameters := setupTestParameters()

	// Get the max active agents limit from the feature flag
	max_active_agents, err := featureflags.NewIntFlag("max_active_remote_agents_per_user", 10).Get(featureflags.NewLocalFeatureFlagHandler())
	if err != nil {
		t.Fatalf("Failed to get max_active_remote_agents_per_user flag: %v", err)
	}

	// Create active agents up to the limit
	for i := 0; i < max_active_agents; i++ {
		agentID := fmt.Sprintf("test-active-agent-%d", i)
		agentConfig := createTestAgentConfig()
		err := writeRemoteAgentAtCreation(
			context.Background(),
			testParameters.RequestCtx,
			realBigtableProxyClient,
			testParameters.TenantID,
			testParameters.UserID,
			agentID,
			agentConfig,
			"test-namespace",
		)
		require.NoError(t, err, "Failed to write agent config to BigTable")
	}

	// Create a few paused agents (these shouldn't count toward the active limit)
	for i := 0; i < 3; i++ {
		agentID := fmt.Sprintf("test-paused-agent-%d", i)
		agentConfig := createTestAgentConfig()
		err := writeRemoteAgentAtCreation(
			context.Background(),
			testParameters.RequestCtx,
			realBigtableProxyClient,
			testParameters.TenantID,
			testParameters.UserID,
			agentID,
			agentConfig,
			"test-namespace",
		)
		require.NoError(t, err, "Failed to write agent config to BigTable")

		// Update the workspace status to PAUSING -> PAUSED (correct state transitions)
		agentEntity, err := readRemoteAgentEntity(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, agentID)
		require.NoError(t, err, "Failed to read agent entity")
		agentEntity.Status.Status = remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE
		agentEntity.Status.WorkspaceStatus = remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSING
		err = agentEntity.AtomicSave(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID)
		require.NoError(t, err, "Failed to update agent status to PAUSED")
		agentEntity, err = readRemoteAgentEntity(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, agentID)
		require.NoError(t, err, "Failed to read agent entity after PAUSING")
		agentEntity.Status.WorkspaceStatus = remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSED
		err = agentEntity.AtomicSave(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID)
		require.NoError(t, err, "Failed to update agent status to PAUSED")
	}

	// Now try to create one more agent, which should fail due to active agent limit
	ctx := requestcontext.NewIncomingContext(testParameters.Ctx, testParameters.RequestCtx)
	req := &remoteagentsproto.CreateAgentRequest{
		Config: createTestAgentConfig(),
	}

	// The test should fail before ApplyWorkspace is called, but we need to set up the mock
	// to handle the case where the test fails in a different way
	tse.mockRWC.EXPECT().
		ApplyWorkspace(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
		Return(nil, errors.New("This should not be called")).
		AnyTimes()

	resp, err := tse.server.CreateAgent(ctx, req)

	// Verify that the request failed with ResourceExhausted error
	require.Error(t, err, "Expected error when exceeding max active agents limit")
	require.Nil(t, resp, "Response should be nil when exceeding max active agents limit")

	// Check the error code and message
	st, ok := status.FromError(err)
	require.True(t, ok, "Error should be a gRPC status error")
	require.Equal(t, codes.ResourceExhausted, st.Code(), "Error should be ResourceExhausted")
	require.Contains(t, st.Message(), fmt.Sprintf("maximum number of active agents (%d)", max_active_agents), "Error message should mention the active limit")
}

// TestNumActiveAgents_MixedStatuses tests numActiveAgents with agents in different statuses
func TestNumActiveAgents_MixedStatuses(t *testing.T) {
	tse := setupTestServerWithEmulator(t)
	testParameters := setupTestParameters()

	// Create agents with different workspace statuses
	agentConfigs := []struct {
		agentID         string
		workspaceStatus remoteagentsproto.WorkspaceStatus
		shouldCount     bool
	}{
		{"agent-running-1", remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING, true},
		{"agent-running-2", remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING, true},
		{"agent-paused-1", remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSED, false},
		{"agent-pausing-1", remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSING, false},
	}

	expectedActiveCount := 0
	for _, config := range agentConfigs {
		// Create agent in BigTable
		agentConfig := createTestAgentConfig()
		err := writeRemoteAgentAtCreation(
			context.Background(),
			testParameters.RequestCtx,
			realBigtableProxyClient,
			testParameters.TenantID,
			testParameters.UserID,
			config.agentID,
			agentConfig,
			"test-namespace",
		)
		require.NoError(t, err, "Failed to write agent config to BigTable")

		// Update workspace status (following proper state transitions)
		agentEntity, err := readRemoteAgentEntity(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, config.agentID)
		require.NoError(t, err, "Failed to read agent entity")

		// Handle state transitions properly
		if config.workspaceStatus == remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSED {
			// First transition to PAUSING
			agentEntity.Status.Status = remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE
			agentEntity.Status.WorkspaceStatus = remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSING
			err = agentEntity.AtomicSave(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID)
			require.NoError(t, err, "Failed to update agent status to PAUSING")

			// Then transition to PAUSED
			agentEntity, err = readRemoteAgentEntity(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, config.agentID)
			require.NoError(t, err, "Failed to read agent entity after PAUSING")
			agentEntity.Status.WorkspaceStatus = remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSED
			err = agentEntity.AtomicSave(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID)
			require.NoError(t, err, "Failed to update agent status to PAUSED")
		} else if config.workspaceStatus == remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSING {
			// First transition to PAUSING
			agentEntity.Status.Status = remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE
			agentEntity.Status.WorkspaceStatus = remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSING
			err = agentEntity.AtomicSave(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID)
			require.NoError(t, err, "Failed to update agent status to PAUSING")
		} else {
			// For other statuses, update directly
			agentEntity.Status.WorkspaceStatus = config.workspaceStatus
			err = agentEntity.AtomicSave(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID)
			require.NoError(t, err, "Failed to update agent workspace status")
		}

		if config.shouldCount {
			expectedActiveCount++
		}
	}

	ctx := requestcontext.NewIncomingContext(testParameters.Ctx, testParameters.RequestCtx)

	// Call numActiveAgents
	count, err := tse.server.numActiveAgents(ctx, testParameters.RequestCtx, testParameters.TenantID, testParameters.UserID)

	// Should return correct count
	require.NoError(t, err, "numActiveAgents should not error")
	require.Equal(t, expectedActiveCount, count, "Should return correct count of active agents")
}

func TestCreateAgent_DuplicateCreation(t *testing.T) {
	tse := setupTestServerWithEmulator(t)
	testParameters := setupTestParameters()

	sharedRequestID := uuid.New().String()
	// Create a request context with the request ID
	testParameters.RequestCtx = requestcontext.New(
		requestcontext.RequestId(sharedRequestID),
		testParameters.RequestCtx.RequestSessionId,
		testParameters.RequestCtx.RequestSource,
		testParameters.RequestCtx.AuthToken,
	)
	req := &remoteagentsproto.CreateAgentRequest{
		Config: createTestAgentConfig(),
	}
	createdAt := time.Now()
	mockAgent := &remoteagentsproto.Agent{
		RemoteAgentId: "test-agent-id", // This will be replaced in the test
		Status:        remoteagentsproto.AgentStatus_AGENT_STATUS_STARTING,
		Config:        req.Config,
		CreatedAt:     timestamppb.New(createdAt),
	}

	mockWorkspace := agentsmock.NewMockRemoteWorkspace(tse.ctrl)
	tse.mockRWC.EXPECT().
		ApplyWorkspace(gomock.Any(), testParameters.UserID, gomock.Any(), gomock.Any()).
		DoAndReturn(func(_ interface{}, _ string, argAgentID string, wsCfg *remoteagentsproto.WorkspaceAgentConfig) (ws.RemoteWorkspace, error) {
			expectedAgentID, err := deriveAgentIDFromRequestID(sharedRequestID, testParameters.UserID)
			require.NoError(t, err, "Failed to derive expected agent ID")
			require.Equal(t, expectedAgentID, argAgentID, "Agent ID should be deterministically derived from request ID")
			// Update the mock agent with the actual agent ID
			mockAgent.RemoteAgentId = argAgentID

			return mockWorkspace, nil
		}).
		Times(1) // Should only be called once

	mockWorkspace.EXPECT().
		Agent(gomock.Any()).
		Return(mockAgent, nil).
		Times(1) // Should only be called once

	ctx := requestcontext.NewIncomingContext(testParameters.Ctx, testParameters.RequestCtx)
	// Number of concurrent attempts
	numAttempts := 5
	// Channel to collect results
	type result struct {
		attemptID int
		response  *remoteagentsproto.CreateAgentResponse
		err       error
	}
	resultChan := make(chan result, numAttempts)
	var wg sync.WaitGroup
	wg.Add(numAttempts)

	startGate := make(chan struct{})
	for i := 0; i < numAttempts; i++ {
		go func(index int) {
			defer wg.Done()
			<-startGate
			resp, err := tse.server.CreateAgent(ctx, req)
			resultChan <- result{attemptID: index, response: resp, err: err}
		}(i)
	}

	close(startGate)
	wg.Wait()
	close(resultChan)
	var successCount int
	var firstSuccess *remoteagentsproto.CreateAgentResponse
	var errors []error

	for r := range resultChan {
		if r.err == nil {
			successCount++
			if firstSuccess == nil {
				firstSuccess = r.response
			}
		} else {
			errors = append(errors, r.err)
		}
	}

	// Verify that exactly one attempt succeeded (duplication safe but not truly idempotent)
	require.Equal(t, 1, successCount, "Exactly one attempt should succeed with duplication safety")
	require.NotNil(t, firstSuccess, "Should have exactly one successful response")
	require.Equal(t, numAttempts-1, len(errors), "All other attempts should fail")

	// Verify that all errors are AlreadyExists errors
	for _, err := range errors {
		st, ok := status.FromError(err)
		require.True(t, ok, "Error should be a gRPC status error")
		require.Equal(t, codes.AlreadyExists, st.Code(), "Error should be AlreadyExists")
	}

	// Verify the agent ID is deterministic based on the request ID
	expectedAgentID, err := deriveAgentIDFromRequestID(sharedRequestID, testParameters.UserID)
	require.NoError(t, err, "Failed to derive expected agent ID")
	require.Equal(t, expectedAgentID, firstSuccess.Agent.RemoteAgentId, "Agent ID should be deterministically derived from request ID")

	// Verify that the agent was written to BigTable by reading it back
	agent, err := readRemoteAgent(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.UserID, expectedAgentID, 30)
	require.NoError(t, err, "Failed to read agent from BigTable")
	require.NotNil(t, agent, "Agent should not be nil")
	require.Equal(t, expectedAgentID, agent.RemoteAgentId, "Agent ID should match expected ID")
}

func TestWorkspaceInterrupt_WorkspacePollingEnabled(t *testing.T) {
	// First, create an agent in BigTable
	testParameters := setupTestParameters()

	// Create the agent config in BigTable
	agentConfig := createTestAgentConfig()
	// UserId is set in writeRemoteAgentAtCreation

	// Write the agent config to BigTable
	err := writeRemoteAgentAtCreation(
		context.Background(),
		testParameters.RequestCtx,
		realBigtableProxyClient,
		testParameters.TenantID,
		testParameters.UserID,
		testParameters.AgentID,
		agentConfig,
		"test-namespace",
	)
	require.NoError(t, err, "Failed to write agent config to BigTable")

	// Now set up the server and test the stop request
	tse := setupTestServerWithEmulator(t)

	// Create request with agent ID
	req := &remoteagentsproto.InterruptAgentRequest{
		RemoteAgentId: testParameters.AgentID,
	}

	// Set a unique request ID in the context
	testParameters.RequestCtx = requestcontext.New(
		requestcontext.RequestId(uuid.New().String()),
		testParameters.RequestCtx.RequestSessionId,
		testParameters.RequestCtx.RequestSource,
		testParameters.RequestCtx.AuthToken,
	)

	// Create a context with metadata
	ctx := requestcontext.NewIncomingContext(testParameters.Ctx, testParameters.RequestCtx)

	// Call the WorkspaceInterrupt method
	resp, err := tse.server.InterruptAgent(ctx, req)

	// Verify the response
	require.NoError(t, err)
	require.NotNil(t, resp)

	// Verify that the interrupt request was written to BigTable
	pendingUpdates, err := readPendingAgentUpdates(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.UserID, testParameters.AgentID)
	require.NoError(t, err, "Failed to read pending agent updates from BigTable")
	require.NotNil(t, pendingUpdates, "Pending updates should not be nil")
	require.Len(t, pendingUpdates, 1, "Pending updates should contain exactly one update")

	// Check that the update contains an interrupt
	require.NotNil(t, pendingUpdates[0].Update, "Pending update should not be nil")
	require.NotNil(t, pendingUpdates[0].Update.GetInterrupt(), "Update should be an interrupt request")
}

func TestChat_WorkspacePollingEnabled(t *testing.T) {
	// Mock the time function to make the test deterministic
	fixedTime := time.Date(2023, 12, 25, 10, 30, 0, 0, time.UTC)
	originalNowFunc := nowFunc
	nowFunc = func() time.Time { return fixedTime }
	defer func() { nowFunc = originalNowFunc }()

	// First, create an agent in BigTable
	testParameters := setupTestParameters()

	// Create the agent config in BigTable
	agentConfig := createTestAgentConfig()
	// UserId is set in writeRemoteAgentAtCreation

	// Write the agent config to BigTable
	err := writeRemoteAgentAtCreation(
		context.Background(),
		testParameters.RequestCtx,
		realBigtableProxyClient,
		testParameters.TenantID,
		testParameters.UserID,
		testParameters.AgentID,
		agentConfig,
		"test-namespace",
	)
	require.NoError(t, err, "Failed to write agent config to BigTable")

	// Now set up the server and test the chat request
	tse := setupTestServerWithEmulator(t)

	// Create request with agent ID and chat details
	req := &remoteagentsproto.ChatRequest{
		RemoteAgentId: testParameters.AgentID,
		RequestDetails: &remoteagentsproto.ChatRequestDetails{
			RequestNodes: []*chatproto.ChatRequestNode{
				{
					Id:   1,
					Type: chatproto.ChatRequestNodeType_TEXT,
					TextNode: &chatproto.ChatRequestText{
						Content: "Hello, agent!",
					},
				},
			},
		},
	}

	// Create a context with metadata
	ctx := requestcontext.NewIncomingContext(testParameters.Ctx, testParameters.RequestCtx)

	// Call the Chat method
	resp, err := tse.server.Chat(ctx, req)

	// Verify the response
	require.NoError(t, err)
	require.NotNil(t, resp) // Chat now returns a response instead of nil

	// Verify that the chat request was written to BigTable
	pendingUpdates, err := readPendingAgentUpdates(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.UserID, testParameters.AgentID)
	require.NoError(t, err, "Failed to read pending agent updates from BigTable")
	require.NotNil(t, pendingUpdates, "Pending updates should not be nil")
	require.Len(t, pendingUpdates, 1, "Pending updates should contain exactly one update")

	// Check that the update contains a chat request
	require.NotNil(t, pendingUpdates[0].Update, "Pending update should not be nil")

	chatRequest := pendingUpdates[0].Update.GetChatRequest()
	require.NotNil(t, chatRequest, "Update should be a chat request")
	require.NotNil(t, chatRequest.RequestDetails, "Chat request should have request details")
	require.NotEmpty(t, chatRequest.RequestDetails.RequestNodes, "Chat request should have request nodes")
	require.Equal(t, 1, len(chatRequest.RequestDetails.RequestNodes), "Chat request should have exactly one request node")
	require.Equal(t, chatproto.ChatRequestNodeType_TEXT, chatRequest.RequestDetails.RequestNodes[0].Type, "Chat request node should be a text node")
	require.Equal(t, "Hello, agent!", chatRequest.RequestDetails.RequestNodes[0].TextNode.Content, "Chat request message should match")

	// Verify that last_user_update_received_at timestamp was set
	agentEntity, err := readRemoteAgentEntity(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.AgentID)
	require.NoError(t, err, "Failed to read agent entity")
	require.NotNil(t, agentEntity.Status.LastUserUpdateReceivedAt, "last_user_update_received_at should be set")
	require.Equal(t, fixedTime.Unix(), agentEntity.Status.LastUserUpdateReceivedAt.Seconds, "last_user_update_received_at should match the fixed time")
}

func TestChat_ResumeWorkspace(t *testing.T) {
	tse := setupTestServerWithEmulator(t)

	testParameters := setupTestParameters()

	// 1. Create the agent first using the existing helper, it will be in STARTING or similar state.
	initialAgentConfig := createTestAgentConfig()
	err := writeRemoteAgentAtCreation(
		testParameters.Ctx,
		testParameters.RequestCtx,
		realBigtableProxyClient,
		testParameters.TenantID,
		testParameters.UserID,
		testParameters.AgentID,
		initialAgentConfig,
		"test-namespace",
	)
	require.NoError(t, err, "Setup: Failed to write initial agent config")

	// Set the agent status to PAUSED (following the correct state transition: RUNNING -> PAUSING -> PAUSED)
	agentEntity, err := readRemoteAgentEntity(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.AgentID)
	require.NoError(t, err, "Failed to read agent entity")

	// First transition from RUNNING to PAUSING (also set agent status to IDLE to pass validation)
	agentEntity.Status.WorkspaceStatus = remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSING
	agentEntity.Status.Status = remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE
	err = agentEntity.AtomicSave(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID)
	require.NoError(t, err, "Failed to update agent status to PAUSING")
	agentEntity, err = readRemoteAgentEntity(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.AgentID)
	require.NoError(t, err, "Failed to read agent entity after PAUSING")
	agentEntity.Status.WorkspaceStatus = remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSED
	err = agentEntity.AtomicSave(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID)
	require.NoError(t, err, "Failed to update agent status to PAUSED")

	// Expect ResumeWorkspace to be called
	tse.mockRWC.EXPECT().
		ResumeWorkspace(gomock.Any(), testParameters.UserID, testParameters.AgentID).
		DoAndReturn(func(_ interface{}, _ string, argAgentID string) error {
			require.Equal(t, testParameters.AgentID, argAgentID, "Agent ID should match")
			_, wsErr := tse.server.WorkspaceReportStatus(testParameters.Ctx, &remoteagentsproto.WorkspaceReportStatusRequest{
				RemoteAgentId: argAgentID,
				Status:        remoteagentsproto.AgentStatus_AGENT_STATUS_RUNNING,
			})
			require.NoError(t, wsErr, "Failed to report workspace status")
			return nil
		}).
		Times(1)

	// Prepare ChatRequest
	req := &remoteagentsproto.ChatRequest{
		RemoteAgentId: testParameters.AgentID,
		RequestDetails: &remoteagentsproto.ChatRequestDetails{
			RequestNodes: []*chatproto.ChatRequestNode{
				{
					Id:   1,
					Type: chatproto.ChatRequestNodeType_TEXT,
					TextNode: &chatproto.ChatRequestText{
						Content: "Hello, resume this workspace!",
					},
				},
			},
		},
	}
	ctx := requestcontext.NewIncomingContext(testParameters.Ctx, testParameters.RequestCtx)

	// Call Chat
	_, chatErr := tse.server.Chat(ctx, req)
	require.NoError(t, chatErr)

	// Verify agent status is now RUNNING
	updatedAgentEntity, err := readRemoteAgentEntity(testParameters.Ctx, testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.AgentID)
	require.NoError(t, err, "Failed to read agent entity after chat call")
	require.NotNil(t, updatedAgentEntity, "Updated agent entity should not be nil")
	require.Equal(t, remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING, updatedAgentEntity.Status.WorkspaceStatus, "WorkspaceStatus should be RUNNING")
}

func TestChat_ResumeWorkspace_ResumeFailure(t *testing.T) {
	tse := setupTestServerWithEmulator(t)

	testParameters := setupTestParameters()
	expectedError := errors.New("resume workspace failed")

	// Create the agent in BigTable
	initialAgentConfig := createTestAgentConfig()
	err := writeRemoteAgentAtCreation(
		testParameters.Ctx,
		testParameters.RequestCtx,
		realBigtableProxyClient,
		testParameters.TenantID,
		testParameters.UserID,
		testParameters.AgentID,
		initialAgentConfig,
		"test-namespace",
	)
	require.NoError(t, err, "Setup: Failed to write initial agent config")

	// Set the agent status to PAUSED (following the correct state transition: RUNNING -> PAUSING -> PAUSED)
	agentEntity, err := readRemoteAgentEntity(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.AgentID)
	require.NoError(t, err, "Failed to read agent entity")

	// First transition from RUNNING to PAUSING (also set agent status to IDLE to pass validation)
	agentEntity.Status.WorkspaceStatus = remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSING
	agentEntity.Status.Status = remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE
	err = agentEntity.AtomicSave(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID)
	require.NoError(t, err, "Failed to update agent status to PAUSING")
	agentEntity, err = readRemoteAgentEntity(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.AgentID)
	require.NoError(t, err, "Failed to read agent entity after PAUSING")
	agentEntity.Status.WorkspaceStatus = remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSED
	err = agentEntity.AtomicSave(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID)
	require.NoError(t, err, "Failed to update agent status to PAUSED")

	// Expect ResumeWorkspace to be called and fail
	tse.mockRWC.EXPECT().
		ResumeWorkspace(gomock.Any(), testParameters.UserID, testParameters.AgentID).
		Return(expectedError).
		AnyTimes()

	// Prepare ChatRequest
	req := &remoteagentsproto.ChatRequest{
		RemoteAgentId: testParameters.AgentID,
		RequestDetails: &remoteagentsproto.ChatRequestDetails{
			RequestNodes: []*chatproto.ChatRequestNode{
				{Id: 1, Type: chatproto.ChatRequestNodeType_TEXT, TextNode: &chatproto.ChatRequestText{Content: "Test"}},
			},
		},
	}
	ctx := requestcontext.NewIncomingContext(testParameters.Ctx, testParameters.RequestCtx)

	// Call Chat
	_, chatErr := tse.server.Chat(ctx, req)
	require.Error(t, chatErr)
	require.True(t, errors.Is(chatErr, expectedError), "Error from Chat should match ResumeWorkspace error")

	// Verify agent status is RESUMING (as revert is not implemented yet)
	finalAgentEntity, err := readRemoteAgentEntity(testParameters.Ctx, testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.AgentID)
	require.NoError(t, err, "Failed to read agent entity after failed chat call")
	require.NotNil(t, finalAgentEntity, "Final agent entity should not be nil")
	require.Equal(t, remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RESUMING, finalAgentEntity.Status.WorkspaceStatus, "WorkspaceStatus should be RESUMING")
	require.Equal(t, remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE, finalAgentEntity.Status.Status, "AgentStatus should still be IDLE")
}

// TestChat_PausedAgentWithMaxActiveAgents tests that chat fails when trying to resume a paused agent at the active agent limit
func TestChat_PausedAgentWithMaxActiveAgents(t *testing.T) {
	tse := setupTestServerWithEmulator(t)
	testParameters := setupTestParameters()

	// Get the max active agents limit from the feature flag
	max_active_agents, err := featureflags.NewIntFlag("max_active_remote_agents_per_user", 10).Get(featureflags.NewLocalFeatureFlagHandler())
	require.NoError(t, err, "Failed to get max_active_remote_agents_per_user flag")

	// Create active agents up to the limit
	for i := 0; i < max_active_agents; i++ {
		agentID := fmt.Sprintf("active-agent-%d", i)
		agentConfig := createTestAgentConfig()
		err := writeRemoteAgentAtCreation(
			context.Background(),
			testParameters.RequestCtx,
			realBigtableProxyClient,
			testParameters.TenantID,
			testParameters.UserID,
			agentID,
			agentConfig,
			"test-namespace",
		)
		require.NoError(t, err, "Failed to write active agent config to BigTable")

		// Keep them in RUNNING state (default)
	}

	// Create one paused agent that we'll try to chat with
	pausedAgentID := "paused-agent-to-chat"
	agentConfig := createTestAgentConfig()
	err = writeRemoteAgentAtCreation(
		context.Background(),
		testParameters.RequestCtx,
		realBigtableProxyClient,
		testParameters.TenantID,
		testParameters.UserID,
		pausedAgentID,
		agentConfig,
		"test-namespace",
	)
	require.NoError(t, err, "Failed to write paused agent config to BigTable")

	// Set the agent to PAUSED status (following correct state transition: RUNNING -> PAUSING -> PAUSED)
	agentEntity, err := readRemoteAgentEntity(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, pausedAgentID)
	require.NoError(t, err, "Failed to read agent entity")
	agentEntity.Status.Status = remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE
	agentEntity.Status.WorkspaceStatus = remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSING
	err = agentEntity.AtomicSave(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID)
	require.NoError(t, err, "Failed to update agent status to PAUSING")

	agentEntity, err = readRemoteAgentEntity(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, pausedAgentID)
	require.NoError(t, err, "Failed to read agent entity after PAUSING")
	agentEntity.Status.WorkspaceStatus = remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSED
	err = agentEntity.AtomicSave(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID)
	require.NoError(t, err, "Failed to update agent status to PAUSED")

	// Verify we're at the limit
	ctx := requestcontext.NewIncomingContext(testParameters.Ctx, testParameters.RequestCtx)
	activeCount, err := tse.server.numActiveAgents(ctx, testParameters.RequestCtx, testParameters.TenantID, testParameters.UserID)
	require.NoError(t, err, "Failed to get active agent count")
	require.Equal(t, max_active_agents, activeCount, "Should be at the active agent limit")

	// Try to chat with the paused agent
	chatReq := &remoteagentsproto.ChatRequest{
		RemoteAgentId: pausedAgentID,
		RequestDetails: &remoteagentsproto.ChatRequestDetails{
			RequestNodes: []*chatproto.ChatRequestNode{
				{
					Id:   1,
					Type: chatproto.ChatRequestNodeType_TEXT,
					TextNode: &chatproto.ChatRequestText{
						Content: "Hello, please resume!",
					},
				},
			},
		},
	}

	// Call Chat - this should fail because we're at the active agent limit
	resp, err := tse.server.Chat(ctx, chatReq)

	// Verify that the request failed with ResourceExhausted error
	require.Error(t, err, "Expected error when trying to chat with paused agent at active limit")
	require.Nil(t, resp, "Response should be nil when exceeding max active agents limit")

	// Check the error code and message
	st, ok := status.FromError(err)
	require.True(t, ok, "Error should be a gRPC status error")
	require.Equal(t, codes.ResourceExhausted, st.Code(), "Error should be ResourceExhausted")
	require.Contains(t, st.Message(), fmt.Sprintf("maximum number of active agents (%d)", max_active_agents), "Error message should mention the active limit")

	// Verify the paused agent is still paused (wasn't resumed)
	agentEntity, err = readRemoteAgentEntity(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, pausedAgentID)
	require.NoError(t, err, "Failed to read agent entity after failed chat")
	require.Equal(t, remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSED, agentEntity.Status.WorkspaceStatus, "Agent should still be paused")
}

// TestResumeAgent_PausedAgentWithMaxActiveAgents tests that ResumeAgent fails when trying to resume a paused agent at the active agent limit
func TestResumeAgent_PausedAgentWithMaxActiveAgents(t *testing.T) {
	tse := setupTestServerWithEmulator(t)
	testParameters := setupTestParameters()

	// Get the max active agents limit from the feature flag
	max_active_agents, err := featureflags.NewIntFlag("max_active_remote_agents_per_user", 10).Get(featureflags.NewLocalFeatureFlagHandler())
	require.NoError(t, err, "Failed to get max_active_remote_agents_per_user flag")

	// Create active agents up to the limit
	for i := 0; i < max_active_agents; i++ {
		agentID := fmt.Sprintf("resume-test-active-agent-%d-%d", time.Now().UnixNano(), i)
		agentConfig := createTestAgentConfig()
		err := writeRemoteAgentAtCreation(
			context.Background(),
			testParameters.RequestCtx,
			realBigtableProxyClient,
			testParameters.TenantID,
			testParameters.UserID,
			agentID,
			agentConfig,
			"test-namespace",
		)
		require.NoError(t, err, "Failed to write active agent config to BigTable")

		// Keep them in RUNNING state (default)
	}

	// Create one paused agent that we'll try to resume
	pausedAgentID := fmt.Sprintf("resume-test-paused-agent-%d", time.Now().UnixNano())
	agentConfig := createTestAgentConfig()
	err = writeRemoteAgentAtCreation(
		context.Background(),
		testParameters.RequestCtx,
		realBigtableProxyClient,
		testParameters.TenantID,
		testParameters.UserID,
		pausedAgentID,
		agentConfig,
		"test-namespace",
	)
	require.NoError(t, err, "Failed to write paused agent config to BigTable")

	// Set the agent to PAUSED status (following correct state transition: RUNNING -> PAUSING -> PAUSED)
	agentEntity, err := readRemoteAgentEntity(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, pausedAgentID)
	require.NoError(t, err, "Failed to read agent entity")
	agentEntity.Status.Status = remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE
	agentEntity.Status.WorkspaceStatus = remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSING
	err = agentEntity.AtomicSave(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID)
	require.NoError(t, err, "Failed to update agent status to PAUSING")

	agentEntity, err = readRemoteAgentEntity(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, pausedAgentID)
	require.NoError(t, err, "Failed to read agent entity after PAUSING")
	agentEntity.Status.WorkspaceStatus = remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSED
	err = agentEntity.AtomicSave(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID)
	require.NoError(t, err, "Failed to update agent status to PAUSED")

	// Verify we're at the limit
	ctx := requestcontext.NewIncomingContext(testParameters.Ctx, testParameters.RequestCtx)
	activeCount, err := tse.server.numActiveAgents(ctx, testParameters.RequestCtx, testParameters.TenantID, testParameters.UserID)
	require.NoError(t, err, "Failed to get active agent count")
	require.Equal(t, max_active_agents, activeCount, "Should be at the active agent limit")

	// Try to resume the paused agent
	resumeReq := &remoteagentsproto.ResumeAgentRequest{
		RemoteAgentId: pausedAgentID,
	}

	// Call ResumeAgent - this should fail because we're at the active agent limit
	resp, err := tse.server.ResumeAgent(ctx, resumeReq)

	// Verify that the request failed with ResourceExhausted error
	require.Error(t, err, "Expected error when trying to resume paused agent at active limit")
	require.Nil(t, resp, "Response should be nil when exceeding max active agents limit")

	// Check the error code and message
	st, ok := status.FromError(err)
	require.True(t, ok, "Error should be a gRPC status error")
	require.Equal(t, codes.ResourceExhausted, st.Code(), "Error should be ResourceExhausted")
	require.Contains(t, st.Message(), fmt.Sprintf("maximum number of active agents (%d)", max_active_agents), "Error message should mention the active limit")

	// Verify the paused agent is still paused (wasn't resumed)
	agentEntity, err = readRemoteAgentEntity(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, pausedAgentID)
	require.NoError(t, err, "Failed to read agent entity after failed resume")
	require.Equal(t, remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSED, agentEntity.Status.WorkspaceStatus, "Agent should still be paused")
}

// TestConcurrentSequenceIDIncrement tests that concurrent calls to readAndIncrementUpdateSequenceID
// all get different sequence IDs
func TestConcurrentSequenceIDIncrement(t *testing.T) {
	// Create a context and request context for the test
	ctx := context.Background()
	// Use a UUID for the request ID to ensure it can be parsed correctly
	requestCtx := requestcontext.New(
		requestcontext.RequestId(uuid.New().String()),
		requestcontext.RequestSessionId("test-session-id"),
		"test-source",
		secretstring.New("test-token"),
	)

	// write the initial sequence ID of 0 for concurrency control
	agentID := uuid.New().String()
	tenantID := "test-tenant"
	// Store the sequence ID as a proto message
	seqIDProto := &entitiesproto.UpdateSequenceID{
		Value: 0,
	}
	protoValue, err := proto.Marshal(seqIDProto)
	require.NoError(t, err, "Failed to marshal sequence ID")
	_, err = realBigtableProxyClient.MutateRows(
		ctx,
		tenantID,
		proxyproto.TableName_REMOTE_AGENTS,
		[]*bigtableproto.MutateRowsRequest_Entry{
			{
				RowKey: getRemoteAgentUpdateSequenceIDKey(agentID),
				Mutations: []*bigtableproto.Mutation{
					{
						Mutation: &bigtableproto.Mutation_SetCell_{
							SetCell: &bigtableproto.Mutation_SetCell{
								FamilyName:      outputFamilyName,
								ColumnQualifier: []byte("sequence_id"),
								Value:           protoValue,
							},
						},
					},
				},
			},
		},
		requestCtx,
	)
	require.NoError(t, err, "Failed to write initial sequence ID")

	// Number of concurrent goroutines to run
	numGoroutines := 10

	// Channel to collect results
	type result struct {
		goroutineID int
		sequenceID  uint32
		err         error
	}
	resultChan := make(chan result, numGoroutines)

	// Use a WaitGroup to wait for all goroutines to complete
	var wg sync.WaitGroup
	wg.Add(numGoroutines)

	// Create a starting gate to ensure all goroutines start at roughly the same time
	startGate := make(chan struct{})

	// Start multiple goroutines that all try to increment the sequence ID
	for i := 0; i < numGoroutines; i++ {
		go func(index int) {
			defer wg.Done()
			<-startGate
			seqID, err := retryWithExponentialBackoff[uint32](ctx, maxExponentialBackoffAttempts, func() (uint32, error) {
				return readAndIncrementUpdateSequenceID(ctx, requestCtx, realBigtableProxyClient, tenantID, agentID)
			})
			resultChan <- result{goroutineID: index, sequenceID: seqID, err: err}
		}(i)
	}
	// Open the starting gate to let all goroutines proceed at once
	close(startGate)
	wg.Wait()
	close(resultChan)
	// Collect all the sequence IDs
	successResults := make([]result, 0, numGoroutines)
	errorResults := make([]result, 0)
	for r := range resultChan {
		if r.err != nil {
			errorResults = append(errorResults, r)
		} else {
			successResults = append(successResults, r)
		}
	}
	require.NotEmpty(t, successResults, "At least some goroutines should succeed")
	// Check if we have any duplicate sequence IDs
	seqIDCounts := make(map[uint32]int)
	for _, r := range successResults {
		seqIDCounts[r.sequenceID]++
	}
	duplicates := false
	for _, count := range seqIDCounts {
		if count > 1 {
			duplicates = true
		}
	}
	// Now we'll fail the test if there are duplicates
	require.False(t, duplicates, "Duplicate sequence IDs detected! This indicates a concurrency issue.")

	// Check that the sequence IDs are consecutive starting from 1
	expectedSeqIDs := make(map[uint32]bool)
	for i := uint32(1); i <= uint32(len(successResults)); i++ {
		expectedSeqIDs[i] = true
	}

	// Check if we're missing any expected sequence IDs
	missing := false
	for i := uint32(1); i <= uint32(len(successResults)); i++ {
		if _, exists := seqIDCounts[i]; !exists {
			missing = true
			fmt.Printf("WARNING: Expected sequence ID %d was not returned\n", i)
		}
	}

	require.False(t, missing, "Missing sequence IDs detected! This indicates a concurrency issue.")
}

// TestReadRemoteAgentsBatch tests the readRemoteAgentsBatch function
// It creates 100 agents and then reads them in batches of 10
func TestReadRemoteAgentsBatch(t *testing.T) {
	// Create a context and request context for the test
	ctx := context.Background()
	requestCtx := requestcontext.New(
		requestcontext.RequestId(uuid.New().String()),
		requestcontext.RequestSessionId("test-session-id"),
		"test-source",
		secretstring.New("test-token"),
	)
	tenantID := "test-tenant"
	userID := uuid.New().String()

	// Create 100 agents in BigTable
	numAgents := 100
	agentIDs := make([]string, numAgents)
	// cleanup any existing rows in the table
	err := deleteAllRowsInTable(ctx, tenantID, requestCtx)
	require.NoError(t, err, "Failed to delete all rows in table")

	for i := 0; i < numAgents; i++ {
		// Create a unique agent ID with a predictable prefix for sorting
		agentID := fmt.Sprintf("test-agent-%03d-%s", i, uuid.New().String())
		agentIDs[i] = agentID

		// Create the agent config
		agentConfig := createTestAgentConfig()

		// Write the agent to BigTable
		err := writeRemoteAgentAtCreation(
			ctx,
			requestCtx,
			realBigtableProxyClient,
			tenantID,
			userID,
			agentID,
			agentConfig,
			"test-namespace",
		)
		require.NoError(t, err, "Failed to write agent %d to BigTable", i)
	}

	// Read the agents in batches of 10
	pageSize := 10
	var startRowKey string
	var allAgents []*AgentEntity

	// We should get 10 pages of 10 agents each
	for page := 0; page < numAgents/pageSize; page++ {
		agents, err := readRemoteAgentsBatchForTenant(ctx, requestCtx, realBigtableProxyClient, tenantID, startRowKey, pageSize)
		require.NoError(t, err, "Failed to read agents batch for page %d", page)
		require.Len(t, agents, pageSize, "Expected %d agents in page %d, got %d", pageSize, page, len(agents))

		// The last row key of this batch becomes the start row key for the next batch
		if len(agents) > 0 {
			lastAgent := agents[len(agents)-1]
			startRowKey = lastAgent.AgentID
		}

		// Add these agents to our collection
		allAgents = append(allAgents, agents...)
	}

	// Verify we got all 100 agents
	require.Len(t, allAgents, numAgents, "Expected to read all %d agents", numAgents)

	// Create a map of agent IDs we created
	createdAgentIDs := make(map[string]bool)
	for _, agentID := range agentIDs {
		createdAgentIDs[agentID] = true
	}
	require.Len(t, createdAgentIDs, numAgents, "Expected to create %d unique agent IDs", numAgents)

	// Verify each agent has the expected data
	for _, agent := range allAgents {
		require.NotNil(t, agent.Config, "Agent config should not be nil")
		require.NotNil(t, agent.Status, "Agent status should not be nil")
		require.Equal(t, remoteagentsproto.AgentStatus_AGENT_STATUS_STARTING, agent.Status.Status, "Agent status should be STARTING")
		require.Equal(t, userID, agent.Config.UserId, "Agent user ID should match")
		require.Equal(t, "test-namespace", agent.Config.Namespace, "Agent namespace should match")
		// Verify that this agent ID was one we created
		require.True(t, createdAgentIDs[agent.AgentID], "Agent ID %s was not in the list of created agent IDs", agent.AgentID)
		// Mark this ID as found
		delete(createdAgentIDs, agent.AgentID)
	}

	// Verify that all agent IDs were found
	require.Empty(t, createdAgentIDs, "Not all created agent IDs were found in the results: %v", createdAgentIDs)
}

func TestWorkspacePollUpdate_MultipleUpdates(t *testing.T) {
	testParameters := setupTestParameters()
	agentConfig := createTestAgentConfig()
	err := writeRemoteAgentAtCreation(
		context.Background(),
		testParameters.RequestCtx,
		realBigtableProxyClient,
		testParameters.TenantID,
		testParameters.UserID,
		testParameters.AgentID,
		agentConfig,
		"test-namespace",
	)
	require.NoError(t, err, "Failed to write agent config to BigTable")

	tse := setupTestServerWithEmulator(t)

	ctx := requestcontext.NewIncomingContext(testParameters.Ctx, testParameters.RequestCtx)

	// 1. Send an interrupt request
	interruptReq := &remoteagentsproto.InterruptAgentRequest{
		RemoteAgentId: testParameters.AgentID,
	}
	interruptResp, err := tse.server.InterruptAgent(ctx, interruptReq)
	require.NoError(t, err)
	require.NotNil(t, interruptResp)

	// 2. Send a chat request
	chatReq := &remoteagentsproto.ChatRequest{
		RemoteAgentId: testParameters.AgentID,
		RequestDetails: &remoteagentsproto.ChatRequestDetails{
			RequestNodes: []*chatproto.ChatRequestNode{
				{
					Id:   1,
					Type: chatproto.ChatRequestNodeType_TEXT,
					TextNode: &chatproto.ChatRequestText{
						Content: "First message",
					},
				},
			},
		},
	}
	chatResp, err := tse.server.Chat(ctx, chatReq)
	require.NoError(t, err)
	require.NotNil(t, chatResp) // Chat now returns a response instead of nil

	// 3. Send another interrupt request
	interruptReq2 := &remoteagentsproto.InterruptAgentRequest{
		RemoteAgentId: testParameters.AgentID,
	}
	interruptResp2, err := tse.server.InterruptAgent(ctx, interruptReq2)
	require.NoError(t, err)
	require.NotNil(t, interruptResp2)

	// 4. Send another chat request
	chatReq2 := &remoteagentsproto.ChatRequest{
		RemoteAgentId: testParameters.AgentID,
		RequestDetails: &remoteagentsproto.ChatRequestDetails{
			RequestNodes: []*chatproto.ChatRequestNode{
				{
					Id:   1,
					Type: chatproto.ChatRequestNodeType_TEXT,
					TextNode: &chatproto.ChatRequestText{
						Content: "Second message",
					},
				},
			},
		},
	}
	chatResp2, err := tse.server.Chat(ctx, chatReq2)
	require.NoError(t, err)
	require.NotNil(t, chatResp2) // Chat now returns a response instead of nil

	// Now read all pending updates to verify they were written correctly
	pendingUpdates, err := readPendingAgentUpdates(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.UserID, testParameters.AgentID)
	require.NoError(t, err, "Failed to read pending agent updates from BigTable")
	require.NotNil(t, pendingUpdates, "Pending updates should not be nil")
	require.Len(t, pendingUpdates, 4, "Pending updates should contain exactly four updates")

	// Verify the sequence IDs are correct and in order
	// With the new Pub/Sub implementation, sequence IDs start at 1 and increment by 1
	// The exact values don't matter as long as they're in order
	require.Less(t, uint32(0), pendingUpdates[0].SequenceId, "First update should have sequence ID > 0")
	require.Less(t, pendingUpdates[0].SequenceId, pendingUpdates[1].SequenceId, "Second update should have sequence ID > first")
	require.Less(t, pendingUpdates[1].SequenceId, pendingUpdates[2].SequenceId, "Third update should have sequence ID > second")
	require.Less(t, pendingUpdates[2].SequenceId, pendingUpdates[3].SequenceId, "Fourth update should have sequence ID > third")

	// Verify the update types are correct
	require.NotNil(t, pendingUpdates[0].Update.GetInterrupt(), "First update should be an interrupt")
	require.NotNil(t, pendingUpdates[1].Update.GetChatRequest(), "Second update should be a chat request")
	require.NotNil(t, pendingUpdates[2].Update.GetInterrupt(), "Third update should be an interrupt")
	require.NotNil(t, pendingUpdates[3].Update.GetChatRequest(), "Fourth update should be a chat request")

	// Verify the chat request contents
	require.Equal(t, "First message", pendingUpdates[1].Update.GetChatRequest().RequestDetails.RequestNodes[0].TextNode.Content, "First chat message content should match")
	require.Equal(t, "Second message", pendingUpdates[3].Update.GetChatRequest().RequestDetails.RequestNodes[0].TextNode.Content, "Second chat message content should match")

	// Now test polling with different last_processed_sequence_id values

	// Test 1: Poll with last_processed_sequence_id = 0 (should get all updates)
	pollReq := &remoteagentsproto.WorkspacePollUpdateRequest{
		RemoteAgentId:           testParameters.AgentID,
		LastProcessedSequenceId: 0,
	}
	pollResp, err := tse.server.WorkspacePollUpdate(ctx, pollReq)
	require.NoError(t, err)
	require.NotNil(t, pollResp)
	require.Len(t, pollResp.Updates, 4, "Should return all 4 updates when last_processed_sequence_id = 0")

	// Test 2: Poll with last_processed_sequence_id = second update's sequence ID
	// (should get the third and fourth updates)
	pollReq2 := &remoteagentsproto.WorkspacePollUpdateRequest{
		RemoteAgentId:           testParameters.AgentID,
		LastProcessedSequenceId: pendingUpdates[1].SequenceId,
	}
	pollResp2, err := tse.server.WorkspacePollUpdate(ctx, pollReq2)
	require.NoError(t, err)
	require.NotNil(t, pollResp2)
	require.Len(t, pollResp2.Updates, 2, "Should return 2 updates when last_processed_sequence_id = second update's sequence ID")
	require.Equal(t, pendingUpdates[2].SequenceId, pollResp2.Updates[0].SequenceId, "First returned update should have sequence ID matching third update")
	require.Equal(t, pendingUpdates[3].SequenceId, pollResp2.Updates[1].SequenceId, "Second returned update should have sequence ID matching fourth update")

	// Test 3: Poll with last_processed_sequence_id = fourth update's sequence ID (should get no updates)
	pollReq3 := &remoteagentsproto.WorkspacePollUpdateRequest{
		RemoteAgentId:           testParameters.AgentID,
		LastProcessedSequenceId: pendingUpdates[3].SequenceId,
	}
	pollResp3, err := tse.server.WorkspacePollUpdate(ctx, pollReq3)
	require.NoError(t, err)
	require.NotNil(t, pollResp3)
	require.Empty(t, pollResp3.Updates, "Should return no updates when last_processed_sequence_id = 4")
}

// TestErrorWrap tests that we can wrap errors with the retriable error and still get the correct status code
func TestErrorWrap(t *testing.T) {
	err := status.Errorf(codes.AlreadyExists, "already exists err")
	err = fmt.Errorf("test retryable error %w, %w", retriableErr, err)
	require.True(t, errors.Is(err, retriableErr), "Error should be retriable")
	st, ok := status.FromError(err)
	require.True(t, ok, "Error should be a gRPC status error")
	require.Equal(t, codes.AlreadyExists, st.Code(), "Error should be AlreadyExists")
}

// TestAgentEntityDestroy tests that the AgentEntity is destroyed after a successful save
func TestAgentEntityDestroy(t *testing.T) {
	// First, create an agent in BigTable
	testParameters := setupTestParameters()
	agentConfig := createTestAgentConfig()
	err := writeRemoteAgentAtCreation(
		context.Background(),
		testParameters.RequestCtx,
		realBigtableProxyClient,
		testParameters.TenantID,
		testParameters.UserID,
		testParameters.AgentID,
		agentConfig,
		"test-namespace",
	)
	require.NoError(t, err, "Failed to write agent config to BigTable")

	// Read the agent entity
	agentEntity, err := readRemoteAgentEntity(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.AgentID)
	require.NoError(t, err, "Failed to read agent entity")
	require.NotNil(t, agentEntity, "Agent entity should not be nil")

	// Verify that the internal fields are set
	require.NotNil(t, agentEntity.configInStorage, "configInStorage should not be nil before save")
	require.NotNil(t, agentEntity.statusInStorage, "statusInStorage should not be nil before save")
	require.NotNil(t, agentEntity.statusCell, "statusCell should not be nil before save")

	// Update the status
	agentEntity.Status.Status = remoteagentsproto.AgentStatus_AGENT_STATUS_RUNNING

	// Save the agent entity
	err = agentEntity.AtomicSave(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID)
	require.NoError(t, err, "Failed to save agent entity")

	// Verify that the internal fields are nil after save
	require.Nil(t, agentEntity.configInStorage, "configInStorage should be nil after save")
	require.Nil(t, agentEntity.statusInStorage, "statusInStorage should be nil after save")
	require.Nil(t, agentEntity.statusCell, "statusCell should be nil after save")

	// Try to save the agent entity again, which should fail
	agentEntity.Status.Status = remoteagentsproto.AgentStatus_AGENT_STATUS_PAUSED
	err = agentEntity.AtomicSave(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID)
	require.Error(t, err, "Saving a destroyed agent entity should fail")
	require.Contains(t, err.Error(), "status cell is not set, likely this object is already saved", "Error message should indicate that the entity was modified concurrently")
}

func TestAddSSHKey(t *testing.T) {
	tse := setupTestServerWithEmulator(t)
	testParameters := setupTestParameters()

	agentConfig := createTestAgentConfig()

	err := writeRemoteAgentAtCreation(
		context.Background(),
		testParameters.RequestCtx,
		realBigtableProxyClient,
		testParameters.TenantID,
		testParameters.UserID,
		testParameters.AgentID,
		agentConfig,
		"test-namespace",
	)
	require.NoError(t, err, "Failed to write agent config to BigTable")

	// Create a request with the agent ID and some test SSH keys
	testPublicKeys := []string{
		"ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAAAgQCp+Z6Oc2A5R1HMf4U5KnglMCBjIw1Cs1JEcSph4HOhS2nrWLlI0IkwwWyG3HvS/h0nnxbq7y2zp6LyiaAw10nOhOaKwBJvafqT/KpRSJLr911+FmVKST8s6Xz+/AuD9PBLBZySI/bPDj1859ZLtP1cw13v5kBatjei3/VddpBLBQ== test-rsa-key",
		"ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAINCeWd8vjexVmKWp5OxztQUF6aMfT4EnqU/icjOnDB2h test-ed25519-key",
	}
	req := &remoteagentsproto.AddSSHKeyRequest{
		RemoteAgentId: testParameters.AgentID,
		PublicKeys:    testPublicKeys,
	}

	// Create a context with metadata
	ctx := requestcontext.NewIncomingContext(testParameters.Ctx, testParameters.RequestCtx)

	// Expect GetWorkspace
	mockWorkspace := agentsmock.NewMockRemoteWorkspace(tse.ctrl)
	tse.mockRWC.EXPECT().
		GetWorkspace(gomock.Any(), testParameters.UserID, testParameters.AgentID).
		Return(mockWorkspace, nil).Times(3)
	mockWorkspace.EXPECT().
		AppendSSHKeys(gomock.Any(), gomock.Any()).
		Return([]byte{}, nil).Times(3)
	mockWorkspace.EXPECT().
		SSHConfig(gomock.Any()).
		Return(&remoteagentsproto.AgentSSHConfig{Hostname: "test-hostname"}, nil).Times(3)

	// Fail the health check once, then pass it
	mockWorkspace.EXPECT().
		HealthCheck(gomock.Any()).
		Return("", false, errors.New("test error")).Times(1)
	mockWorkspace.EXPECT().
		HealthCheck(gomock.Any()).
		Return("test-status", false, nil).Times(3)

	// Call the AddSSHKey method
	resp, err := tse.server.AddSSHKey(ctx, req)

	// Verify the response
	require.NoError(t, err)
	require.NotNil(t, resp)
	require.NotNil(t, resp.SshConfig)

	// Verify the public keys were added
	require.GreaterOrEqual(t, len(resp.SshConfig.PublicKeys), len(testPublicKeys))

	// Verify at least our test keys are in the response
	for _, key := range testPublicKeys {
		found := false
		for _, respKey := range resp.SshConfig.PublicKeys {
			if respKey == key {
				found = true
				break
			}
		}
		require.True(t, found, "Public key should be in the response")
	}

	// Add another key and verify we see all three now
	anotherKey := "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIGwm9LiSoXtMcumoFEbftyHsk8C6iR2dC59XFqEoHeKi test-ed25519-key2"
	req.PublicKeys = []string{anotherKey}
	testPublicKeys = append(testPublicKeys, anotherKey)
	resp, err = tse.server.AddSSHKey(ctx, req)
	require.NoError(t, err)
	require.NotNil(t, resp)
	require.NotNil(t, resp.SshConfig)
	t.Log(resp.SshConfig.PublicKeys)
	require.Equal(t, len(testPublicKeys), len(resp.SshConfig.PublicKeys))

	// Adding one of the original keys should do nothing, even with a different comment
	duplicateKey := "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIGwm9LiSoXtMcumoFEbftyHsk8C6iR2dC59XFqEoHeKi test-ed25519-key3"
	req.PublicKeys = []string{duplicateKey}
	resp, err = tse.server.AddSSHKey(ctx, req)
	require.NoError(t, err)
	require.NotNil(t, resp)
	require.NotNil(t, resp.SshConfig)
	require.Equal(t, len(testPublicKeys), len(resp.SshConfig.PublicKeys))
}

// TestWorkspaceReportSetupLogs_HappyPath tests the WorkspaceReportSetupLogs method in a happy path scenario
func TestWorkspaceReportSetupLogs_HappyPath(t *testing.T) {
	// First, create an agent in BigTable
	testParameters := setupTestParameters()
	agentConfig := createTestAgentConfig()
	err := writeRemoteAgentAtCreation(
		context.Background(),
		testParameters.RequestCtx,
		realBigtableProxyClient,
		testParameters.TenantID,
		testParameters.UserID,
		testParameters.AgentID,
		agentConfig,
		"test-namespace",
	)
	require.NoError(t, err, "Failed to write agent config to BigTable")

	// Set up the server
	tse := setupTestServerWithEmulator(t)

	// Mock the GetWorkspace call to return nil, error
	// This is fine since we're just testing the logs functionality
	tse.mockRWC.EXPECT().
		GetWorkspace(gomock.Any(), testParameters.UserID, testParameters.AgentID).
		Return(nil, fmt.Errorf("workspace not found")).
		AnyTimes()

	// Create a context with metadata
	ctx := requestcontext.NewIncomingContext(testParameters.Ctx, testParameters.RequestCtx)

	// Create a request with setup logs
	step1 := &remoteagentsproto.WorkspaceSetupStep{
		StepDescription: "Cloning repository",
		Logs:            "git clone https://github.com/example/repo.git\nCloning into 'repo'...",
		Status:          remoteagentsproto.WorkspaceSetupStepStatus_WORKSPACE_SETUP_STEP_STATUS_RUNNING,
		SequenceId:      1,
		StepNumber:      0,
	}

	step2 := &remoteagentsproto.WorkspaceSetupStep{
		StepDescription: "Installing dependencies",
		Logs:            "npm install\nAdded 1000 packages in 5s",
		Status:          remoteagentsproto.WorkspaceSetupStepStatus_WORKSPACE_SETUP_STEP_STATUS_SUCCESS,
		SequenceId:      2,
		StepNumber:      1,
	}

	req := &remoteagentsproto.WorkspaceReportSetupLogsRequest{
		RemoteAgentId: testParameters.AgentID,
		WorkspaceSetupStatus: &remoteagentsproto.WorkspaceSetupStatus{
			Steps: []*remoteagentsproto.WorkspaceSetupStep{step1, step2},
		},
	}

	// Call the WorkspaceReportSetupLogs method
	resp, err := tse.server.WorkspaceReportSetupLogs(ctx, req)

	// Verify the response
	require.NoError(t, err)
	require.NotNil(t, resp)

	// Now verify that the logs were written to BigTable by reading them back
	// using the WorkspaceLogs method
	logsReq := &remoteagentsproto.WorkspaceLogsRequest{
		RemoteAgentId: testParameters.AgentID,
	}

	logsResp, err := tse.server.WorkspaceLogs(ctx, logsReq)
	t.Logf("Logs response: %+v", logsResp)
	// Verify the logs response
	require.NoError(t, err)
	require.NotNil(t, logsResp)
	require.Equal(t, testParameters.AgentID, logsResp.RemoteAgentId)
	require.NotNil(t, logsResp.WorkspaceSetupStatus)
	require.Len(t, logsResp.WorkspaceSetupStatus.Steps, 2)

	// Verify the steps match what we sent
	require.Equal(t, step1.StepDescription, logsResp.WorkspaceSetupStatus.Steps[0].StepDescription)
	require.Equal(t, step1.Logs, logsResp.WorkspaceSetupStatus.Steps[0].Logs)
	require.Equal(t, step1.Status, logsResp.WorkspaceSetupStatus.Steps[0].Status)
	require.Equal(t, step1.SequenceId, logsResp.WorkspaceSetupStatus.Steps[0].SequenceId)
	require.Equal(t, step1.StepNumber, logsResp.WorkspaceSetupStatus.Steps[0].StepNumber)

	require.Equal(t, step2.StepDescription, logsResp.WorkspaceSetupStatus.Steps[1].StepDescription)
	require.Equal(t, step2.Logs, logsResp.WorkspaceSetupStatus.Steps[1].Logs)
	require.Equal(t, step2.Status, logsResp.WorkspaceSetupStatus.Steps[1].Status)
	require.Equal(t, step2.SequenceId, logsResp.WorkspaceSetupStatus.Steps[1].SequenceId)
	require.Equal(t, step2.StepNumber, logsResp.WorkspaceSetupStatus.Steps[1].StepNumber)
}

// TestWorkspaceLogs_Pagination tests the WorkspaceLogs method with pagination
func TestWorkspaceLogs_Pagination(t *testing.T) {
	// First, create an agent in BigTable
	testParameters := setupTestParameters()
	agentConfig := createTestAgentConfig()
	err := writeRemoteAgentAtCreation(
		context.Background(),
		testParameters.RequestCtx,
		realBigtableProxyClient,
		testParameters.TenantID,
		testParameters.UserID,
		testParameters.AgentID,
		agentConfig,
		"test-namespace",
	)
	require.NoError(t, err, "Failed to write agent config to BigTable")

	// Set up the server
	tse := setupTestServerWithEmulator(t)

	// Create a context with metadata
	ctx := requestcontext.NewIncomingContext(testParameters.Ctx, testParameters.RequestCtx)

	// Mock the GetWorkspace call to return nil, error
	// This is fine since we're just testing the logs functionality
	tse.mockRWC.EXPECT().
		GetWorkspace(gomock.Any(), testParameters.UserID, testParameters.AgentID).
		Return(nil, fmt.Errorf("workspace not found")).
		AnyTimes()

	// Create multiple steps with different sequence IDs and step numbers
	steps := []*remoteagentsproto.WorkspaceSetupStep{
		{
			StepDescription: "Step 0",
			Logs:            "Initial logs for step 0\n",
			Status:          remoteagentsproto.WorkspaceSetupStepStatus_WORKSPACE_SETUP_STEP_STATUS_RUNNING,
			SequenceId:      1,
			StepNumber:      0,
		},
		{
			StepDescription: "Step 0",
			Logs:            "More logs for step 0",
			Status:          remoteagentsproto.WorkspaceSetupStepStatus_WORKSPACE_SETUP_STEP_STATUS_RUNNING,
			SequenceId:      2,
			StepNumber:      0,
		},
		{
			StepDescription: "Step 0",
			Logs:            "Final logs for step 0\n",
			Status:          remoteagentsproto.WorkspaceSetupStepStatus_WORKSPACE_SETUP_STEP_STATUS_SUCCESS,
			SequenceId:      4,
			StepNumber:      0,
		},
		{
			StepDescription: "Step 1",
			Logs:            "Initial logs for step 1\n",
			Status:          remoteagentsproto.WorkspaceSetupStepStatus_WORKSPACE_SETUP_STEP_STATUS_RUNNING,
			SequenceId:      3,
			StepNumber:      1,
		},
		{
			StepDescription: "Step 1",
			Logs:            "Final logs for step 1",
			Status:          remoteagentsproto.WorkspaceSetupStepStatus_WORKSPACE_SETUP_STEP_STATUS_SUCCESS,
			SequenceId:      5,
			StepNumber:      1,
		},
	}

	// Report the logs in multiple calls
	// First report: steps 0 and 1
	req1 := &remoteagentsproto.WorkspaceReportSetupLogsRequest{
		RemoteAgentId: testParameters.AgentID,
		WorkspaceSetupStatus: &remoteagentsproto.WorkspaceSetupStatus{
			Steps: []*remoteagentsproto.WorkspaceSetupStep{steps[0], steps[1]},
		},
	}
	t.Logf("Reporting logs: %+v", req1)
	resp1, err := tse.server.WorkspaceReportSetupLogs(ctx, req1)
	require.NoError(t, err)
	require.NotNil(t, resp1)

	// Second report: step 2
	req2 := &remoteagentsproto.WorkspaceReportSetupLogsRequest{
		RemoteAgentId: testParameters.AgentID,
		WorkspaceSetupStatus: &remoteagentsproto.WorkspaceSetupStatus{
			Steps: []*remoteagentsproto.WorkspaceSetupStep{steps[2]},
		},
	}
	resp2, err := tse.server.WorkspaceReportSetupLogs(ctx, req2)
	require.NoError(t, err)
	require.NotNil(t, resp2)

	// Third report: steps 3 and 4
	req3 := &remoteagentsproto.WorkspaceReportSetupLogsRequest{
		RemoteAgentId: testParameters.AgentID,
		WorkspaceSetupStatus: &remoteagentsproto.WorkspaceSetupStatus{
			Steps: []*remoteagentsproto.WorkspaceSetupStep{steps[3], steps[4]},
		},
	}
	resp3, err := tse.server.WorkspaceReportSetupLogs(ctx, req3)
	require.NoError(t, err)
	require.NotNil(t, resp3)

	// Test 1: Get all logs
	logsReq1 := &remoteagentsproto.WorkspaceLogsRequest{
		RemoteAgentId: testParameters.AgentID,
	}
	logsResp1, err := tse.server.WorkspaceLogs(ctx, logsReq1)
	require.NoError(t, err)
	require.NotNil(t, logsResp1)
	require.Equal(t, testParameters.AgentID, logsResp1.RemoteAgentId)
	require.NotNil(t, logsResp1.WorkspaceSetupStatus)

	// The logs response should contain 5 steps
	t.Logf("Got logs: %v", logsResp1.WorkspaceSetupStatus.Steps)
	require.Len(t, logsResp1.WorkspaceSetupStatus.Steps, 5)

	// Test 2: Get logs after step
	offset := uint32(1)
	lastProcessedStep := uint32(1 + offset) // offset by 1
	logsReq2 := &remoteagentsproto.WorkspaceLogsRequest{
		RemoteAgentId:           testParameters.AgentID,
		LastProcessedStep:       &lastProcessedStep,
		LastProcessedSequenceId: nil, // Should default to 0
	}
	logsResp2, err := tse.server.WorkspaceLogs(ctx, logsReq2)
	require.NoError(t, err)
	require.NotNil(t, logsResp2)
	require.Len(t, logsResp2.WorkspaceSetupStatus.Steps, 2) // Should only include the last two

	// Test 3: Get logs after sequence ID 3
	lastProcessedSequenceId := uint32(3)
	logsReq3 := &remoteagentsproto.WorkspaceLogsRequest{
		RemoteAgentId:           testParameters.AgentID,
		LastProcessedStep:       &offset, // Should default to 0
		LastProcessedSequenceId: &lastProcessedSequenceId,
	}
	logsResp3, err := tse.server.WorkspaceLogs(ctx, logsReq3)
	require.NoError(t, err)
	require.NotNil(t, logsResp3)
	t.Logf("Got logs after sequence ID 3: %v", logsResp3.WorkspaceSetupStatus.Steps)
	require.Len(t, logsResp3.WorkspaceSetupStatus.Steps, 3) // The last three from above should return

	// Verify that the steps have the expected sequence IDs
	// Instead of checking exact content, just verify the sequence IDs are correct
	for _, step := range logsResp3.WorkspaceSetupStatus.Steps {
		require.GreaterOrEqual(t, step.SequenceId, lastProcessedSequenceId, "All steps should have sequence ID >= 3")
	}
}

// TestWorkspaceLogs_ContainerSetupStep tests the WorkspaceLogs method when there are no logs
// and it needs to add the container setup step
func TestWorkspaceLogs_ContainerSetupStep(t *testing.T) {
	// First, create an agent in BigTable
	testParameters := setupTestParameters()
	agentConfig := createTestAgentConfig()
	err := writeRemoteAgentAtCreation(
		context.Background(),
		testParameters.RequestCtx,
		realBigtableProxyClient,
		testParameters.TenantID,
		testParameters.UserID,
		testParameters.AgentID,
		agentConfig,
		"test-namespace",
	)
	require.NoError(t, err, "Failed to write agent config to BigTable")

	// Set up the controller for mocks
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Create a context with metadata
	ctx := requestcontext.NewIncomingContext(testParameters.Ctx, testParameters.RequestCtx)

	// Test different pod statuses
	testCases := []struct {
		podStatus  string
		expectLogs string
	}{
		{"", "Starting ..."},
		{"Pending", "Allocating resources ..."},
		{"Running", "Setting up workspace ..."},
		{"Failed", "Failed"},
		{"Unknown", "Unknown"},
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("PodStatus_%s", tc.podStatus), func(t *testing.T) {
			// Create a new mock controller and workspace for each test case
			subCtrl := gomock.NewController(t)
			mockSubWorkspace := wsmock.NewMockRemoteWorkspace(subCtrl)
			mockSubRWC := wsmock.NewMockRemoteWorkspaceController(subCtrl)

			// Set up the server with the new mocks
			var memstoreClient memstoreclient.MemstoreClient = nil
			subServer, err := NewRemoteAgentsServer(mockSubRWC, realBigtableProxyClient, nil, ripublisher.NewRequestInsightPublisherMock(), featureflags.NewLocalFeatureFlagHandler(), false, "test-ri-topic", memstoreClient)
			require.NoError(t, err)

			// Mock the GetWorkspace call to return our mock workspace
			mockSubRWC.EXPECT().
				GetWorkspace(gomock.Any(), testParameters.UserID, testParameters.AgentID).
				Return(mockSubWorkspace, nil)

			// Mock the PodStatus method to return the test case status
			mockSubWorkspace.EXPECT().
				PodStatus(gomock.Any()).
				Return(tc.podStatus)

			// Call the WorkspaceLogs method
			logsReq := &remoteagentsproto.WorkspaceLogsRequest{
				RemoteAgentId: testParameters.AgentID,
			}
			logsResp, err := subServer.WorkspaceLogs(ctx, logsReq)

			// Verify the response
			require.NoError(t, err)
			require.NotNil(t, logsResp)
			require.Equal(t, testParameters.AgentID, logsResp.RemoteAgentId)
			require.NotNil(t, logsResp.WorkspaceSetupStatus)

			// Should have exactly one step (the container setup step)
			require.Len(t, logsResp.WorkspaceSetupStatus.Steps, 1)

			// Verify the container setup step
			containerStep := logsResp.WorkspaceSetupStatus.Steps[0]
			require.Equal(t, "Container Setup Status", containerStep.StepDescription)
			require.Equal(t, tc.expectLogs, containerStep.Logs)
			require.Equal(t, remoteagentsproto.WorkspaceSetupStepStatus_WORKSPACE_SETUP_STEP_STATUS_RUNNING, containerStep.Status)
			require.Equal(t, uint32(0), containerStep.SequenceId)
			require.Equal(t, uint32(0), containerStep.StepNumber)
		})
	}

	// Test error case when GetWorkspace fails
	t.Run("GetWorkspace_Error", func(t *testing.T) {
		// Create a new mock controller and workspace controller for this test
		subCtrl := gomock.NewController(t)
		mockSubRWC := wsmock.NewMockRemoteWorkspaceController(subCtrl)

		// Set up the server with the new mocks
		var memstoreClient memstoreclient.MemstoreClient = nil
		subServer, err := NewRemoteAgentsServer(mockSubRWC, realBigtableProxyClient, nil, ripublisher.NewRequestInsightPublisherMock(), featureflags.NewLocalFeatureFlagHandler(), false, "test-ri-topic", memstoreClient)
		require.NoError(t, err)

		// Mock the GetWorkspace call to return an error
		mockSubRWC.EXPECT().
			GetWorkspace(gomock.Any(), testParameters.UserID, testParameters.AgentID).
			Return(nil, fmt.Errorf("workspace not found"))

		// Call the WorkspaceLogs method
		logsReq := &remoteagentsproto.WorkspaceLogsRequest{
			RemoteAgentId: testParameters.AgentID,
		}
		logsResp, err := subServer.WorkspaceLogs(ctx, logsReq)

		// Verify the response
		require.NoError(t, err)
		require.NotNil(t, logsResp)
		require.Equal(t, testParameters.AgentID, logsResp.RemoteAgentId)
		require.NotNil(t, logsResp.WorkspaceSetupStatus)
		containerStep := logsResp.WorkspaceSetupStatus.Steps[0]
		require.Equal(t, "Container Setup Status", containerStep.StepDescription)
		require.Equal(t, "Starting ...", containerStep.Logs)
		require.Equal(t, remoteagentsproto.WorkspaceSetupStepStatus_WORKSPACE_SETUP_STEP_STATUS_RUNNING, containerStep.Status)
		require.Equal(t, uint32(0), containerStep.SequenceId)
		require.Equal(t, uint32(0), containerStep.StepNumber)

		// Should have exactly one step (the container setup step)
		require.Len(t, logsResp.WorkspaceSetupStatus.Steps, 1)
	})
}

// TestReadChatHistory tests the readChatHistory function with various pagination scenarios
func TestReadChatHistory(t *testing.T) {
	// Setup test parameters
	testParameters := setupTestParameters()
	agentID := testParameters.AgentID
	tenantID := testParameters.TenantID

	// Create test exchanges with different sequence IDs
	exchanges := createTestExchanges(5)

	// Write the exchanges to BigTable
	err := writeChatHistoryChunk(
		context.Background(),
		testParameters.RequestCtx,
		realBigtableProxyClient,
		tenantID,
		testParameters.UserID,
		agentID,
		exchanges,
	)
	require.NoError(t, err, "Failed to write chat history to BigTable")

	// When pageSize is 0, all exchanges should be returned
	t.Run("Backward compatible case (pageSize=0)", func(t *testing.T) {
		result, err := readChatHistory(
			context.Background(),
			testParameters.RequestCtx,
			realBigtableProxyClient,
			tenantID,
			agentID,
			0, // pageSize = 0 (backward compatibility)
			0, // lastProcessedSequenceId = 0
		)
		require.NoError(t, err, "Failed to read chat history")
		require.Len(t, result, 5, "Should return all 5 exchanges")

		// Verify the exchanges are sorted by sequence ID
		for i := 0; i < len(result)-1; i++ {
			require.Less(t, result[i].SequenceId, result[i+1].SequenceId,
				"Exchanges should be sorted by sequence ID")
		}
	})

	t.Run("Page size less than total items", func(t *testing.T) {
		// When pageSize is less than the total number of items,
		// only pageSize items should be returned
		pageSize := uint32(3)
		lastProcessedSeqID := uint32(0)

		result, err := readChatHistory(
			context.Background(),
			testParameters.RequestCtx,
			realBigtableProxyClient,
			tenantID,
			agentID,
			pageSize,
			lastProcessedSeqID,
		)
		require.NoError(t, err, "Failed to read chat history")
		require.Len(t, result, 3, "Should return exactly 3 exchanges")

		// Verify we got the first 3 exchanges by sequence ID
		require.Equal(t, uint32(1), result[0].SequenceId, "First exchange should have sequence ID 1")
		require.Equal(t, uint32(2), result[1].SequenceId, "Second exchange should have sequence ID 2")
		require.Equal(t, uint32(3), result[2].SequenceId, "Third exchange should have sequence ID 3")
	})

	t.Run("Page size greater than total items", func(t *testing.T) {
		// When pageSize is greater than the total number of items,
		// all items should be returned
		pageSize := uint32(10)
		lastProcessedSeqID := uint32(0)

		result, err := readChatHistory(
			context.Background(),
			testParameters.RequestCtx,
			realBigtableProxyClient,
			tenantID,
			agentID,
			pageSize,
			lastProcessedSeqID,
		)
		require.NoError(t, err, "Failed to read chat history")
		require.Len(t, result, 5, "Should return all 5 exchanges")
	})

	t.Run("Last sequence ID filtering", func(t *testing.T) {
		// When lastProcessedSequenceId is specified, only exchanges with
		// sequence ID > lastProcessedSequenceId should be returned
		pageSize := uint32(10)
		lastProcessedSeqID := uint32(2)

		result, err := readChatHistory(
			context.Background(),
			testParameters.RequestCtx,
			realBigtableProxyClient,
			tenantID,
			agentID,
			pageSize,
			lastProcessedSeqID,
		)
		require.NoError(t, err, "Failed to read chat history")
		require.Len(t, result, 3, "Should return 3 exchanges (with sequence IDs 3, 4, and 5)")

		// Verify all returned exchanges have sequence ID > lastProcessedSequenceId
		for _, exchange := range result {
			require.Greater(t, exchange.SequenceId, lastProcessedSeqID,
				"All returned exchanges should have sequence ID > lastProcessedSequenceId")
		}
	})

	t.Run("Last sequence ID greater than all items", func(t *testing.T) {
		// When lastProcessedSequenceId is greater than all sequence IDs,
		// no exchanges should be returned
		pageSize := uint32(10)
		lastProcessedSeqID := uint32(10) // Greater than all sequence IDs

		result, err := readChatHistory(
			context.Background(),
			testParameters.RequestCtx,
			realBigtableProxyClient,
			tenantID,
			agentID,
			pageSize,
			lastProcessedSeqID,
		)
		require.NoError(t, err, "Failed to read chat history")
		require.Len(t, result, 0, "Should return 0 exchanges")
	})

	t.Run("Pagination with multiple pages", func(t *testing.T) {
		// Test pagination by fetching exchanges in multiple pages
		pageSize := uint32(2)
		lastProcessedSeqID := uint32(0)

		// First page
		result1, err := readChatHistory(
			context.Background(),
			testParameters.RequestCtx,
			realBigtableProxyClient,
			tenantID,
			agentID,
			pageSize,
			lastProcessedSeqID,
		)
		require.NoError(t, err, "Failed to read first page")
		require.Len(t, result1, 2, "First page should have 2 exchanges")

		// Update lastProcessedSeqID to the last sequence ID from the first page
		lastProcessedSeqID = result1[len(result1)-1].SequenceId

		// Second page
		result2, err := readChatHistory(
			context.Background(),
			testParameters.RequestCtx,
			realBigtableProxyClient,
			tenantID,
			agentID,
			pageSize,
			lastProcessedSeqID,
		)
		require.NoError(t, err, "Failed to read second page")
		require.Len(t, result2, 2, "Second page should have 2 exchanges")

		// Update lastProcessedSeqID to the last sequence ID from the second page
		lastProcessedSeqID = result2[len(result2)-1].SequenceId

		// Third page
		result3, err := readChatHistory(
			context.Background(),
			testParameters.RequestCtx,
			realBigtableProxyClient,
			tenantID,
			agentID,
			pageSize,
			lastProcessedSeqID,
		)
		require.NoError(t, err, "Failed to read third page")
		require.Len(t, result3, 1, "Third page should have 1 exchange")

		// Verify we got all exchanges without duplicates
		allExchanges := append(append(result1, result2...), result3...)
		require.Len(t, allExchanges, 5, "Total should be 5 exchanges across all pages")

		// Verify the sequence IDs are in order and unique
		seqIDs := make(map[uint32]bool)
		for _, exchange := range allExchanges {
			seqIDs[exchange.SequenceId] = true
		}
		require.Len(t, seqIDs, 5, "Should have 5 unique sequence IDs")
	})
}

// createTestExchanges creates a set of test exchanges with different sequence IDs
// numExchanges specifies how many exchanges to create, with sequence IDs starting from 1
func createTestExchanges(numExchanges int) []*remoteagentsproto.ChatHistoryExchange {
	exchanges := make([]*remoteagentsproto.ChatHistoryExchange, numExchanges)

	// Create numExchanges exchanges with sequence IDs 1 to numExchanges
	for i := 0; i < numExchanges; i++ {
		seqID := uint32(i + 1)
		requestMessage := "Test request " + string(rune('A'+i))
		responseText := "Test response " + string(rune('A'+i))
		requestId := "request-id-" + string(rune('A'+i))

		exchanges[i] = &remoteagentsproto.ChatHistoryExchange{
			Exchange: &chatproto.Exchange{
				RequestMessage: requestMessage,
				ResponseText:   &responseText,
				RequestId:      &requestId,
			},
			SequenceId: seqID,
			FinishedAt: timestamppb.Now(), // Add timestamp
		}
	}

	return exchanges
}

// TestHasUpdatesField tests that the has_updates field is properly set and cleared
func TestHasUpdatesField(t *testing.T) {
	// Mock the time function to make the test deterministic
	fixedTime := time.Date(2023, 12, 25, 10, 30, 0, 0, time.UTC)
	originalNowFunc := nowFunc
	nowFunc = func() time.Time { return fixedTime }
	defer func() { nowFunc = originalNowFunc }()

	// First, create an agent in BigTable
	testParameters := setupTestParameters()
	agentConfig := createTestAgentConfig()
	err := writeRemoteAgentAtCreation(
		context.Background(),
		testParameters.RequestCtx,
		realBigtableProxyClient,
		testParameters.TenantID,
		testParameters.UserID,
		testParameters.AgentID,
		agentConfig,
		"test-namespace",
	)
	require.NoError(t, err, "Failed to write agent config to BigTable")

	// Set up the server
	tse := setupTestServerWithEmulator(t)

	// Create a context with metadata
	ctx := requestcontext.NewIncomingContext(testParameters.Ctx, testParameters.RequestCtx)

	// Step 1: Verify that has_updates is initially false or nil
	agent, err := readRemoteAgent(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.UserID, testParameters.AgentID, 30)
	require.NoError(t, err, "Failed to read agent from BigTable")
	// Either has_updates is nil or it's false
	hasUpdates := agent.HasUpdates != nil && *agent.HasUpdates
	require.False(t, hasUpdates, "has_updates should initially be false or nil")

	// Step 2: Report status to set has_updates to true
	statusReq := &remoteagentsproto.WorkspaceReportStatusRequest{
		RemoteAgentId: testParameters.AgentID,
		Status:        remoteagentsproto.AgentStatus_AGENT_STATUS_RUNNING,
	}
	_, err = tse.server.WorkspaceReportStatus(ctx, statusReq)
	require.NoError(t, err, "Failed to report status")

	// Verify that has_updates is now true
	agent, err = readRemoteAgent(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.UserID, testParameters.AgentID, 30)
	require.NoError(t, err, "Failed to read agent from BigTable")
	hasUpdates = agent.HasUpdates != nil && *agent.HasUpdates
	require.True(t, hasUpdates, "has_updates should be true after reporting status")

	// Step 3: Call ChatHistory to set has_updates to false
	chatHistoryReq := &remoteagentsproto.ChatHistoryRequest{
		RemoteAgentId: testParameters.AgentID,
	}
	_, err = tse.server.ChatHistory(ctx, chatHistoryReq)
	require.NoError(t, err, "Failed to get chat history")

	// Verify that has_updates is now false
	agent, err = readRemoteAgent(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.UserID, testParameters.AgentID, 30)
	require.NoError(t, err, "Failed to read agent from BigTable")
	hasUpdates = agent.HasUpdates != nil && *agent.HasUpdates
	require.False(t, hasUpdates, "has_updates should be false after viewing chat history")

	// Step 4: Report chat history to set has_updates to true
	responseText := "Test response"
	requestId := "request-id-1"
	exchange := &chatproto.Exchange{
		RequestMessage: "Test request",
		ResponseText:   &responseText,
		RequestId:      &requestId,
	}
	chatHistoryExchange := &remoteagentsproto.ChatHistoryExchange{
		Exchange:   exchange,
		SequenceId: 1,
		FinishedAt: timestamppb.Now(),
	}
	t.Logf("TestHasUpdatesField: chatHistoryExchange being sent: %+v, FinishedAt: %v", chatHistoryExchange, chatHistoryExchange.FinishedAt)
	reportChatReq := &remoteagentsproto.WorkspaceReportChatHistoryRequest{
		RemoteAgentId: testParameters.AgentID,
		ChatHistory:   []*remoteagentsproto.ChatHistoryExchange{chatHistoryExchange},
	}
	_, err = tse.server.WorkspaceReportChatHistory(ctx, reportChatReq)
	require.NoError(t, err, "Failed to report chat history")

	// Verify that has_updates is now true
	agent, err = readRemoteAgent(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.UserID, testParameters.AgentID, 30)
	require.NoError(t, err, "Failed to read agent from BigTable")
	hasUpdates = agent.HasUpdates != nil && *agent.HasUpdates
	require.True(t, hasUpdates, "has_updates should be true after reporting chat history")

	// Verify that last_agent_update_received_at timestamp was set
	agentEntity, err := readRemoteAgentEntity(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.AgentID)
	require.NoError(t, err, "Failed to read agent entity")
	require.NotNil(t, agentEntity.Status.LastAgentUpdateReceivedAt, "last_agent_update_received_at should be set")
	require.Equal(t, fixedTime.Unix(), agentEntity.Status.LastAgentUpdateReceivedAt.Seconds, "last_agent_update_received_at should match the fixed time")
}

func TestStripInstructionFlags(t *testing.T) {
	t.Parallel()
	tests := map[string]struct {
		inNodes   []*chatproto.ChatRequestNode
		wantNodes []*chatproto.ChatRequestNode
		wantFlags map[string]string
	}{
		"nil": {
			inNodes:   nil,
			wantFlags: map[string]string{},
		},
		"no-flags": {
			inNodes: []*chatproto.ChatRequestNode{
				{
					Type: chatproto.ChatRequestNodeType_TEXT,
					TextNode: &chatproto.ChatRequestText{
						Content: "no flags here",
					},
				},
			},
			wantNodes: []*chatproto.ChatRequestNode{
				{
					Type: chatproto.ChatRequestNodeType_TEXT,
					TextNode: &chatproto.ChatRequestText{
						Content: "no flags here",
					},
				},
			},
			wantFlags: map[string]string{},
		},
		"not-text": {
			inNodes: []*chatproto.ChatRequestNode{
				{
					Type: chatproto.ChatRequestNodeType_TOOL_RESULT,
					ToolResultNode: &chatproto.ChatRequestToolResult{
						Content: "ABCD _flags:{key1=bar1,key2=has space,key3,key4=123} EFG",
					},
				},
			},
			wantNodes: []*chatproto.ChatRequestNode{
				{
					Type: chatproto.ChatRequestNodeType_TOOL_RESULT,
					ToolResultNode: &chatproto.ChatRequestToolResult{
						Content: "ABCD _flags:{key1=bar1,key2=has space,key3,key4=123} EFG",
					},
				},
			},
			wantFlags: map[string]string{},
		},
		"flags": {
			inNodes: []*chatproto.ChatRequestNode{
				{
					Type: chatproto.ChatRequestNodeType_TEXT,
					TextNode: &chatproto.ChatRequestText{
						Content: "ABCD _flags:{key1=bar1,key2 = has spaces , key3,key4=123} EFG",
					},
				},
			},
			wantNodes: []*chatproto.ChatRequestNode{
				{
					Type: chatproto.ChatRequestNodeType_TEXT,
					TextNode: &chatproto.ChatRequestText{
						Content: "ABCD EFG",
					},
				},
			},
			wantFlags: map[string]string{
				"key1": "bar1",
				"key2": "has spaces",
				"key3": "true",
				"key4": "123",
			},
		},
		"multiple-nodes": {
			inNodes: []*chatproto.ChatRequestNode{
				{
					Type: chatproto.ChatRequestNodeType_TEXT,
					TextNode: &chatproto.ChatRequestText{
						Content: "ABCD _flags:{key1=bar1,key2 = has spaces , key3,key4=123} EFG",
					},
				},
				{
					Type: chatproto.ChatRequestNodeType_TEXT,
					TextNode: &chatproto.ChatRequestText{
						Content: "XYZ _flags:{key2 = has spaces2 , key3,key5=456} ZYX",
					},
				},
			},
			wantNodes: []*chatproto.ChatRequestNode{
				{
					Type: chatproto.ChatRequestNodeType_TEXT,
					TextNode: &chatproto.ChatRequestText{
						Content: "ABCD EFG",
					},
				},
				{
					Type: chatproto.ChatRequestNodeType_TEXT,
					TextNode: &chatproto.ChatRequestText{
						Content: "XYZ ZYX",
					},
				},
			},
			wantFlags: map[string]string{
				"key1": "bar1",
				"key2": "has spaces2",
				"key3": "true",
				"key4": "123",
				"key5": "456",
			},
		},
		"flags-first": {
			inNodes: []*chatproto.ChatRequestNode{
				{
					Type: chatproto.ChatRequestNodeType_TEXT,
					TextNode: &chatproto.ChatRequestText{
						Content: "_flags:{key1=bar1,key2 = has spaces , key3,key4=123} EFG",
					},
				},
			},
			wantNodes: []*chatproto.ChatRequestNode{
				{
					Type: chatproto.ChatRequestNodeType_TEXT,
					TextNode: &chatproto.ChatRequestText{
						Content: "EFG",
					},
				},
			},
			wantFlags: map[string]string{
				"key1": "bar1",
				"key2": "has spaces",
				"key3": "true",
				"key4": "123",
			},
		},
		"flags-last": {
			inNodes: []*chatproto.ChatRequestNode{
				{
					Type: chatproto.ChatRequestNodeType_TEXT,
					TextNode: &chatproto.ChatRequestText{
						Content: "ABCD _flags:{key1=bar1,key2 = has spaces , key3,key4=123}",
					},
				},
			},
			wantNodes: []*chatproto.ChatRequestNode{
				{
					Type: chatproto.ChatRequestNodeType_TEXT,
					TextNode: &chatproto.ChatRequestText{
						Content: "ABCD",
					},
				},
			},
			wantFlags: map[string]string{
				"key1": "bar1",
				"key2": "has spaces",
				"key3": "true",
				"key4": "123",
			},
		},
	}
	for tn, tc := range tests {
		t.Run(tn, func(t *testing.T) {
			t.Parallel()
			got := stripInstructionFlags(tc.inNodes...)
			if diff := cmp.Diff(tc.wantFlags, got); diff != "" {
				t.Errorf("flags -want +got:\n%s", diff)
			}
			if diff := cmp.Diff(tc.wantNodes, tc.inNodes, protocmp.Transform()); diff != "" {
				t.Errorf("nodes -want +got:\n%s", diff)
			}
		})
	}
}

func TestParseGithubURL(t *testing.T) {
	testCases := []struct {
		name          string
		url           string
		expectedOwner string
		expectedName  string
		expectedErr   bool
	}{
		{
			name:          "Standard URL",
			url:           "https://github.com/augmentcode/augment",
			expectedOwner: "augmentcode",
			expectedName:  "augment",
		},
		{
			name:          "Standard URL with www",
			url:           "https://www.github.com/augmentcode/augment",
			expectedOwner: "augmentcode",
			expectedName:  "augment",
		},
		{
			name:          "URL with trailing slash",
			url:           "https://github.com/augmentcode/augment/",
			expectedOwner: "augmentcode",
			expectedName:  "augment",
		},
		{
			name:          "URL with .git suffix",
			url:           "https://github.com/augmentcode/augment.git",
			expectedOwner: "augmentcode",
			expectedName:  "augment",
		},
		{
			name:        "Invalid URL",
			url:         "https://bitbucket.org/augmentcode/augment",
			expectedErr: true,
		},
		{
			name:        "Invalid URL",
			url:         "https://github.com/augmentcode",
			expectedErr: true,
		},
		{
			name:        "Invalid URL",
			url:         "https://github.com/augmentcode/augment/foo",
			expectedErr: true,
		},
		{
			name:        "git@ url",
			url:         "**************:augmentcode/augment.git",
			expectedErr: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			owner, name, err := parseGithubURL(tc.url)
			if tc.expectedErr {
				require.Error(t, err, "Expected an error, but got none")
				return
			}
			require.NoError(t, err, "Expected no error, but got one")
			require.Equal(t, tc.expectedOwner, owner, "Owner did not match")
			require.Equal(t, tc.expectedName, name, "Name did not match")
		})
	}
}

func TestDeleteAgent(t *testing.T) {
	// First, create an agent in BigTable
	testParameters := setupTestParameters()

	// Create the agent config in BigTable
	agentConfig := createTestAgentConfig()

	// Write the agent config to BigTable
	err := writeRemoteAgentAtCreation(
		context.Background(),
		testParameters.RequestCtx,
		realBigtableProxyClient,
		testParameters.TenantID,
		testParameters.UserID,
		testParameters.AgentID,
		agentConfig,
		"test-namespace",
	)
	fmt.Println("userID:", testParameters.UserID)
	require.NoError(t, err, "Failed to write agent config to BigTable")

	// Verify that the agent was written to BigTable by reading it back
	agent, err := readRemoteAgent(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.UserID, testParameters.AgentID, 30)
	require.NoError(t, err, "Failed to read agent from BigTable")
	require.NotNil(t, agent, "Agent should not be nil")
	require.Equal(t, testParameters.AgentID, agent.RemoteAgentId, "Agent ID should match")

	// Verify that the agent ID is in the user agent mapping
	userMappingFromBigTable, err := readUserAgentMapping(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.UserID)
	require.NoError(t, err, "Failed to read user agent mapping from BigTable")
	require.Contains(t, userMappingFromBigTable, testParameters.AgentID, "User agent mapping should contain the agent ID")

	// Write chat history and workspace log
	err = writeChatHistoryChunk(
		context.Background(),
		testParameters.RequestCtx,
		realBigtableProxyClient,
		testParameters.TenantID,
		testParameters.UserID,
		testParameters.AgentID,
		createTestExchanges(5),
	)
	require.NoError(t, err, "Failed to write chat history to BigTable")

	// Now delete the agent
	err = deleteRemoteAgent(
		context.Background(),
		testParameters.RequestCtx,
		realBigtableProxyClient,
		testParameters.TenantID,
		testParameters.UserID,
		testParameters.AgentID,
	)
	require.NoError(t, err, "Failed to delete agent from BigTable")

	// Verify that the agent is no longer in BigTable
	agent, err = readRemoteAgent(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.UserID, testParameters.AgentID, 30)
	require.Error(t, err, "Reading deleted agent should return an error")
	require.Nil(t, agent, "Deleted agent should be nil")

	// Verify that the agent ID is removed from the user agent mapping
	userMappingFromBigTable, err = readUserAgentMapping(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.UserID)
	require.NoError(t, err, "Failed to read user agent mapping from BigTable")
	require.NotContains(t, userMappingFromBigTable, testParameters.AgentID, "User agent mapping should not contain the deleted agent ID")

	// Verify that all rows related to the agent are deleted
	allRowsRange := &bigtableproto.RowSet{
		RowRanges: []*bigtableproto.RowRange{{
			StartKey: &bigtableproto.RowRange_StartKeyClosed{StartKeyClosed: []byte("\x00")},
			EndKey:   &bigtableproto.RowRange_EndKeyOpen{EndKeyOpen: []byte("\xFF")},
		}},
	}
	allRows, err := realBigtableProxyClient.ReadRows(
		context.Background(),
		testParameters.TenantID,
		proxyproto.TableName_REMOTE_AGENTS,
		allRowsRange,
		nil,
		0,
		testParameters.RequestCtx,
	)
	for _, row := range allRows {
		require.NotContains(t, string(row.RowKey), testParameters.AgentID, "Should not find agent ID in any row key")
	}
}

func TestAtomicAgentEntityUpdate_Concurrency(t *testing.T) {
	testParameters := setupTestParameters()
	initialConfig := createTestAgentConfig()

	// 1. Write initial state (STARTING status)
	err := writeRemoteAgentAtCreation(
		context.Background(),
		testParameters.RequestCtx,
		realBigtableProxyClient,
		testParameters.TenantID,
		testParameters.UserID,
		testParameters.AgentID,
		initialConfig,
		"test-namespace",
	)
	require.NoError(t, err, "Failed to write initial agent config to BigTable")

	// 2. Read initial entity to get the predicate value
	initialAgentEntity, err := readRemoteAgentEntity(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.AgentID)
	require.NoError(t, err, "Failed to read initial agent entity")
	require.NotNil(t, initialAgentEntity.Config, "Initial config proto should not be nil")
	require.NotNil(t, initialAgentEntity.Status, "Initial status proto should not be nil")
	require.Equal(t, remoteagentsproto.AgentStatus_AGENT_STATUS_STARTING, initialAgentEntity.Status.Status, "Initial status should be STARTING")

	// 3. Define the single update operation everyone will attempt
	updatedConfigProto := proto.Clone(initialAgentEntity.Config).(*entitiesproto.AgentConfig)
	newGuidelines := "Updated guidelines by concurrent test"
	updatedConfigProto.InputConfig.UserGuidelines = &newGuidelines
	updatedStatusProto := &entitiesproto.AgentStatus{Status: remoteagentsproto.AgentStatus_AGENT_STATUS_RUNNING}

	// Create a template agent entity for concurrent updates
	templateAgentEntity := &AgentEntity{
		Config:     updatedConfigProto,
		Status:     updatedStatusProto,
		AgentID:    testParameters.AgentID,
		statusCell: initialAgentEntity.statusCell, // All attempts use the initial predicate
	}

	// 4. Concurrent Execution
	numGoroutines := 20
	var wg sync.WaitGroup
	wg.Add(numGoroutines)

	type result struct {
		goroutineID int
		err         error
	}
	resultChan := make(chan result, numGoroutines)
	startGate := make(chan struct{})

	for i := 0; i < numGoroutines; i++ {
		go func(id int) {
			defer wg.Done()
			<-startGate // Wait for the signal to start
			// Each goroutine attempts the *same* update
			// Create a copy of the template agent entity to avoid concurrent modification
			agentEntityCopy := &AgentEntity{
				Config:          proto.Clone(templateAgentEntity.Config).(*entitiesproto.AgentConfig),
				Status:          proto.Clone(templateAgentEntity.Status).(*entitiesproto.AgentStatus),
				AgentID:         templateAgentEntity.AgentID,
				statusCell:      templateAgentEntity.statusCell,
				configInStorage: proto.Clone(templateAgentEntity.Config).(*entitiesproto.AgentConfig),
				statusInStorage: proto.Clone(templateAgentEntity.Status).(*entitiesproto.AgentStatus),
			}
			err := agentEntityCopy.AtomicSave(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID)
			resultChan <- result{goroutineID: id, err: err}
		}(i)
	}

	close(startGate) // Signal all goroutines to start
	wg.Wait()        // Wait for all goroutines to finish
	close(resultChan)

	// 5. Collect Results and Assertions
	successCount := 0
	failureCount := 0
	var errorsList []error

	for res := range resultChan {
		if res.err == nil {
			successCount++
		} else {
			failureCount++
			errorsList = append(errorsList, res.err)
		}
	}

	// require.Equal(t, 1, successCount, "Expected exactly one goroutine to succeed the atomic update")
	// require.Equal(t, numGoroutines-1, failureCount, "Expected all other goroutines to fail")
	require.GreaterOrEqual(t, successCount, 1, "Expected at least one goroutine to succeed the atomic update")
	require.LessOrEqual(t, failureCount, numGoroutines-1, "Expected at most all other goroutines to fail")

	// Verify all failures were due to predicate mismatch (Aborted)
	for _, failureErr := range errorsList {
		st, ok := status.FromError(failureErr)
		require.True(t, ok, "Failure error should be a gRPC status error")
		require.Equal(t, codes.Aborted, st.Code(), "Failure error code should be Aborted")
	}

	// 6. Final Verification: Check the state in Bigtable
	finalAgentEntity, err := readRemoteAgentEntity(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.AgentID)
	require.NoError(t, err, "Failed to read final agent entity")

	// Verify the final state matches the one successful update
	require.True(t, proto.Equal(templateAgentEntity.Config, finalAgentEntity.Config), "Final config should match the successfully updated config")
	require.True(t, proto.Equal(templateAgentEntity.Status, finalAgentEntity.Status), "Final status should match the successfully updated status (RUNNING)")
}

func TestResumeAgent_HappyPath(t *testing.T) {
	// First, create an agent in BigTable
	testParameters := setupTestParameters()
	agentConfig := createTestAgentConfig()
	err := writeRemoteAgentAtCreation(
		context.Background(),
		testParameters.RequestCtx,
		realBigtableProxyClient,
		testParameters.TenantID,
		testParameters.UserID,
		testParameters.AgentID,
		agentConfig,
		"test-namespace",
	)
	require.NoError(t, err, "Failed to write agent config to BigTable")

	// Set up the server with mocks
	tse := setupTestServerWithEmulator(t)

	// Set the agent status to PAUSED (following the correct state transition: RUNNING -> PAUSING -> PAUSED)
	agentEntity, err := readRemoteAgentEntity(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.AgentID)
	require.NoError(t, err, "Failed to read agent entity")

	// First transition from RUNNING to PAUSING (also set agent status to IDLE to pass validation)
	agentEntity.Status.WorkspaceStatus = remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSING
	agentEntity.Status.Status = remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE
	err = agentEntity.AtomicSave(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID)
	require.NoError(t, err, "Failed to update agent status to PAUSING")
	agentEntity, err = readRemoteAgentEntity(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.AgentID)
	require.NoError(t, err, "Failed to read agent entity after PAUSING")
	agentEntity.Status.WorkspaceStatus = remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSED
	err = agentEntity.AtomicSave(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID)
	require.NoError(t, err, "Failed to update agent status to PAUSED")

	// Expect ResumeWorkspace to be called with the correct parameters
	tse.mockRWC.EXPECT().
		ResumeWorkspace(gomock.Any(), testParameters.UserID, testParameters.AgentID).
		DoAndReturn(func(_ interface{}, _ string, argAgentID string) error {
			require.Equal(t, testParameters.AgentID, argAgentID, "Agent ID should match")
			_, wsErr := tse.server.WorkspaceReportStatus(testParameters.Ctx, &remoteagentsproto.WorkspaceReportStatusRequest{
				RemoteAgentId: argAgentID,
				Status:        remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE,
			})
			require.NoError(t, wsErr, "Failed to report workspace status")
			return nil
		}).
		Times(1)

	// Create a context with metadata
	ctx := requestcontext.NewIncomingContext(testParameters.Ctx, testParameters.RequestCtx)

	// Call the ResumeAgent method
	req := &remoteagentsproto.ResumeAgentRequest{
		RemoteAgentId: testParameters.AgentID,
	}
	resp, err := tse.server.ResumeAgent(ctx, req)

	// Verify the response
	require.NoError(t, err)
	require.NotNil(t, resp)

	// Verify that the agent status was updated in BigTable
	updatedAgentEntity, err := readRemoteAgentEntity(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.AgentID)
	require.NoError(t, err, "Failed to read updated agent")
	require.Equal(t, remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE, updatedAgentEntity.Status.Status, "Agent status should be IDLE")
}

func TestResumeAgent_NotPaused(t *testing.T) {
	// First, create an agent in BigTable
	testParameters := setupTestParameters()
	agentConfig := createTestAgentConfig()
	err := writeRemoteAgentAtCreation(
		context.Background(),
		testParameters.RequestCtx,
		realBigtableProxyClient,
		testParameters.TenantID,
		testParameters.UserID,
		testParameters.AgentID,
		agentConfig,
		"test-namespace",
	)
	require.NoError(t, err, "Failed to write agent config to BigTable")

	// Set up the server with mocks
	tse := setupTestServerWithEmulator(t)

	// Set the agent status to RUNNING (not PAUSED)
	agentEntity, err := readRemoteAgentEntity(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.AgentID)
	require.NoError(t, err, "Failed to read agent entity")
	agentEntity.Status.WorkspaceStatus = remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING
	err = agentEntity.AtomicSave(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID)
	require.NoError(t, err, "Failed to update agent status to RUNNING")

	// Create a context with metadata
	ctx := requestcontext.NewIncomingContext(testParameters.Ctx, testParameters.RequestCtx)

	// Call the ResumeAgent method
	req := &remoteagentsproto.ResumeAgentRequest{
		RemoteAgentId: testParameters.AgentID,
	}
	resp, err := tse.server.ResumeAgent(ctx, req)

	// Verify the error response is nil
	require.NoError(t, err)
	require.NotNil(t, resp)
}

func TestResumeAgent_Resuming(t *testing.T) {
	// First, create an agent in BigTable
	testParameters := setupTestParameters()
	agentConfig := createTestAgentConfig()
	err := writeRemoteAgentAtCreation(
		context.Background(),
		testParameters.RequestCtx,
		realBigtableProxyClient,
		testParameters.TenantID,
		testParameters.UserID,
		testParameters.AgentID,
		agentConfig,
		"test-namespace",
	)
	require.NoError(t, err, "Failed to write agent config to BigTable")

	// Set up the server with mocks
	tse := setupTestServerWithEmulator(t)

	// Create a mock workspace, but ReportStatus is never called yet
	tse.mockRWC.EXPECT().
		StopWorkspace(gomock.Any(), testParameters.UserID, testParameters.AgentID).
		Return(nil).
		Times(1)
	tse.mockRWC.EXPECT().
		ResumeWorkspace(gomock.Any(), testParameters.UserID, testParameters.AgentID).
		Return(nil).
		Times(1)

	// Mark the agent as idle and pause the workspace
	ctx := requestcontext.NewIncomingContext(testParameters.Ctx, testParameters.RequestCtx)
	statusReq := &remoteagentsproto.WorkspaceReportStatusRequest{
		RemoteAgentId: testParameters.AgentID,
		Status:        remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE,
	}
	_, err = tse.server.WorkspaceReportStatus(ctx, statusReq)
	require.NoError(t, err)
	pauseReq := &remoteagentsproto.PauseAgentRequest{
		RemoteAgentId: testParameters.AgentID,
	}
	_, err = tse.server.PauseAgent(ctx, pauseReq)
	require.NoError(t, err)

	// Call the ResumeAgent method, since the health checks fail this should
	// fail and the state should stay in resuming
	req := &remoteagentsproto.ResumeAgentRequest{
		RemoteAgentId: testParameters.AgentID,
	}
	resp, err := tse.server.ResumeAgent(ctx, req)
	require.Error(t, err)

	// Verify that the workspace status is still RESUMING
	updatedAgentEntity, err := readRemoteAgentEntity(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.AgentID)
	require.NoError(t, err, "Failed to read updated agent entity")
	require.Equal(t, remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RESUMING, updatedAgentEntity.Status.WorkspaceStatus, "Agent workspace status should be RESUMING")

	// Now the agent reports its status
	_, err = tse.server.WorkspaceReportStatus(testParameters.Ctx, &remoteagentsproto.WorkspaceReportStatusRequest{
		RemoteAgentId: testParameters.AgentID,
		Status:        remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE,
	})
	require.NoError(t, err, "Failed to report workspace status")

	// Now call the ResumeAgent method again, this time it should succeed and move the workspace status to running
	resp, err = tse.server.ResumeAgent(ctx, req)
	require.NoError(t, err)
	require.NotNil(t, resp)

	updatedAgentEntity, err = readRemoteAgentEntity(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.AgentID)
	require.NoError(t, err, "Failed to read updated agent entity")
	require.Equal(t, remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING, updatedAgentEntity.Status.WorkspaceStatus, "Agent workspace status should be RUNNING")
}

func TestResumeAgent_ResumeWorkspaceError(t *testing.T) {
	// First, create an agent in BigTable
	testParameters := setupTestParameters()
	agentConfig := createTestAgentConfig()
	err := writeRemoteAgentAtCreation(
		context.Background(),
		testParameters.RequestCtx,
		realBigtableProxyClient,
		testParameters.TenantID,
		testParameters.UserID,
		testParameters.AgentID,
		agentConfig,
		"test-namespace",
	)
	require.NoError(t, err, "Failed to write agent config to BigTable")

	// Set up the server with mocks
	tse := setupTestServerWithEmulator(t)

	// Set the agent status to PAUSED, ready for resume (following the correct state transition: RUNNING -> PAUSING -> PAUSED)
	agentEntity, err := readRemoteAgentEntity(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.AgentID)
	require.NoError(t, err, "Failed to read agent entity for initial setup")

	// First transition from RUNNING to PAUSING (also set agent status to IDLE to pass validation)
	agentEntity.Status.WorkspaceStatus = remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSING
	agentEntity.Status.Status = remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE
	err = agentEntity.AtomicSave(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID)
	require.NoError(t, err, "Failed to update agent status to PAUSING")
	agentEntity, err = readRemoteAgentEntity(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.AgentID)
	require.NoError(t, err, "Failed to read agent entity after PAUSING")
	agentEntity.Status.WorkspaceStatus = remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSED
	err = agentEntity.AtomicSave(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID)
	require.NoError(t, err, "Failed to update agent status to PAUSED")

	// Expect ResumeWorkspace to be called and return an error
	resumeErr := errors.New("mock failed to resume workspace")
	tse.mockRWC.EXPECT().
		ResumeWorkspace(gomock.Any(), testParameters.UserID, testParameters.AgentID).
		Return(resumeErr).
		AnyTimes()

	// Create a context with metadata
	ctx := requestcontext.NewIncomingContext(testParameters.Ctx, testParameters.RequestCtx)

	// Call the ResumeAgent method
	req := &remoteagentsproto.ResumeAgentRequest{
		RemoteAgentId: testParameters.AgentID,
	}
	resp, err := tse.server.ResumeAgent(ctx, req)

	// Verify the error response
	require.Error(t, err, "ResumeAgent should return an error when ResumeWorkspace fails")
	require.Nil(t, resp, "Response should be nil on error")
	require.True(t, errors.Is(err, resumeErr), "The error returned by ResumeAgent should be the error from ResumeWorkspace")

	// Verify that the agent status in BigTable is RESUMING (as revert is not implemented yet)
	updatedAgentEntity, readAfterErr := readRemoteAgentEntity(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.AgentID)
	require.NoError(t, readAfterErr, "Failed to read agent entity after ResumeAgent call")
	require.Equal(t, remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RESUMING, updatedAgentEntity.Status.WorkspaceStatus, "Agent workspace status should be RESUMING after failed ResumeWorkspace")
}

func TestResumeAgent_AgentNotWorking(t *testing.T) {
	// First, create an agent in BigTable
	testParameters := setupTestParameters()
	agentConfig := createTestAgentConfig()
	err := writeRemoteAgentAtCreation(
		context.Background(),
		testParameters.RequestCtx,
		realBigtableProxyClient,
		testParameters.TenantID,
		testParameters.UserID,
		testParameters.AgentID,
		agentConfig,
		"test-namespace",
	)
	require.NoError(t, err, "Failed to write agent config to BigTable")

	// Set the agent status to PAUSED so it can be resumed (following the correct state transition: RUNNING -> PAUSING -> PAUSED)
	agentEntity, err := readRemoteAgentEntity(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.AgentID)
	require.NoError(t, err, "Failed to read agent entity")

	// First transition from RUNNING to PAUSING (also set agent status to IDLE to pass validation)
	agentEntity.Status.WorkspaceStatus = remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSING
	agentEntity.Status.Status = remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE
	err = agentEntity.AtomicSave(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID)
	require.NoError(t, err, "Failed to update agent status to PAUSING")

	// Then transition from PAUSING to PAUSED
	agentEntity, err = readRemoteAgentEntity(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.AgentID)
	require.NoError(t, err, "Failed to read agent entity after PAUSING")
	agentEntity.Status.WorkspaceStatus = remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSED
	err = agentEntity.AtomicSave(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID)
	require.NoError(t, err, "Failed to update agent status to PAUSED")

	// Set up the server with mocks
	tse := setupTestServerWithEmulator(t)

	// Mock ResumeWorkspace to succeed, but ReportStatus() is never called
	tse.mockRWC.EXPECT().
		ResumeWorkspace(gomock.Any(), testParameters.UserID, testParameters.AgentID).
		Return(nil).
		AnyTimes()

	// Create a context with metadata
	ctx := requestcontext.NewIncomingContext(testParameters.Ctx, testParameters.RequestCtx)

	// Create a request to resume the agent
	req := &remoteagentsproto.ResumeAgentRequest{
		RemoteAgentId: testParameters.AgentID,
	}

	// Call the ResumeAgent method
	resp, err := tse.server.ResumeAgent(ctx, req)

	// Verify that the request failed with the expected error
	require.Error(t, err, "Expected error when agent health check fails")
	require.Nil(t, resp, "Response should be nil when agent health check fails")
}

func TestPauseAgent_HappyPath(t *testing.T) {
	// First, create an agent in BigTable
	testParameters := setupTestParameters()
	agentConfig := createTestAgentConfig()
	err := writeRemoteAgentAtCreation(
		context.Background(),
		testParameters.RequestCtx,
		realBigtableProxyClient,
		testParameters.TenantID,
		testParameters.UserID,
		testParameters.AgentID,
		agentConfig,
		"test-namespace",
	)
	require.NoError(t, err, "Failed to write agent config to BigTable")

	// Set up the server with mocks
	tse := setupTestServerWithEmulator(t)

	// Set the agent status to IDLE
	agentEntity, err := readRemoteAgentEntity(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.AgentID)
	require.NoError(t, err, "Failed to read agent entity")
	agentEntity.Status.Status = remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE
	agentEntity.Status.WorkspaceStatus = remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING
	err = agentEntity.AtomicSave(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID)
	require.NoError(t, err, "Failed to update agent status to IDLE")

	// Expect StopWorkspace to be called with the correct parameters
	tse.mockRWC.EXPECT().
		StopWorkspace(gomock.Any(), testParameters.UserID, testParameters.AgentID).
		Return(nil).
		Times(1)

	// Create a context with metadata
	ctx := requestcontext.NewIncomingContext(testParameters.Ctx, testParameters.RequestCtx)

	// Call the PauseAgent method
	req := &remoteagentsproto.PauseAgentRequest{
		RemoteAgentId: testParameters.AgentID,
	}
	resp, err := tse.server.PauseAgent(ctx, req)

	// Verify the response
	require.NoError(t, err)
	require.NotNil(t, resp)

	// Verify that the agent status was updated in BigTable
	updatedAgentEntity, err := readRemoteAgentEntity(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.AgentID)
	require.NoError(t, err, "Failed to read updated agent entity")
	require.Equal(t, remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSED, updatedAgentEntity.Status.WorkspaceStatus, "Agent workspace status should be PAUSED")
}

func TestPauseAgent_NotIdle(t *testing.T) {
	// First, create an agent in BigTable
	testParameters := setupTestParameters()
	agentConfig := createTestAgentConfig()
	err := writeRemoteAgentAtCreation(
		context.Background(),
		testParameters.RequestCtx,
		realBigtableProxyClient,
		testParameters.TenantID,
		testParameters.UserID,
		testParameters.AgentID,
		agentConfig,
		"test-namespace",
	)
	require.NoError(t, err, "Failed to write agent config to BigTable")

	// Set up the server with mocks
	tse := setupTestServerWithEmulator(t)

	// Set the agent status to RUNNING (not IDLE)
	agentEntity, err := readRemoteAgentEntity(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.AgentID)
	require.NoError(t, err, "Failed to read agent entity")
	agentEntity.Status.Status = remoteagentsproto.AgentStatus_AGENT_STATUS_RUNNING
	err = agentEntity.AtomicSave(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID)
	require.NoError(t, err, "Failed to update agent status to RUNNING")

	// Create a context with metadata
	ctx := requestcontext.NewIncomingContext(testParameters.Ctx, testParameters.RequestCtx)

	// Call the PauseAgent method
	req := &remoteagentsproto.PauseAgentRequest{
		RemoteAgentId: testParameters.AgentID,
	}
	resp, err := tse.server.PauseAgent(ctx, req)

	// Verify the error response
	require.Error(t, err)
	require.Nil(t, resp)
	require.Equal(t, codes.FailedPrecondition, status.Code(err), "Should return FailedPrecondition error")
	require.Contains(t, err.Error(), "not idle", "Error message should indicate agent is not idle")
}

func TestPauseAgent_StopWorkspaceError(t *testing.T) {
	// First, create an agent in BigTable
	testParameters := setupTestParameters()
	agentConfig := createTestAgentConfig()
	err := writeRemoteAgentAtCreation(
		context.Background(),
		testParameters.RequestCtx,
		realBigtableProxyClient,
		testParameters.TenantID,
		testParameters.UserID,
		testParameters.AgentID,
		agentConfig,
		"test-namespace",
	)
	require.NoError(t, err, "Failed to write agent config to BigTable")

	// Set up the server with mocks
	tse := setupTestServerWithEmulator(t)

	// Set the agent status to IDLE
	agentEntity, err := readRemoteAgentEntity(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.AgentID)
	require.NoError(t, err, "Failed to read agent entity")
	agentEntity.Status.Status = remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE
	agentEntity.Status.WorkspaceStatus = remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING
	err = agentEntity.AtomicSave(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID)
	require.NoError(t, err, "Failed to update agent status to IDLE")

	// Expect StopWorkspace to be called and return an error
	stopErr := errors.New("failed to stop workspace")
	tse.mockRWC.EXPECT().
		StopWorkspace(gomock.Any(), testParameters.UserID, testParameters.AgentID).
		Return(stopErr).
		Times(1)

	// Create a context with metadata
	ctx := requestcontext.NewIncomingContext(testParameters.Ctx, testParameters.RequestCtx)

	// Call the PauseAgent method
	req := &remoteagentsproto.PauseAgentRequest{
		RemoteAgentId: testParameters.AgentID,
	}
	resp, err := tse.server.PauseAgent(ctx, req)

	// Verify the error response
	require.Error(t, err)
	require.Nil(t, resp)
	require.Contains(t, err.Error(), "failed to stop workspace", "Error message should indicate failure to stop workspace")
	require.Equal(t, stopErr, err, "Error should be the same as the one returned by StopWorkspace")

	// Verify that the agent status remained PAUSING
	updatedAgentEntity, err := readRemoteAgentEntity(context.Background(), testParameters.RequestCtx, realBigtableProxyClient, testParameters.TenantID, testParameters.AgentID)
	require.NoError(t, err, "Failed to read updated agent")
	require.Equal(t, remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSING, updatedAgentEntity.Status.WorkspaceStatus, "Agent workspace status should be reverted to RUNNING")
}

func TestToPublicAgent(t *testing.T) {
	createdTime := time.Date(2023, 5, 15, 10, 0, 0, 0, time.UTC)
	lastUserUpdateTime := time.Date(2023, 5, 16, 14, 30, 0, 0, time.UTC)
	lastAgentUpdateTime := time.Date(2023, 5, 16, 15, 45, 0, 0, time.UTC)
	agentEntity := &AgentEntity{
		AgentID: "test-agent-123",
		Config: &entitiesproto.AgentConfig{
			CreatedAt: timestamppb.New(createdTime),
		},
		Status: &entitiesproto.AgentStatus{
			Status:                    remoteagentsproto.AgentStatus_AGENT_STATUS_RUNNING,
			WorkspaceStatus:           remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING,
			HasUpdates:                true,
			LastUserUpdateReceivedAt:  timestamppb.New(lastUserUpdateTime),
			LastAgentUpdateReceivedAt: timestamppb.New(lastAgentUpdateTime),
		},
	}

	publicAgent := agentEntity.ToPublicAgent(30) // Pass a test value for expirationDays
	require.NotNil(t, publicAgent, "Public agent should not be nil")
	require.Equal(t, "test-agent-123", publicAgent.RemoteAgentId, "Agent ID should match")
	require.Equal(t, remoteagentsproto.AgentStatus_AGENT_STATUS_RUNNING, publicAgent.Status, "Status should match")
	require.Equal(t, remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING, publicAgent.WorkspaceStatus, "Workspace status should match")
	require.Equal(t, agentEntity.Config.CreatedAt, publicAgent.CreatedAt, "CreatedAt should match config timestamp")
	require.Equal(t, agentEntity.Status.LastAgentUpdateReceivedAt, publicAgent.UpdatedAt, "UpdatedAt should use the latest timestamp (agent update)")
	require.True(t, *publicAgent.HasUpdates, "HasUpdates should be true")
	expectedExpiration := lastAgentUpdateTime.Add(30 * 24 * time.Hour)
	require.Equal(t, expectedExpiration.Unix(), publicAgent.ExpiresAt.Seconds, "ExpiresAt should be calculated correctly")
}

// mockAgentHistoryStreamServer implements remoteagentsproto.RemoteAgents_AgentHistoryStreamServer for testing
type mockAgentHistoryStreamServer struct {
	grpc.ServerStream
	ctx       context.Context
	responses []*remoteagentsproto.AgentHistoryStreamResponse
}

func (m *mockAgentHistoryStreamServer) Context() context.Context {
	return m.ctx
}

func (m *mockAgentHistoryStreamServer) Send(resp *remoteagentsproto.AgentHistoryStreamResponse) error {
	m.responses = append(m.responses, resp)
	return nil
}

func TestGetDelitionTTLInDays(t *testing.T) {
	// Arrange
	mockFeatureFlag := featureflags.NewLocalFeatureFlagHandler()

	tests := []struct {
		name           string
		flagValue      int
		expectedResult uint32
	}{
		{
			name:           "flag set to 30 returns 30",
			flagValue:      30,
			expectedResult: 30,
		},
		{
			name:           "flag set to 10 returns 10",
			flagValue:      10,
			expectedResult: 10,
		},
		{
			name:           "flag set to 1 returns 10 (minimum enforced)",
			flagValue:      1,
			expectedResult: 10,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Arrange
			mockFeatureFlag.Set("remote_agents_auto_delete_ttl_days", tt.flagValue)

			// Act
			result := getAgentDeletionTTLDays(mockFeatureFlag)

			// Assert
			require.Equal(t, tt.expectedResult, result, "TTL should match expected value")
		})
	}
}
